/**
 * 模型服务
 */
import { Injectable } from '@nestjs/common';
import { AssetsService } from '../assets/assets.service';
import type {  AssetType  } from '../assets/entities/asset.entity';

@Injectable()
export class ModelsService {
  constructor(private readonly assetsService: AssetsService) {}

  /**
   * 查找所有模型
   */
  async findAll(userId: string, projectId?: string, tags?: string[]) {
    return this.assetsService.findAll(userId, projectId, AssetType.MODEL, tags);
  }

  /**
   * 搜索模型
   */
  async search(query: string, userId: string) {
    return this.assetsService.search(query, userId, AssetType.MODEL);
  }
}
