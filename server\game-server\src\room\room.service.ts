import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';
import type { 
  GameRoom,
  Player,
  RoomStatus,
  PlayerStatus,
  RoomType,
  CreateRoomParams,
  JoinRoomParams,
  RoomSearchParams,
  RoomConfig,
  RoomEvent
 } from './room.interface';

@Injectable()
export class RoomService {
  private readonly logger = new Logger(RoomService.name);
  private readonly rooms = new Map<string, GameRoom>();
  private readonly playerRoomMap = new Map<string, string>(); // playerId -> roomId

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 创建游戏房间
   */
  async createRoom(params: CreateRoomParams): Promise<GameRoom> {
    const roomId = uuidv4();
    const now = new Date();

    // 默认房间配置
    const defaultConfig: RoomConfig = {
      maxPlayers: 8,
      minPlayers: 2,
      gameMode: 'default',
      gameType: 'multiplayer',
      isPrivate: false,
      allowSpectators: true,
      maxSpectators: 10,
      autoStart: false,
      autoStartDelay: 10,
      gameSettings: {},
      ...params.config
    };

    const room: GameRoom = {
      id: roomId,
      name: params.name,
      type: params.type,
      status: RoomStatus.WAITING,
      config: defaultConfig,
      players: new Map(),
      spectators: new Map(),
      hostId: params.hostId,
      createdAt: now,
      updatedAt: now,
      gameState: {},
      metadata: params.metadata || {}
    };

    this.rooms.set(roomId, room);

    // 触发房间创建事件
    this.emitRoomEvent('room.created', roomId, undefined, { room });

    this.logger.log(`房间已创建: ${roomId} by ${params.hostId}`);
    return room;
  }

  /**
   * 加入房间
   */
  async joinRoom(params: JoinRoomParams): Promise<GameRoom> {
    const room = this.rooms.get(params.roomId);
    if (!room) {
      throw new NotFoundException(`房间不存在: ${params.roomId}`);
    }

    // 检查房间状态
    if (room.status === RoomStatus.FINISHED || room.status === RoomStatus.CLOSED) {
      throw new BadRequestException('房间已结束或关闭');
    }

    // 检查密码
    if (room.config.isPrivate && room.config.password && room.config.password !== params.password) {
      throw new BadRequestException('房间密码错误');
    }

    // 检查玩家是否已在其他房间
    const existingRoomId = this.playerRoomMap.get(params.playerId);
    if (existingRoomId && existingRoomId !== params.roomId) {
      await this.leaveRoom(params.playerId);
    }

    const player: Player = {
      id: params.playerId,
      userId: params.playerId,
      username: params.username,
      status: PlayerStatus.CONNECTED,
      joinedAt: new Date(),
      lastActiveAt: new Date(),
      isHost: room.players.size === 0 && !params.asSpectator,
      metadata: params.metadata || {}
    };

    if (params.asSpectator) {
      // 加入观众
      if (room.spectators.size >= room.config.maxSpectators) {
        throw new BadRequestException('观众席已满');
      }
      room.spectators.set(params.playerId, player);
    } else {
      // 加入玩家
      if (room.players.size >= room.config.maxPlayers) {
        throw new BadRequestException('房间已满');
      }
      room.players.set(params.playerId, player);
    }

    this.playerRoomMap.set(params.playerId, params.roomId);
    room.updatedAt = new Date();

    // 检查是否可以自动开始游戏
    if (room.config.autoStart && room.players.size >= room.config.minPlayers) {
      setTimeout(() => {
        this.startGame(params.roomId);
      }, room.config.autoStartDelay * 1000);
    }

    // 触发玩家加入事件
    this.emitRoomEvent('player.joined', params.roomId, params.playerId, { player });

    this.logger.log(`玩家 ${params.username} 加入房间 ${params.roomId}`);
    return room;
  }

  /**
   * 离开房间
   */
  async leaveRoom(playerId: string): Promise<void> {
    const roomId = this.playerRoomMap.get(playerId);
    if (!roomId) {
      return; // 玩家不在任何房间中
    }

    const room = this.rooms.get(roomId);
    if (!room) {
      this.playerRoomMap.delete(playerId);
      return;
    }

    // 从房间中移除玩家
    const wasPlayer = room.players.has(playerId);
    const wasSpectator = room.spectators.has(playerId);
    
    room.players.delete(playerId);
    room.spectators.delete(playerId);
    this.playerRoomMap.delete(playerId);

    // 如果是房主离开，转移房主权限
    if (room.hostId === playerId && room.players.size > 0) {
      const newHost = room.players.values().next().value;
      room.hostId = newHost.id;
      newHost.isHost = true;
      
      this.emitRoomEvent('host.changed', roomId, newHost.id, { newHostId: newHost.id });
    }

    // 如果房间为空，关闭房间
    if (room.players.size === 0 && room.spectators.size === 0) {
      await this.closeRoom(roomId);
    } else {
      room.updatedAt = new Date();
    }

    // 触发玩家离开事件
    this.emitRoomEvent('player.left', roomId, playerId, { wasPlayer, wasSpectator });

    this.logger.log(`玩家 ${playerId} 离开房间 ${roomId}`);
  }

  /**
   * 开始游戏
   */
  async startGame(roomId: string): Promise<void> {
    const room = this.rooms.get(roomId);
    if (!room) {
      throw new NotFoundException(`房间不存在: ${roomId}`);
    }

    if (room.status !== RoomStatus.WAITING) {
      throw new BadRequestException('房间状态不允许开始游戏');
    }

    if (room.players.size < room.config.minPlayers) {
      throw new BadRequestException('玩家数量不足');
    }

    room.status = RoomStatus.STARTING;
    room.startedAt = new Date();
    room.updatedAt = new Date();

    // 将所有玩家状态设为游戏中
    for (const player of room.players.values()) {
      player.status = PlayerStatus.PLAYING;
    }

    // 触发游戏开始事件
    this.emitRoomEvent('game.starting', roomId, undefined, { room });

    // 延迟设置为游戏中状态
    setTimeout(() => {
      room.status = RoomStatus.PLAYING;
      this.emitRoomEvent('game.started', roomId, undefined, { room });
    }, 3000); // 3秒准备时间

    this.logger.log(`房间 ${roomId} 开始游戏`);
  }

  /**
   * 关闭房间
   */
  async closeRoom(roomId: string): Promise<void> {
    const room = this.rooms.get(roomId);
    if (!room) {
      return;
    }

    // 移除所有玩家的房间映射
    for (const playerId of room.players.keys()) {
      this.playerRoomMap.delete(playerId);
    }
    for (const playerId of room.spectators.keys()) {
      this.playerRoomMap.delete(playerId);
    }

    room.status = RoomStatus.CLOSED;
    room.finishedAt = new Date();
    room.updatedAt = new Date();

    // 触发房间关闭事件
    this.emitRoomEvent('room.closed', roomId, undefined, { room });

    // 延迟删除房间数据
    setTimeout(() => {
      this.rooms.delete(roomId);
    }, 60000); // 1分钟后删除

    this.logger.log(`房间 ${roomId} 已关闭`);
  }

  /**
   * 获取房间信息
   */
  getRoom(roomId: string): GameRoom | undefined {
    return this.rooms.get(roomId);
  }

  /**
   * 搜索房间
   */
  searchRooms(params: RoomSearchParams): GameRoom[] {
    const rooms = Array.from(this.rooms.values());
    let filteredRooms = rooms;

    // 应用过滤条件
    if (params.gameType) {
      filteredRooms = filteredRooms.filter(room => room.config.gameType === params.gameType);
    }
    if (params.gameMode) {
      filteredRooms = filteredRooms.filter(room => room.config.gameMode === params.gameMode);
    }
    if (params.status) {
      filteredRooms = filteredRooms.filter(room => room.status === params.status);
    }
    if (params.type) {
      filteredRooms = filteredRooms.filter(room => room.type === params.type);
    }
    if (params.hasSlots) {
      filteredRooms = filteredRooms.filter(room => room.players.size < room.config.maxPlayers);
    }

    // 分页
    const offset = params.offset || 0;
    const limit = params.limit || 20;
    
    return filteredRooms.slice(offset, offset + limit);
  }

  /**
   * 获取玩家所在房间
   */
  getPlayerRoom(playerId: string): GameRoom | undefined {
    const roomId = this.playerRoomMap.get(playerId);
    return roomId ? this.rooms.get(roomId) : undefined;
  }

  /**
   * 发送房间事件
   */
  private emitRoomEvent(type: string, roomId: string, playerId?: string, data?: any): void {
    const event: RoomEvent = {
      type,
      roomId,
      playerId,
      data,
      timestamp: new Date()
    };

    this.eventEmitter.emit(type, event);
  }

  /**
   * 获取房间统计信息
   */
  getRoomStats() {
    const totalRooms = this.rooms.size;
    const activeRooms = Array.from(this.rooms.values()).filter(
      room => room.status === RoomStatus.WAITING || room.status === RoomStatus.PLAYING
    ).length;
    const totalPlayers = Array.from(this.rooms.values()).reduce(
      (sum, room) => sum + room.players.size, 0
    );

    return {
      totalRooms,
      activeRooms,
      totalPlayers,
      averagePlayersPerRoom: totalRooms > 0 ? totalPlayers / totalRooms : 0
    };
  }
}
