{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": true, "paths": {"@/*": ["src/*"], "@emotion/*": ["src/emotion/*"], "@emotion-data/*": ["src/emotion-data/*"], "@entities/*": ["src/entities/*"], "@dto/*": ["src/dto/*"], "@config/*": ["src/config/*"], "@shared/*": ["../shared/*"], "@engine/*": ["../../engine/*"]}, "strict": true, "strictFunctionTypes": true, "noImplicitReturns": true}, "include": ["src/**/*", "../shared/**/*"], "exclude": ["node_modules", "dist", "test", "**/*.spec.ts"], "extends": "../tsconfig.base.json"}