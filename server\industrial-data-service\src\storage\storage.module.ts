import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { StorageService } from './storage.service';
import { StorageController } from './storage.controller';
import { TimeSeriesData } from './entities/time-series-data.entity';
import { DataArchive } from './entities/data-archive.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([TimeSeriesData, DataArchive])
  ],
  controllers: [StorageController],
  providers: [StorageService],
  exports: [StorageService]
})
export class StorageModule {}
