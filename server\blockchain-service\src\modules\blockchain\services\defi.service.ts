import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { EventEmitter2 } from '@nestjs/event-emitter';
import Web3 from 'web3';
import { BlockchainAsset } from '../../../entities/blockchain-asset.entity';
import { Transaction } from '../../../entities/transaction.entity';
import { SmartContractService } from './smart-contract.service';

/**
 * 流动性池配置接口
 */
interface LiquidityPoolConfig {
  tokenA: string;
  tokenB: string;
  feeRate: number; // 基点，如30表示0.3%
  initialPriceRatio?: string;
}

/**
 * 流动性提供配置接口
 */
interface LiquidityProvisionConfig {
  poolId: string;
  tokenAAmount: string;
  tokenBAmount: string;
  providerAddress: string;
  slippageTolerance: number; // 滑点容忍度，如5表示5%
}

/**
 * 交换配置接口
 */
interface SwapConfig {
  poolId: string;
  tokenIn: string;
  tokenOut: string;
  amountIn: string;
  minAmountOut: string;
  traderAddress: string;
  deadline: number; // Unix时间戳
}

/**
 * 借贷配置接口
 */
interface LendingConfig {
  assetId: string;
  amount: string;
  interestRate: number;
  duration: number; // 天数
  collateralAssetId?: string;
  collateralAmount?: string;
  lenderAddress: string;
}

/**
 * 借款配置接口
 */
interface BorrowingConfig {
  lendingOfferId: string;
  borrowerAddress: string;
  collateralAssetId: string;
  collateralAmount: string;
}

/**
 * 收益农场配置接口
 */
interface YieldFarmConfig {
  poolId: string;
  rewardTokenId: string;
  rewardRate: string; // 每秒奖励数量
  startTime: number;
  endTime: number;
  totalRewards: string;
}

/**
 * DeFi服务
 */
@Injectable()
export class DeFiService {
  private readonly logger = new Logger(DeFiService.name);
  private web3: Web3;
  
  // 流动性池缓存
  private liquidityPools: Map<string, any> = new Map();
  
  // 借贷市场缓存
  private lendingOffers: Map<string, any> = new Map();
  
  // 收益农场缓存
  private yieldFarms: Map<string, any> = new Map();

  constructor(
    @InjectRepository(BlockchainAsset)
    private assetRepository: Repository<BlockchainAsset>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    private configService: ConfigService,
    private smartContractService: SmartContractService,
    @InjectQueue('defi') private defiQueue: Queue,
    private eventEmitter: EventEmitter2
  ) {
    this.initializeWeb3();
    this.initializeDeFi();
  }

  /**
   * 创建流动性池
   */
  async createLiquidityPool(
    config: LiquidityPoolConfig,
    creatorAddress: string,
    privateKey: string
  ): Promise<string> {
    try {
      this.logger.log(`创建流动性池: ${config.tokenA}/${config.tokenB}`);

      // 验证代币存在
      const tokenA = await this.assetRepository.findOne({ where: { id: config.tokenA } });
      const tokenB = await this.assetRepository.findOne({ where: { id: config.tokenB } });

      if (!tokenA || !tokenB) {
        throw new Error('代币不存在');
      }

      // 调用AMM合约创建池
      const ammContract = this.configService.get('blockchain.contracts.amm');
      const result = await this.smartContractService.callContractMethod({
        contractAddress: ammContract,
        methodName: 'createPool',
        methodArgs: [tokenA.contractAddress, tokenB.contractAddress, config.feeRate],
        fromAddress: creatorAddress
      }, privateKey);

      // 生成池ID
      const poolId = `pool_${config.tokenA}_${config.tokenB}_${config.feeRate}`;

      // 缓存池信息
      const pool = {
        id: poolId,
        tokenA: config.tokenA,
        tokenB: config.tokenB,
        tokenAAddress: tokenA.contractAddress,
        tokenBAddress: tokenB.contractAddress,
        feeRate: config.feeRate,
        reserveA: '0',
        reserveB: '0',
        totalLiquidity: '0',
        creatorAddress,
        createdAt: new Date(),
        txHash: result.transactionHash
      };

      this.liquidityPools.set(poolId, pool);

      // 发送池创建事件
      this.eventEmitter.emit('pool.created', {
        poolId,
        tokenA: config.tokenA,
        tokenB: config.tokenB,
        creator: creatorAddress
      });

      this.logger.log(`流动性池创建成功: ${poolId}`);
      return poolId;

    } catch (error) {
      this.logger.error('创建流动性池失败', error);
      throw error;
    }
  }

  /**
   * 添加流动性
   */
  async addLiquidity(
    config: LiquidityProvisionConfig,
    privateKey: string
  ): Promise<string> {
    try {
      this.logger.log(`添加流动性: ${config.poolId}`);

      const pool = this.liquidityPools.get(config.poolId);
      if (!pool) {
        throw new Error(`流动性池不存在: ${config.poolId}`);
      }

      // 调用AMM合约添加流动性
      const ammContract = this.configService.get('blockchain.contracts.amm');
      const result = await this.smartContractService.callContractMethod({
        contractAddress: ammContract,
        methodName: 'addLiquidity',
        methodArgs: [
          pool.tokenAAddress,
          pool.tokenBAddress,
          config.tokenAAmount,
          config.tokenBAmount,
          this.calculateMinAmount(config.tokenAAmount, config.slippageTolerance),
          this.calculateMinAmount(config.tokenBAmount, config.slippageTolerance),
          config.providerAddress,
          Math.floor(Date.now() / 1000) + 1800 // 30分钟后过期
        ],
        fromAddress: config.providerAddress
      }, privateKey);

      // 更新池储备
      pool.reserveA = (BigInt(pool.reserveA) + BigInt(config.tokenAAmount)).toString();
      pool.reserveB = (BigInt(pool.reserveB) + BigInt(config.tokenBAmount)).toString();

      // 计算LP代币数量
      const liquidityTokens = this.calculateLiquidityTokens(
        config.tokenAAmount,
        config.tokenBAmount,
        pool.reserveA,
        pool.reserveB,
        pool.totalLiquidity
      );

      pool.totalLiquidity = (BigInt(pool.totalLiquidity) + BigInt(liquidityTokens)).toString();

      // 记录交易
      await this.recordDeFiTransaction({
        type: 'add_liquidity',
        poolId: config.poolId,
        userAddress: config.providerAddress,
        tokenAAmount: config.tokenAAmount,
        tokenBAmount: config.tokenBAmount,
        liquidityTokens,
        txHash: result.transactionHash
      });

      // 发送流动性添加事件
      this.eventEmitter.emit('liquidity.added', {
        poolId: config.poolId,
        provider: config.providerAddress,
        tokenAAmount: config.tokenAAmount,
        tokenBAmount: config.tokenBAmount,
        liquidityTokens
      });

      this.logger.log(`流动性添加成功: ${config.poolId}`);
      return result.transactionHash;

    } catch (error) {
      this.logger.error('添加流动性失败', error);
      throw error;
    }
  }

  /**
   * 移除流动性
   */
  async removeLiquidity(
    poolId: string,
    liquidityTokens: string,
    providerAddress: string,
    privateKey: string
  ): Promise<string> {
    try {
      this.logger.log(`移除流动性: ${poolId}`);

      const pool = this.liquidityPools.get(poolId);
      if (!pool) {
        throw new Error(`流动性池不存在: ${poolId}`);
      }

      // 调用AMM合约移除流动性
      const ammContract = this.configService.get('blockchain.contracts.amm');
      const result = await this.smartContractService.callContractMethod({
        contractAddress: ammContract,
        methodName: 'removeLiquidity',
        methodArgs: [
          pool.tokenAAddress,
          pool.tokenBAddress,
          liquidityTokens,
          '0', // 最小tokenA数量
          '0', // 最小tokenB数量
          providerAddress,
          Math.floor(Date.now() / 1000) + 1800
        ],
        fromAddress: providerAddress
      }, privateKey);

      // 计算返还的代币数量
      const { tokenAAmount, tokenBAmount } = this.calculateTokenAmountsFromLiquidity(
        liquidityTokens,
        pool.reserveA,
        pool.reserveB,
        pool.totalLiquidity
      );

      // 更新池储备
      pool.reserveA = (BigInt(pool.reserveA) - BigInt(tokenAAmount)).toString();
      pool.reserveB = (BigInt(pool.reserveB) - BigInt(tokenBAmount)).toString();
      pool.totalLiquidity = (BigInt(pool.totalLiquidity) - BigInt(liquidityTokens)).toString();

      // 记录交易
      await this.recordDeFiTransaction({
        type: 'remove_liquidity',
        poolId,
        userAddress: providerAddress,
        tokenAAmount,
        tokenBAmount,
        liquidityTokens,
        txHash: result.transactionHash
      });

      this.logger.log(`流动性移除成功: ${poolId}`);
      return result.transactionHash;

    } catch (error) {
      this.logger.error('移除流动性失败', error);
      throw error;
    }
  }

  /**
   * 代币交换
   */
  async swapTokens(config: SwapConfig, privateKey: string): Promise<string> {
    try {
      this.logger.log(`代币交换: ${config.tokenIn} -> ${config.tokenOut}`);

      const pool = this.liquidityPools.get(config.poolId);
      if (!pool) {
        throw new Error(`流动性池不存在: ${config.poolId}`);
      }

      // 计算输出数量
      const amountOut = this.calculateSwapOutput(
        config.amountIn,
        config.tokenIn === pool.tokenA ? pool.reserveA : pool.reserveB,
        config.tokenIn === pool.tokenA ? pool.reserveB : pool.reserveA,
        pool.feeRate
      );

      if (BigInt(amountOut) < BigInt(config.minAmountOut)) {
        throw new Error('滑点过大，交易失败');
      }

      // 调用AMM合约执行交换
      const ammContract = this.configService.get('blockchain.contracts.amm');
      const result = await this.smartContractService.callContractMethod({
        contractAddress: ammContract,
        methodName: 'swapExactTokensForTokens',
        methodArgs: [
          config.amountIn,
          config.minAmountOut,
          [config.tokenIn, config.tokenOut],
          config.traderAddress,
          config.deadline
        ],
        fromAddress: config.traderAddress
      }, privateKey);

      // 更新池储备
      if (config.tokenIn === pool.tokenA) {
        pool.reserveA = (BigInt(pool.reserveA) + BigInt(config.amountIn)).toString();
        pool.reserveB = (BigInt(pool.reserveB) - BigInt(amountOut)).toString();
      } else {
        pool.reserveB = (BigInt(pool.reserveB) + BigInt(config.amountIn)).toString();
        pool.reserveA = (BigInt(pool.reserveA) - BigInt(amountOut)).toString();
      }

      // 记录交易
      await this.recordDeFiTransaction({
        type: 'swap',
        poolId: config.poolId,
        userAddress: config.traderAddress,
        tokenIn: config.tokenIn,
        tokenOut: config.tokenOut,
        amountIn: config.amountIn,
        amountOut,
        txHash: result.transactionHash
      });

      // 发送交换事件
      this.eventEmitter.emit('tokens.swapped', {
        poolId: config.poolId,
        trader: config.traderAddress,
        tokenIn: config.tokenIn,
        tokenOut: config.tokenOut,
        amountIn: config.amountIn,
        amountOut
      });

      this.logger.log(`代币交换成功: ${config.amountIn} ${config.tokenIn} -> ${amountOut} ${config.tokenOut}`);
      return result.transactionHash;

    } catch (error) {
      this.logger.error('代币交换失败', error);
      throw error;
    }
  }

  /**
   * 创建借贷订单
   */
  async createLendingOffer(
    config: LendingConfig,
    privateKey: string
  ): Promise<string> {
    try {
      this.logger.log(`创建借贷订单: ${config.amount} ${config.assetId}`);

      // 调用借贷合约创建订单
      const lendingContract = this.configService.get('blockchain.contracts.lending');
      const result = await this.smartContractService.callContractMethod({
        contractAddress: lendingContract,
        methodName: 'createLendingOffer',
        methodArgs: [
          config.assetId,
          config.amount,
          config.interestRate,
          config.duration,
          config.collateralAssetId || '',
          config.collateralAmount || '0'
        ],
        fromAddress: config.lenderAddress
      }, privateKey);

      // 生成订单ID
      const offerId = `lending_${Date.now()}`;

      // 缓存订单信息
      const offer = {
        id: offerId,
        assetId: config.assetId,
        amount: config.amount,
        interestRate: config.interestRate,
        duration: config.duration,
        collateralAssetId: config.collateralAssetId,
        collateralAmount: config.collateralAmount,
        lenderAddress: config.lenderAddress,
        status: 'active',
        createdAt: new Date(),
        txHash: result.transactionHash
      };

      this.lendingOffers.set(offerId, offer);

      this.logger.log(`借贷订单创建成功: ${offerId}`);
      return offerId;

    } catch (error) {
      this.logger.error('创建借贷订单失败', error);
      throw error;
    }
  }

  /**
   * 借款
   */
  async borrowTokens(config: BorrowingConfig, privateKey: string): Promise<string> {
    try {
      this.logger.log(`借款: ${config.lendingOfferId}`);

      const offer = this.lendingOffers.get(config.lendingOfferId);
      if (!offer) {
        throw new Error(`借贷订单不存在: ${config.lendingOfferId}`);
      }

      if (offer.status !== 'active') {
        throw new Error(`借贷订单不可用: ${config.lendingOfferId}`);
      }

      // 调用借贷合约执行借款
      const lendingContract = this.configService.get('blockchain.contracts.lending');
      const result = await this.smartContractService.callContractMethod({
        contractAddress: lendingContract,
        methodName: 'borrow',
        methodArgs: [
          config.lendingOfferId,
          config.collateralAssetId,
          config.collateralAmount
        ],
        fromAddress: config.borrowerAddress
      }, privateKey);

      // 更新订单状态
      offer.status = 'borrowed';
      offer.borrowerAddress = config.borrowerAddress;
      offer.borrowedAt = new Date();

      this.logger.log(`借款成功: ${config.lendingOfferId}`);
      return result.transactionHash;

    } catch (error) {
      this.logger.error('借款失败', error);
      throw error;
    }
  }

  /**
   * 创建收益农场
   */
  async createYieldFarm(
    config: YieldFarmConfig,
    creatorAddress: string,
    privateKey: string
  ): Promise<string> {
    try {
      this.logger.log(`创建收益农场: ${config.poolId}`);

      // 调用收益农场合约
      const farmContract = this.configService.get('blockchain.contracts.farm');
      const result = await this.smartContractService.callContractMethod({
        contractAddress: farmContract,
        methodName: 'createFarm',
        methodArgs: [
          config.poolId,
          config.rewardTokenId,
          config.rewardRate,
          config.startTime,
          config.endTime,
          config.totalRewards
        ],
        fromAddress: creatorAddress
      }, privateKey);

      // 生成农场ID
      const farmId = `farm_${config.poolId}_${Date.now()}`;

      // 缓存农场信息
      const farm = {
        id: farmId,
        poolId: config.poolId,
        rewardTokenId: config.rewardTokenId,
        rewardRate: config.rewardRate,
        startTime: config.startTime,
        endTime: config.endTime,
        totalRewards: config.totalRewards,
        totalStaked: '0',
        creatorAddress,
        createdAt: new Date(),
        txHash: result.transactionHash
      };

      this.yieldFarms.set(farmId, farm);

      this.logger.log(`收益农场创建成功: ${farmId}`);
      return farmId;

    } catch (error) {
      this.logger.error('创建收益农场失败', error);
      throw error;
    }
  }

  /**
   * 获取流动性池信息
   */
  async getLiquidityPool(poolId: string): Promise<any> {
    return this.liquidityPools.get(poolId);
  }

  /**
   * 获取所有流动性池
   */
  async getLiquidityPools(): Promise<any[]> {
    return Array.from(this.liquidityPools.values());
  }

  /**
   * 获取借贷订单
   */
  async getLendingOffers(status?: string): Promise<any[]> {
    const offers = Array.from(this.lendingOffers.values());
    
    if (status) {
      return offers.filter(offer => offer.status === status);
    }
    
    return offers;
  }

  /**
   * 获取收益农场
   */
  async getYieldFarms(): Promise<any[]> {
    return Array.from(this.yieldFarms.values());
  }

  // 私有方法

  /**
   * 初始化Web3
   */
  private initializeWeb3(): void {
    const rpcUrl = this.configService.get('blockchain.ethereum.rpcUrl');
    this.web3 = new Web3(rpcUrl);
  }

  /**
   * 初始化DeFi
   */
  private async initializeDeFi(): Promise<void> {
    // 加载现有的流动性池、借贷订单等
    this.logger.log('DeFi服务初始化完成');
  }

  /**
   * 计算最小数量（考虑滑点）
   */
  private calculateMinAmount(amount: string, slippageTolerance: number): string {
    const slippageMultiplier = (100 - slippageTolerance) / 100;
    return (BigInt(amount) * BigInt(Math.floor(slippageMultiplier * 100)) / BigInt(100)).toString();
  }

  /**
   * 计算LP代币数量
   */
  private calculateLiquidityTokens(
    amountA: string,
    amountB: string,
    reserveA: string,
    reserveB: string,
    totalLiquidity: string
  ): string {
    if (totalLiquidity === '0') {
      // 首次添加流动性
      return (BigInt(amountA) * BigInt(amountB) / BigInt(1000)).toString(); // 简化计算
    } else {
      // 后续添加流动性
      const liquidityA = BigInt(amountA) * BigInt(totalLiquidity) / BigInt(reserveA);
      const liquidityB = BigInt(amountB) * BigInt(totalLiquidity) / BigInt(reserveB);
      return (liquidityA < liquidityB ? liquidityA : liquidityB).toString();
    }
  }

  /**
   * 从LP代币计算代币数量
   */
  private calculateTokenAmountsFromLiquidity(
    liquidityTokens: string,
    reserveA: string,
    reserveB: string,
    totalLiquidity: string
  ): { tokenAAmount: string; tokenBAmount: string } {
    const tokenAAmount = (BigInt(liquidityTokens) * BigInt(reserveA) / BigInt(totalLiquidity)).toString();
    const tokenBAmount = (BigInt(liquidityTokens) * BigInt(reserveB) / BigInt(totalLiquidity)).toString();
    
    return { tokenAAmount, tokenBAmount };
  }

  /**
   * 计算交换输出
   */
  private calculateSwapOutput(
    amountIn: string,
    reserveIn: string,
    reserveOut: string,
    feeRate: number
  ): string {
    const amountInWithFee = BigInt(amountIn) * BigInt(10000 - feeRate);
    const numerator = amountInWithFee * BigInt(reserveOut);
    const denominator = BigInt(reserveIn) * BigInt(10000) + amountInWithFee;
    
    return (numerator / denominator).toString();
  }

  /**
   * 记录DeFi交易
   */
  private async recordDeFiTransaction(txData: any): Promise<void> {
    try {
      const transaction = this.transactionRepository.create({
        hash: txData.txHash,
        type: txData.type,
        fromAddress: txData.userAddress,
        toAddress: txData.poolId || txData.contractAddress,
        status: 'success',
        data: txData,
        createdAt: new Date()
      });

      await this.transactionRepository.save(transaction);

    } catch (error) {
      this.logger.error('记录DeFi交易失败', error);
    }
  }
}
