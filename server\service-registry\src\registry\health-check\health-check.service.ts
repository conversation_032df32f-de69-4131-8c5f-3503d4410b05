/**
 * 健康检查服务
 */
import { Injectable, Logger } from '@nestjs/common';
import type {  
  HealthCheckStrategy, 
  HealthCheckConfig, 
  HealthCheckResult, 
  HealthCheckStatus,
  HealthCheckType
 } from './health-check.interface';
import { HttpHealthCheckStrategy } from './http-health-check.strategy';
import { TcpHealthCheckStrategy } from './tcp-health-check.strategy';
import { ScriptHealthCheckStrategy } from './script-health-check.strategy';

@Injectable()
export class HealthCheckService {
  private readonly logger = new Logger(HealthCheckService.name);
  private readonly strategies = new Map<HealthCheckType, HealthCheckStrategy>();
  private readonly healthCheckHistory = new Map<string, {
    successCount: number;
    failureCount: number;
    lastResult: HealthCheckResult;
    lastCheck: Date;
  }>();
  
  constructor(
    private readonly httpHealthCheckStrategy: HttpHealthCheckStrategy,
    private readonly tcpHealthCheckStrategy: TcpHealthCheckStrategy,
    private readonly scriptHealthCheckStrategy: ScriptHealthCheckStrategy,
  ) {
    // 注册健康检查策略
    this.strategies.set(HealthCheckType.HTTP, httpHealthCheckStrategy);
    this.strategies.set(HealthCheckType.TCP, tcpHealthCheckStrategy);
    this.strategies.set(HealthCheckType.SCRIPT, scriptHealthCheckStrategy);
  }
  
  /**
   * 执行健康检查
   * @param instanceId 实例ID
   * @param config 健康检查配置
   */
  async check(instanceId: string, config: HealthCheckConfig): Promise<HealthCheckResult> {
    if (!config.enabled) {
      return {
        status: HealthCheckStatus.UNKNOWN,
        details: '健康检查已禁用',
        timestamp: new Date(),
      };
    }
    
    try {
      // 获取对应的健康检查策略
      const strategy = this.strategies.get(config.type);
      
      if (!strategy) {
        this.logger.warn(`未找到健康检查策略: ${config.type}`);
        return {
          status: HealthCheckStatus.UNKNOWN,
          details: `未找到健康检查策略: ${config.type}`,
          timestamp: new Date(),
        };
      }
      
      // 执行健康检查
      const result = await strategy.check(config);
      
      // 更新健康检查历史
      this.updateHealthCheckHistory(instanceId, result);
      
      // 根据阈值判断最终状态
      const finalStatus = this.determineStatus(instanceId, config, result);
      
      // 如果最终状态与结果状态不同，则更新结果
      if (finalStatus !== result.status) {
        result.status = finalStatus;
        result.details = `${result.details} (根据阈值调整状态)`;
      }
      
      return result;
    } catch (error) {
      this.logger.error(`执行健康检查失败: ${error.message}`, error.stack);
      
      return {
        status: HealthCheckStatus.UNHEALTHY,
        details: `健康检查异常: ${error.message}`,
        timestamp: new Date(),
        metadata: {
          error: error.message,
        },
      };
    }
  }
  
  /**
   * 注册自定义健康检查策略
   * @param type 健康检查类型
   * @param strategy 健康检查策略
   */
  registerStrategy(type: HealthCheckType | string, strategy: HealthCheckStrategy): void {
    this.strategies.set(type as HealthCheckType, strategy);
    this.logger.log(`已注册健康检查策略: ${type}`);
  }
  
  /**
   * 获取健康检查历史
   * @param instanceId 实例ID
   */
  getHealthCheckHistory(instanceId: string): {
    successCount: number;
    failureCount: number;
    lastResult: HealthCheckResult;
    lastCheck: Date;
  } | null {
    return this.healthCheckHistory.get(instanceId) || null;
  }
  
  /**
   * 清除健康检查历史
   * @param instanceId 实例ID
   */
  clearHealthCheckHistory(instanceId: string): void {
    this.healthCheckHistory.delete(instanceId);
  }
  
  /**
   * 更新健康检查历史
   * @param instanceId 实例ID
   * @param result 健康检查结果
   */
  private updateHealthCheckHistory(instanceId: string, result: HealthCheckResult): void {
    const history = this.healthCheckHistory.get(instanceId) || {
      successCount: 0,
      failureCount: 0,
      lastResult: null,
      lastCheck: null,
    };
    
    // 更新成功/失败计数
    if (result.status === HealthCheckStatus.HEALTHY) {
      history.successCount++;
      history.failureCount = 0;
    } else {
      history.failureCount++;
      history.successCount = 0;
    }
    
    // 更新最后结果和检查时间
    history.lastResult = result;
    history.lastCheck = new Date();
    
    // 保存历史
    this.healthCheckHistory.set(instanceId, history);
  }
  
  /**
   * 根据阈值判断最终状态
   * @param instanceId 实例ID
   * @param config 健康检查配置
   * @param result 健康检查结果
   */
  private determineStatus(
    instanceId: string,
    config: HealthCheckConfig,
    result: HealthCheckResult,
  ): HealthCheckStatus {
    const history = this.healthCheckHistory.get(instanceId);
    
    if (!history) {
      return result.status;
    }
    
    // 如果连续成功次数达到健康阈值，则标记为健康
    if (history.successCount >= config.healthyThreshold) {
      return HealthCheckStatus.HEALTHY;
    }
    
    // 如果连续失败次数达到不健康阈值，则标记为不健康
    if (history.failureCount >= config.unhealthyThreshold) {
      return HealthCheckStatus.UNHEALTHY;
    }
    
    // 否则保持当前状态
    return result.status;
  }
}
