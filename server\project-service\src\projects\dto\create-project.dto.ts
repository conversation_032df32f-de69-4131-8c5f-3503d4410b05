/**
 * 创建项目DTO
 */
import { IsString, IsNotEmpty, IsOptional, IsEnum, IsBoolean, IsArray, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import type {  Type  } from 'class-transformer';
import { ProjectVisibility } from '../entities/project.entity';
import { CreateProjectSettingDto } from './create-project-setting.dto';

export class CreateProjectDto {
  @ApiProperty({ description: '项目名称', example: '我的3D项目' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '项目描述', required: false, example: '这是一个3D场景项目' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '缩略图URL', required: false })
  @IsString()
  @IsOptional()
  thumbnailUrl?: string;

  @ApiProperty({ description: '项目可见性', enum: ProjectVisibility, default: ProjectVisibility.PRIVATE })
  @IsEnum(ProjectVisibility)
  @IsOptional()
  visibility?: ProjectVisibility;

  @ApiProperty({ description: '是否为模板', required: false, default: false })
  @IsBoolean()
  @IsOptional()
  isTemplate?: boolean;

  @ApiProperty({ description: '项目设置', required: false, type: [CreateProjectSettingDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateProjectSettingDto)
  @IsOptional()
  settings?: CreateProjectSettingDto[];
}
