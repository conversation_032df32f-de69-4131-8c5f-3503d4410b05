import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsArray, IsObject, ValidateNested, Min, Max, IsEnum } from 'class-validator';
import type {  Type  } from 'class-transformer';

/**
 * 客户端位置信息DTO
 */
export class ClientLocationDto {
  @ApiProperty({ description: '纬度', example: 39.9042 })
  @IsNumber()
  @Min(-90)
  @Max(90)
  latitude: number;

  @ApiProperty({ description: '经度', example: 116.4074 })
  @IsNumber()
  @Min(-180)
  @Max(180)
  longitude: number;

  @ApiPropertyOptional({ description: '城市', example: '北京' })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional({ description: '国家', example: '中国' })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiPropertyOptional({ description: 'ISP提供商', example: '中国电信' })
  @IsOptional()
  @IsString()
  isp?: string;
}

/**
 * 客户端信息DTO
 */
export class ClientInfoDto {
  @ApiProperty({ description: '客户端ID', example: 'client-12345' })
  @IsString()
  clientId: string;

  @ApiProperty({ description: 'IP地址', example: '*************' })
  @IsString()
  ipAddress: string;

  @ApiPropertyOptional({ description: 'User Agent', example: 'Mozilla/5.0...' })
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiPropertyOptional({ description: '客户端位置信息', type: ClientLocationDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => ClientLocationDto)
  location?: ClientLocationDto;

  @ApiPropertyOptional({ 
    description: '连接类型', 
    enum: ['wifi', 'cellular', 'ethernet', 'unknown'],
    example: 'wifi' 
  })
  @IsOptional()
  @IsEnum(['wifi', 'cellular', 'ethernet', 'unknown'])
  connectionType?: 'wifi' | 'cellular' | 'ethernet' | 'unknown';

  @ApiPropertyOptional({ 
    description: '设备类型', 
    enum: ['desktop', 'mobile', 'tablet', 'unknown'],
    example: 'desktop' 
  })
  @IsOptional()
  @IsEnum(['desktop', 'mobile', 'tablet', 'unknown'])
  deviceType?: 'desktop' | 'mobile' | 'tablet' | 'unknown';
}

/**
 * 资源要求DTO
 */
export class ResourceRequirementsDto {
  @ApiPropertyOptional({ description: '最小CPU要求', example: '500m' })
  @IsOptional()
  @IsString()
  cpu?: string;

  @ApiPropertyOptional({ description: '最小内存要求', example: '1Gi' })
  @IsOptional()
  @IsString()
  memory?: string;

  @ApiPropertyOptional({ description: '最小存储要求', example: '5Gi' })
  @IsOptional()
  @IsString()
  storage?: string;
}

/**
 * 路由要求DTO
 */
export class RoutingRequirementsDto {
  @ApiPropertyOptional({ description: '最小带宽要求(Mbps)', example: 10 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  minBandwidth?: number;

  @ApiPropertyOptional({ description: '最大延迟要求(ms)', example: 100 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxLatency?: number;

  @ApiPropertyOptional({ description: '首选区域', example: 'beijing-zone-1' })
  @IsOptional()
  @IsString()
  preferredRegion?: string;

  @ApiPropertyOptional({ description: '最小资源要求', type: ResourceRequirementsDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => ResourceRequirementsDto)
  minResources?: ResourceRequirementsDto;

  @ApiPropertyOptional({ 
    description: '必需功能列表', 
    example: ['webrtc', 'ai-inference'] 
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requiredFeatures?: string[];
}

/**
 * 路由决策请求DTO
 */
export class RoutingDecisionRequestDto {
  @ApiProperty({ description: '客户端信息', type: ClientInfoDto })
  @ValidateNested()
  @Type(() => ClientInfoDto)
  clientInfo: ClientInfoDto;

  @ApiPropertyOptional({ description: '路由要求', type: RoutingRequirementsDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => RoutingRequirementsDto)
  requirements?: RoutingRequirementsDto;

  @ApiPropertyOptional({ description: '额外元数据', example: { sessionId: 'session-123' } })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
