/**
 * 用户服务节点集合 - 第二部分
 * 批次2.1 - 服务器集成节点
 * 提供用户权限、角色、会话等高级用户服务功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { UserInfo, UserSession } from './UserServiceNodes';

/**
 * 用户权限节点
 * 批次2.1 - 用户服务节点
 */
export class UserPermissionNode extends VisualScriptNode {
  public static readonly TYPE = 'UserPermission';
  public static readonly NAME = '用户权限';
  public static readonly DESCRIPTION = '管理用户权限和访问控制';

  constructor(nodeType: string = UserPermissionNode.TYPE, name: string = UserPermissionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('userId', 'string', '用户ID');
    this.addInput('permission', 'string', '权限名称');
    this.addInput('resource', 'string', '资源名称');
    this.addInput('action', 'string', '操作类型');
    this.addInput('permissions', 'array', '权限列表');

    // 输出端口
    this.addOutput('hasPermission', 'boolean', '拥有权限');
    this.addOutput('userPermissions', 'array', '用户权限列表');
    this.addOutput('permissionLevel', 'string', '权限级别');
    this.addOutput('accessDenied', 'boolean', '访问被拒绝');
    this.addOutput('onGranted', 'trigger', '权限授予事件');
    this.addOutput('onDenied', 'trigger', '权限拒绝事件');
  }

  public execute(inputs?: any): any {
    try {
      const userId = inputs?.userId as string;
      const permission = inputs?.permission as string;
      const resource = inputs?.resource as string;
      const action = inputs?.action as string || 'check';
      const permissions = inputs?.permissions as string[] || [];

      if (!userId) {
        Debug.warn('UserPermissionNode', '用户ID为空');
        return this.getDefaultOutputs();
      }

      // 执行权限操作
      const result = this.handlePermissionOperation(userId, permission, resource, action, permissions);
      
      Debug.log('UserPermissionNode', `权限检查完成: ${userId} - ${permission}`);

      return result;
    } catch (error) {
      Debug.error('UserPermissionNode', '权限操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private handlePermissionOperation(userId: string, permission: string, resource: string, action: string, permissions: string[]): any {
    switch (action) {
      case 'check':
        return this.checkPermission(userId, permission, resource);
      case 'list':
        return this.getUserPermissions(userId);
      case 'grant':
        return this.grantPermissions(userId, permissions);
      case 'revoke':
        return this.revokePermissions(userId, permissions);
      default:
        return this.getDefaultOutputs();
    }
  }

  private checkPermission(userId: string, permission: string, resource?: string): any {
    // 简化的权限检查
    const userPermissions = this.getUserPermissionList(userId);
    
    let hasPermission = false;
    let permissionLevel = 'none';
    
    if (permission) {
      // 检查具体权限
      hasPermission = userPermissions.includes(permission);
      
      // 检查资源权限
      if (resource && hasPermission) {
        const resourcePermission = `${permission}:${resource}`;
        hasPermission = userPermissions.includes(resourcePermission) || userPermissions.includes('admin');
      }
      
      // 确定权限级别
      if (userPermissions.includes('admin')) {
        permissionLevel = 'admin';
      } else if (userPermissions.includes('moderator')) {
        permissionLevel = 'moderator';
      } else if (hasPermission) {
        permissionLevel = 'user';
      }
    }

    return {
      hasPermission,
      userPermissions,
      permissionLevel,
      accessDenied: !hasPermission,
      onGranted: hasPermission,
      onDenied: !hasPermission
    };
  }

  private getUserPermissions(userId: string): any {
    const userPermissions = this.getUserPermissionList(userId);
    
    return {
      hasPermission: true,
      userPermissions,
      permissionLevel: this.getPermissionLevel(userPermissions),
      accessDenied: false,
      onGranted: true,
      onDenied: false
    };
  }

  private grantPermissions(userId: string, permissions: string[]): any {
    // 简化的权限授予
    const currentPermissions = this.getUserPermissionList(userId);
    const newPermissions = [...new Set([...currentPermissions, ...permissions])];
    
    return {
      hasPermission: true,
      userPermissions: newPermissions,
      permissionLevel: this.getPermissionLevel(newPermissions),
      accessDenied: false,
      onGranted: true,
      onDenied: false
    };
  }

  private revokePermissions(userId: string, permissions: string[]): any {
    // 简化的权限撤销
    const currentPermissions = this.getUserPermissionList(userId);
    const newPermissions = currentPermissions.filter(p => !permissions.includes(p));
    
    return {
      hasPermission: newPermissions.length > 0,
      userPermissions: newPermissions,
      permissionLevel: this.getPermissionLevel(newPermissions),
      accessDenied: newPermissions.length === 0,
      onGranted: false,
      onDenied: true
    };
  }

  private getUserPermissionList(userId: string): string[] {
    // 简化的用户权限获取
    // 实际应该从数据库或权限服务获取
    const basePermissions = ['read', 'write'];
    
    if (userId === 'admin') {
      return ['admin', 'read', 'write', 'delete', 'manage'];
    } else if (userId.includes('mod')) {
      return ['moderator', 'read', 'write', 'moderate'];
    } else {
      return basePermissions;
    }
  }

  private getPermissionLevel(permissions: string[]): string {
    if (permissions.includes('admin')) return 'admin';
    if (permissions.includes('moderator')) return 'moderator';
    if (permissions.includes('write')) return 'user';
    if (permissions.includes('read')) return 'readonly';
    return 'none';
  }

  private getDefaultOutputs(): any {
    return {
      hasPermission: false,
      userPermissions: [],
      permissionLevel: 'none',
      accessDenied: true,
      onGranted: false,
      onDenied: true
    };
  }
}

/**
 * 用户角色节点
 * 批次2.1 - 用户服务节点
 */
export class UserRoleNode extends VisualScriptNode {
  public static readonly TYPE = 'UserRole';
  public static readonly NAME = '用户角色';
  public static readonly DESCRIPTION = '管理用户角色和角色权限';

  constructor(nodeType: string = UserRoleNode.TYPE, name: string = UserRoleNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('userId', 'string', '用户ID');
    this.addInput('roleName', 'string', '角色名称');
    this.addInput('action', 'string', '操作类型');
    this.addInput('roles', 'array', '角色列表');

    // 输出端口
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('userRoles', 'array', '用户角色');
    this.addOutput('hasRole', 'boolean', '拥有角色');
    this.addOutput('rolePermissions', 'array', '角色权限');
    this.addOutput('primaryRole', 'string', '主要角色');
    this.addOutput('onRoleChanged', 'trigger', '角色变更事件');
  }

  public execute(inputs?: any): any {
    try {
      const userId = inputs?.userId as string;
      const roleName = inputs?.roleName as string;
      const action = inputs?.action as string || 'get';
      const roles = inputs?.roles as string[] || [];

      if (!userId) {
        Debug.warn('UserRoleNode', '用户ID为空');
        return this.getDefaultOutputs();
      }

      // 执行角色操作
      const result = this.handleRoleOperation(userId, roleName, action, roles);
      
      Debug.log('UserRoleNode', `用户角色操作完成: ${action} for ${userId}`);

      return result;
    } catch (error) {
      Debug.error('UserRoleNode', '用户角色操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private handleRoleOperation(userId: string, roleName: string, action: string, roles: string[]): any {
    switch (action) {
      case 'get':
        return this.getUserRoles(userId);
      case 'check':
        return this.checkUserRole(userId, roleName);
      case 'assign':
        return this.assignRoles(userId, roles);
      case 'remove':
        return this.removeRoles(userId, roles);
      case 'getPermissions':
        return this.getRolePermissions(roleName);
      default:
        return this.getDefaultOutputs();
    }
  }

  private getUserRoles(userId: string): any {
    const userRoles = this.getUserRoleList(userId);
    const primaryRole = userRoles.length > 0 ? userRoles[0] : '';
    
    return {
      success: true,
      userRoles,
      hasRole: userRoles.length > 0,
      rolePermissions: this.getAllRolePermissions(userRoles),
      primaryRole,
      onRoleChanged: false
    };
  }

  private checkUserRole(userId: string, roleName: string): any {
    const userRoles = this.getUserRoleList(userId);
    const hasRole = userRoles.includes(roleName);
    
    return {
      success: true,
      userRoles,
      hasRole,
      rolePermissions: hasRole ? this.getRolePermissionList(roleName) : [],
      primaryRole: userRoles.length > 0 ? userRoles[0] : '',
      onRoleChanged: false
    };
  }

  private assignRoles(userId: string, roles: string[]): any {
    const currentRoles = this.getUserRoleList(userId);
    const newRoles = [...new Set([...currentRoles, ...roles])];
    
    return {
      success: true,
      userRoles: newRoles,
      hasRole: newRoles.length > 0,
      rolePermissions: this.getAllRolePermissions(newRoles),
      primaryRole: newRoles.length > 0 ? newRoles[0] : '',
      onRoleChanged: true
    };
  }

  private removeRoles(userId: string, roles: string[]): any {
    const currentRoles = this.getUserRoleList(userId);
    const newRoles = currentRoles.filter(role => !roles.includes(role));
    
    return {
      success: true,
      userRoles: newRoles,
      hasRole: newRoles.length > 0,
      rolePermissions: this.getAllRolePermissions(newRoles),
      primaryRole: newRoles.length > 0 ? newRoles[0] : '',
      onRoleChanged: true
    };
  }

  private getRolePermissions(roleName: string): any {
    const permissions = this.getRolePermissionList(roleName);
    
    return {
      success: true,
      userRoles: [roleName],
      hasRole: true,
      rolePermissions: permissions,
      primaryRole: roleName,
      onRoleChanged: false
    };
  }

  private getUserRoleList(userId: string): string[] {
    // 简化的用户角色获取
    if (userId === 'admin') {
      return ['admin', 'user'];
    } else if (userId.includes('mod')) {
      return ['moderator', 'user'];
    } else {
      return ['user'];
    }
  }

  private getRolePermissionList(roleName: string): string[] {
    // 简化的角色权限映射
    const rolePermissions: Record<string, string[]> = {
      'admin': ['read', 'write', 'delete', 'manage', 'admin'],
      'moderator': ['read', 'write', 'moderate'],
      'user': ['read', 'write'],
      'guest': ['read']
    };
    
    return rolePermissions[roleName] || [];
  }

  private getAllRolePermissions(roles: string[]): string[] {
    const allPermissions = new Set<string>();
    
    for (const role of roles) {
      const permissions = this.getRolePermissionList(role);
      permissions.forEach(permission => allPermissions.add(permission));
    }
    
    return Array.from(allPermissions);
  }

  private getDefaultOutputs(): any {
    return {
      success: false,
      userRoles: [],
      hasRole: false,
      rolePermissions: [],
      primaryRole: '',
      onRoleChanged: false
    };
  }
}
