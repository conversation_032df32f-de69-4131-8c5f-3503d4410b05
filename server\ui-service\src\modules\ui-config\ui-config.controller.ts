import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import type {  Types  } from 'mongoose';
import { UIConfigService } from './ui-config.service';
import { CreateUIConfigDto, UpdateUIConfigDto, QueryUIConfigDto } from './dto/ui-config.dto';
import type {  ConfigType, ConfigScope  } from './schemas/ui-config.schema';

@ApiTags('ui-config')
@Controller('ui-config')
// @UseGuards(JwtAuthGuard) // 需要实现认证守卫
export class UIConfigController {
  constructor(private readonly configService: UIConfigService) {}

  @Post()
  @ApiOperation({ summary: '创建UI配置' })
  @ApiResponse({ status: HttpStatus.CREATED, description: '配置创建成功' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: '请求参数错误' })
  @ApiBearerAuth()
  async create(@Body() createConfigDto: CreateUIConfigDto, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.configService.create(createConfigDto, userId);
  }

  @Get()
  @ApiOperation({ summary: '获取UI配置列表' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiBearerAuth()
  async findAll(@Query() queryDto: QueryUIConfigDto, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.configService.findAll(queryDto, userId);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取单个UI配置' })
  @ApiParam({ name: 'id', description: '配置ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '配置不存在' })
  @ApiBearerAuth()
  async findOne(@Param('id') id: string, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.configService.findOne(id, userId);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新UI配置' })
  @ApiParam({ name: 'id', description: '配置ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '更新成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '配置不存在' })
  @ApiBearerAuth()
  async update(
    @Param('id') id: string,
    @Body() updateConfigDto: UpdateUIConfigDto,
    @Request() req: any,
  ) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.configService.update(id, updateConfigDto, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除UI配置' })
  @ApiParam({ name: 'id', description: '配置ID' })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: '删除成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '配置不存在' })
  @ApiBearerAuth()
  async remove(@Param('id') id: string, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    await this.configService.remove(id, userId);
  }

  @Get('value/:type/:scope/:key')
  @ApiOperation({ summary: '获取配置值' })
  @ApiParam({ name: 'type', enum: ConfigType, description: '配置类型' })
  @ApiParam({ name: 'scope', enum: ConfigScope, description: '配置范围' })
  @ApiParam({ name: 'key', description: '配置键' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  async getConfigValue(
    @Param('type') type: ConfigType,
    @Param('scope') scope: ConfigScope,
    @Param('key') key: string,
    @Request() req: any,
  ) {
    const userId = req.user?.id ? new Types.ObjectId(req.user.id) : undefined;
    return this.configService.getConfigValue(type, scope, key, userId);
  }

  @Get('batch/:type/:scope')
  @ApiOperation({ summary: '批量获取配置' })
  @ApiParam({ name: 'type', enum: ConfigType, description: '配置类型' })
  @ApiParam({ name: 'scope', enum: ConfigScope, description: '配置范围' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  async getBatchConfigs(
    @Param('type') type: ConfigType,
    @Param('scope') scope: ConfigScope,
    @Request() req: any,
  ) {
    const userId = req.user?.id ? new Types.ObjectId(req.user.id) : undefined;
    return this.configService.getBatchConfigs(type, scope, userId);
  }
}
