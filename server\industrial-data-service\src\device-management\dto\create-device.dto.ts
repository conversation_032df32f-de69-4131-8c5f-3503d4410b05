import { IsString, <PERSON>Enum, IsO<PERSON>al, IsNumber, IsObject, IsBoolean, IsIP, Min, Max } from 'class-validator';
import type {  DeviceType, ProtocolType  } from '../entities/device.entity';

export class CreateDeviceDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsEnum(DeviceType)
  type: DeviceType;

  @IsEnum(ProtocolType)
  protocol: ProtocolType;

  @IsIP()
  ipAddress: string;

  @IsNumber()
  @Min(1)
  @Max(65535)
  port: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(255)
  slaveId?: number;

  @IsOptional()
  @IsObject()
  configuration?: any;

  @IsOptional()
  @IsObject()
  tags?: any;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsString()
  manufacturer?: string;

  @IsOptional()
  @IsString()
  model?: string;

  @IsOptional()
  @IsString()
  serialNumber?: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
