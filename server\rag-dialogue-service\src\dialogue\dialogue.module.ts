import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bull';
import { ConfigModule } from '@nestjs/config';

import { DialogueService } from './dialogue.service';
import { DialogueController } from './dialogue.controller';
import { DialogueSession } from './entities/dialogue-session.entity';
import { DialogueMessage } from './entities/dialogue-message.entity';
import { IntentModule } from '../intent/intent.module';
import { EmotionModule } from '../emotion/emotion.module';

@Module({
  imports: [
    // 数据库实体
    TypeOrmModule.forFeature([DialogueSession, DialogueMessage]),
    
    // HTTP客户端
    HttpModule,
    
    // 配置模块
    ConfigModule,
    
    // 队列模块
    BullModule.registerQueue({
      name: 'dialogue-processing',
    }),
    
    // 依赖模块
    IntentModule,
    EmotionModule,
  ],
  controllers: [DialogueController],
  providers: [DialogueService],
  exports: [DialogueService],
})
export class DialogueModule {}
