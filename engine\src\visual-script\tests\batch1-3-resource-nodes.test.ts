/**
 * 批次1.3资源管理节点测试
 * 测试新开发的资源管理和优化节点
 */
import { describe, test, expect, beforeEach } from 'vitest';

// 导入资源加载节点
import {
  LoadAssetNode,
  UnloadAssetNode,
  PreloadAssetNode,
  AsyncLoadAssetNode,
  LoadAssetBundleNode,
  AssetDependencyNode,
  AssetCacheNode,
  AssetCompressionNode,
  AssetEncryptionNode,
  AssetValidationNode,
  AssetMetadataNode,
  AssetVersionNode
} from '../nodes/resources/ResourceManagementNodes';

// 导入资源优化节点
import {
  AssetOptimizationNode,
  TextureCompressionNode,
  MeshOptimizationNode,
  AudioCompressionNode,
  AssetBatchingNode,
  AssetStreamingNode,
  AssetMemoryManagementNode,
  AssetGarbageCollectionNode,
  AssetPerformanceMonitorNode,
  AssetUsageAnalyticsNode
} from '../nodes/resources/ResourceOptimizationNodes';

describe('批次1.3 - 资源管理节点测试', () => {
  describe('资源加载节点', () => {
    test('LoadAssetNode - 基本资源加载', async () => {
      const node = new LoadAssetNode();
      
      const inputs = {
        execute: true,
        resourceId: 'test_texture',
        url: 'https://example.com/texture.jpg',
        type: 'texture',
        priority: 50
      };

      const result = await node.execute(inputs);

      expect(result.resourceId).toBe('test_texture');
      expect(result.onLoaded).toBe(true);
      expect(result.onError).toBe(false);
      expect(result.progress).toBe(100);
    });

    test('UnloadAssetNode - 资源卸载', () => {
      const node = new UnloadAssetNode();
      
      const inputs = {
        execute: true,
        resourceId: 'test_texture',
        force: false
      };

      const result = node.execute(inputs);

      expect(result.resourceId).toBe('test_texture');
      expect(result.success).toBeDefined();
    });

    test('PreloadAssetNode - 预加载多个资源', async () => {
      const node = new PreloadAssetNode();
      
      const inputs = {
        execute: true,
        urls: ['texture1.jpg', 'texture2.jpg', 'model.obj'],
        types: ['texture', 'texture', 'model'],
        priority: 30
      };

      const result = await node.execute(inputs);

      expect(result.totalCount).toBe(3);
      expect(result.loadedCount).toBeGreaterThanOrEqual(0);
      expect(result.progress).toBeGreaterThanOrEqual(0);
      expect(result.onCompleted).toBe(true);
    });

    test('AsyncLoadAssetNode - 异步加载', () => {
      const node = new AsyncLoadAssetNode();
      
      const inputs = {
        execute: true,
        resourceId: 'async_texture',
        url: 'https://example.com/large_texture.jpg',
        type: 'texture',
        priority: 80
      };

      const result = node.execute(inputs);

      expect(result.loadingId).toBeDefined();
      expect(result.onStarted).toBe(true);
    });

    test('LoadAssetBundleNode - 资源包加载', async () => {
      const node = new LoadAssetBundleNode();
      
      const manifest = {
        assets: [
          { id: 'tex1', name: 'texture1', type: 'texture', url: 'tex1.jpg' },
          { id: 'mod1', name: 'model1', type: 'model', url: 'mod1.obj' }
        ]
      };

      const inputs = {
        execute: true,
        bundleId: 'test_bundle',
        manifest,
        priority: 60
      };

      const result = await node.execute(inputs);

      expect(result.bundleId).toBe('test_bundle');
      expect(result.bundle).toBeDefined();
      expect(result.loadedAssets).toBeInstanceOf(Array);
      expect(result.onLoaded).toBe(true);
    });

    test('AssetDependencyNode - 依赖管理', async () => {
      const node = new AssetDependencyNode();
      
      const inputs = {
        execute: true,
        resourceId: 'main_model',
        dependencies: ['texture1', 'texture2', 'material1'],
        autoLoad: true
      };

      const result = await node.execute(inputs);

      expect(result.dependencyTree).toBeDefined();
      expect(result.loadOrder).toBeInstanceOf(Array);
      expect(result.onResolved).toBe(true);
    });

    test('AssetCacheNode - 缓存管理', () => {
      const node = new AssetCacheNode();
      
      const inputs = {
        execute: true,
        operation: 'info'
      };

      const result = node.execute(inputs);

      expect(result.cacheInfo).toBeDefined();
      expect(result.onSuccess).toBe(true);
    });

    test('AssetCompressionNode - 资源压缩', async () => {
      const node = new AssetCompressionNode();
      
      const testData = { type: 'texture', width: 1024, height: 1024 };
      
      const inputs = {
        execute: true,
        operation: 'compress',
        data: testData,
        compressionLevel: 6,
        algorithm: 'gzip'
      };

      const result = await node.execute(inputs);

      expect(result.result).toBeDefined();
      expect(result.originalSize).toBeGreaterThan(0);
      expect(result.compressedSize).toBeGreaterThan(0);
      expect(result.compressionRatio).toBeGreaterThan(0);
      expect(result.onSuccess).toBe(true);
    });

    test('AssetEncryptionNode - 资源加密', () => {
      const node = new AssetEncryptionNode();
      
      const testData = { sensitive: 'data' };
      
      const inputs = {
        execute: true,
        operation: 'encrypt',
        data: testData,
        key: 'test_key',
        algorithm: 'AES-256'
      };

      const result = node.execute(inputs);

      expect(result.result).toBeDefined();
      expect(result.encrypted).toBe(true);
      expect(result.algorithm).toBe('AES-256');
      expect(result.onSuccess).toBe(true);
    });

    test('AssetValidationNode - 资源验证', () => {
      const node = new AssetValidationNode();
      
      const testData = { type: 'texture', width: 1024, height: 1024 };
      const schema = { type: 'object', required: ['type', 'width', 'height'] };
      
      const inputs = {
        execute: true,
        resourceId: 'test_texture',
        data: testData,
        schema
      };

      const result = node.execute(inputs);

      expect(result.valid).toBe(true);
      expect(result.errors).toBeInstanceOf(Array);
      expect(result.checksum).toBeDefined();
      expect(result.onValid).toBe(true);
    });

    test('AssetMetadataNode - 元数据管理', () => {
      const node = new AssetMetadataNode();
      
      const inputs = {
        execute: true,
        operation: 'set',
        resourceId: 'test_resource',
        key: 'author',
        value: 'Test Author'
      };

      const result = node.execute(inputs);

      expect(result.onSuccess).toBe(true);
    });

    test('AssetVersionNode - 版本控制', () => {
      const node = new AssetVersionNode();
      
      const inputs = {
        execute: true,
        operation: 'set',
        resourceId: 'test_resource',
        version: '2.0.0',
        description: '新版本'
      };

      const result = node.execute(inputs);

      expect(result.currentVersion).toBe('2.0.0');
      expect(result.onSuccess).toBe(true);
    });
  });

  describe('资源优化节点', () => {
    test('AssetOptimizationNode - 通用资源优化', async () => {
      const node = new AssetOptimizationNode();
      
      const inputs = {
        execute: true,
        resourceId: 'test_texture',
        optimizationType: 'auto',
        quality: 0.8,
        targetSize: 1024 * 1024
      };

      const result = await node.execute(inputs);

      expect(result.optimizedResource).toBeDefined();
      expect(result.originalSize).toBeGreaterThan(0);
      expect(result.optimizedSize).toBeGreaterThan(0);
      expect(result.compressionRatio).toBeGreaterThanOrEqual(0);
      expect(result.onOptimized).toBe(true);
    });

    test('TextureCompressionNode - 纹理压缩', () => {
      const node = new TextureCompressionNode();
      
      const texture = { width: 1024, height: 1024, channels: 4 };
      
      const inputs = {
        execute: true,
        texture,
        format: 'DXT5',
        quality: 0.8,
        generateMipmaps: true
      };

      const result = node.execute(inputs);

      expect(result.compressedTexture).toBeDefined();
      expect(result.originalSize).toBeGreaterThan(0);
      expect(result.compressedSize).toBeGreaterThan(0);
      expect(result.compressionRatio).toBeGreaterThan(0);
      expect(result.onCompressed).toBe(true);
    });

    test('MeshOptimizationNode - 网格优化', () => {
      const node = new MeshOptimizationNode();
      
      const mesh = { vertexCount: 10000, triangleCount: 20000 };
      
      const inputs = {
        execute: true,
        mesh,
        optimizationType: 'simplify',
        targetVertexCount: 5000,
        preserveUVs: true,
        preserveNormals: true
      };

      const result = node.execute(inputs);

      expect(result.optimizedMesh).toBeDefined();
      expect(result.originalVertexCount).toBe(10000);
      expect(result.optimizedVertexCount).toBeLessThanOrEqual(10000);
      expect(result.reductionRatio).toBeGreaterThanOrEqual(0);
      expect(result.onOptimized).toBe(true);
    });

    test('AudioCompressionNode - 音频压缩', () => {
      const node = new AudioCompressionNode();
      
      const audio = { duration: 60, sampleRate: 44100, channels: 2 };
      
      const inputs = {
        execute: true,
        audio,
        format: 'mp3',
        bitrate: 128,
        sampleRate: 44100,
        channels: 2
      };

      const result = node.execute(inputs);

      expect(result.compressedAudio).toBeDefined();
      expect(result.originalSize).toBeGreaterThan(0);
      expect(result.compressedSize).toBeGreaterThan(0);
      expect(result.compressionRatio).toBeGreaterThan(0);
      expect(result.onCompressed).toBe(true);
    });

    test('AssetBatchingNode - 批处理', async () => {
      const node = new AssetBatchingNode();
      
      const resources = [
        { id: 'res1', size: 1024 },
        { id: 'res2', size: 2048 },
        { id: 'res3', size: 512 }
      ];
      
      const inputs = {
        execute: true,
        resources,
        operation: 'optimize',
        batchSize: 2,
        parallel: true
      };

      const result = await node.execute(inputs);

      expect(result.results).toBeInstanceOf(Array);
      expect(result.results).toHaveLength(3);
      expect(result.successCount).toBeGreaterThanOrEqual(0);
      expect(result.onCompleted).toBe(true);
    });
  });
});
