/**
 * 虚拟化身服务应用模块
 */
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { AvatarModule } from './avatar/avatar.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      useFactory: () => ({
        type: 'postgres',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT) || 5432,
        username: process.env.DB_USERNAME || 'postgres',
        password: process.env.DB_PASSWORD || 'password',
        database: process.env.DB_DATABASE || 'dl_avatar',
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: process.env.NODE_ENV !== 'production',
        logging: process.env.NODE_ENV === 'development',
      }),
    }),

    // 功能模块
    AvatarModule,
  ],
})
export class AppModule {}
