/**
 * 光照控制节点集合
 * 批次1.1：光照控制节点 (8个)
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { 
  Light,
  DirectionalLight,
  PointLight,
  SpotLight,
  AmbientLight,
  HemisphereLight,
  Color,
  Vector3,
  Scene,
  Object3D
} from 'three';

/**
 * 光源类型枚举
 */
export enum LightType {
  DIRECTIONAL = 'directional',
  POINT = 'point',
  SPOT = 'spot',
  AMBIENT = 'ambient',
  HEMISPHERE = 'hemisphere'
}

/**
 * 光照管理器
 */
class LightingManager {
  private lights: Map<string, Light> = new Map();
  private lightGroups: Map<string, Light[]> = new Map();
  private scene: Scene | null = null;
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 设置场景
   */
  setScene(scene: Scene): void {
    this.scene = scene;
  }

  /**
   * 创建光源
   */
  createLight(id: string, type: LightType, config: any): Light {
    let light: Light;

    switch (type) {
      case LightType.DIRECTIONAL:
        light = new DirectionalLight(config.color || 0xffffff, config.intensity || 1);
        if (config.position) light.position.copy(config.position);
        if (config.target && light instanceof DirectionalLight) {
          light.target.position.copy(config.target);
        }
        break;

      case LightType.POINT:
        light = new PointLight(config.color || 0xffffff, config.intensity || 1, config.distance || 0, config.decay || 2);
        if (config.position) light.position.copy(config.position);
        break;

      case LightType.SPOT:
        light = new SpotLight(
          config.color || 0xffffff,
          config.intensity || 1,
          config.distance || 0,
          config.angle || Math.PI / 3,
          config.penumbra || 0,
          config.decay || 2
        );
        if (config.position) light.position.copy(config.position);
        if (config.target && light instanceof SpotLight) {
          light.target.position.copy(config.target);
        }
        break;

      case LightType.AMBIENT:
        light = new AmbientLight(config.color || 0xffffff, config.intensity || 1);
        break;

      case LightType.HEMISPHERE:
        light = new HemisphereLight(
          config.skyColor || 0xffffff,
          config.groundColor || 0x444444,
          config.intensity || 1
        );
        if (config.position) light.position.copy(config.position);
        break;

      default:
        throw new Error(`不支持的光源类型: ${type}`);
    }

    // 设置阴影
    if (config.castShadow && (light instanceof DirectionalLight || light instanceof PointLight || light instanceof SpotLight)) {
      light.castShadow = true;
      if (config.shadowMapSize) {
        light.shadow.mapSize.setScalar(config.shadowMapSize);
      }
      if (config.shadowBias !== undefined) {
        light.shadow.bias = config.shadowBias;
      }
    }

    this.lights.set(id, light);

    // 添加到场景
    if (this.scene) {
      this.scene.add(light);
      if (light instanceof DirectionalLight || light instanceof SpotLight) {
        this.scene.add(light.target);
      }
    }

    this.emit('lightCreated', { id, light, type });
    Debug.log('LightingManager', `光源创建成功: ${id} (${type})`);

    return light;
  }

  /**
   * 获取光源
   */
  getLight(id: string): Light | undefined {
    return this.lights.get(id);
  }

  /**
   * 删除光源
   */
  removeLight(id: string): boolean {
    const light = this.lights.get(id);
    if (!light) return false;

    // 从场景移除
    if (this.scene) {
      this.scene.remove(light);
      if (light instanceof DirectionalLight || light instanceof SpotLight) {
        this.scene.remove(light.target);
      }
    }

    this.lights.delete(id);
    this.emit('lightRemoved', { id, light });
    Debug.log('LightingManager', `光源删除成功: ${id}`);

    return true;
  }

  /**
   * 创建光源组
   */
  createLightGroup(groupId: string, lightIds: string[]): Light[] {
    const lights: Light[] = [];
    
    for (const lightId of lightIds) {
      const light = this.lights.get(lightId);
      if (light) {
        lights.push(light);
      }
    }

    this.lightGroups.set(groupId, lights);
    this.emit('lightGroupCreated', { groupId, lights });
    Debug.log('LightingManager', `光源组创建成功: ${groupId} (${lights.length}个光源)`);

    return lights;
  }

  /**
   * 获取光源组
   */
  getLightGroup(groupId: string): Light[] | undefined {
    return this.lightGroups.get(groupId);
  }

  /**
   * 更新光源属性
   */
  updateLight(id: string, properties: any): boolean {
    const light = this.lights.get(id);
    if (!light) return false;

    if (properties.color) light.color.copy(properties.color);
    if (properties.intensity !== undefined) light.intensity = properties.intensity;
    if (properties.position) light.position.copy(properties.position);

    // 特定光源类型的属性
    if (light instanceof PointLight || light instanceof SpotLight) {
      if (properties.distance !== undefined) light.distance = properties.distance;
      if (properties.decay !== undefined) light.decay = properties.decay;
    }

    if (light instanceof SpotLight) {
      if (properties.angle !== undefined) light.angle = properties.angle;
      if (properties.penumbra !== undefined) light.penumbra = properties.penumbra;
      if (properties.target) light.target.position.copy(properties.target);
    }

    if (light instanceof DirectionalLight && properties.target) {
      light.target.position.copy(properties.target);
    }

    this.emit('lightUpdated', { id, light, properties });
    return true;
  }

  /**
   * 获取所有光源ID
   */
  getAllLightIds(): string[] {
    return Array.from(this.lights.keys());
  }

  /**
   * 获取所有光源组ID
   */
  getAllGroupIds(): string[] {
    return Array.from(this.lightGroups.keys());
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('LightingManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 清理所有光源
   */
  cleanup(): void {
    for (const light of this.lights.values()) {
      if (this.scene) {
        this.scene.remove(light);
        if (light instanceof DirectionalLight || light instanceof SpotLight) {
          this.scene.remove(light.target);
        }
      }
    }
    this.lights.clear();
    this.lightGroups.clear();
    this.eventListeners.clear();
  }
}

// 全局光照管理器实例
const globalLightingManager = new LightingManager();

/**
 * 创建光源节点
 */
export class CreateLightNode extends VisualScriptNode {
  public static readonly TYPE = 'CreateLight';
  public static readonly NAME = '创建光源';
  public static readonly DESCRIPTION = '创建各种类型的光源';

  constructor(nodeType: string = CreateLightNode.TYPE, name: string = CreateLightNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建');
    this.addInput('lightId', 'string', '光源ID');
    this.addInput('lightType', 'string', '光源类型');
    this.addInput('color', 'object', '光源颜色');
    this.addInput('intensity', 'number', '光源强度');
    this.addInput('position', 'object', '光源位置');
    this.addInput('target', 'object', '目标位置');
    this.addInput('distance', 'number', '光照距离');
    this.addInput('angle', 'number', '光照角度');
    this.addInput('penumbra', 'number', '半影');
    this.addInput('castShadow', 'boolean', '投射阴影');

    // 输出端口
    this.addOutput('light', 'object', '光源对象');
    this.addOutput('lightId', 'string', '光源ID');
    this.addOutput('lightType', 'string', '光源类型');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onError', 'trigger', '创建失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      if (!createTrigger) {
        return this.getDefaultOutputs();
      }

      const lightId = inputs?.lightId as string || this.generateLightId();
      const lightType = inputs?.lightType as string || 'directional';
      const color = inputs?.color as Color || new Color(0xffffff);
      const intensity = inputs?.intensity as number ?? 1.0;
      const position = inputs?.position as Vector3 || new Vector3(0, 10, 0);
      const target = inputs?.target as Vector3 || new Vector3(0, 0, 0);
      const distance = inputs?.distance as number ?? 0;
      const angle = inputs?.angle as number ?? Math.PI / 3;
      const penumbra = inputs?.penumbra as number ?? 0;
      const castShadow = inputs?.castShadow as boolean ?? false;

      const config = {
        color,
        intensity,
        position,
        target,
        distance,
        angle,
        penumbra,
        castShadow,
        shadowMapSize: 1024,
        shadowBias: -0.0001
      };

      const light = globalLightingManager.createLight(lightId, lightType as LightType, config);

      Debug.log('CreateLightNode', `光源创建成功: ${lightId} (${lightType})`);

      return {
        light,
        lightId,
        lightType,
        onCreated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('CreateLightNode', '光源创建失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private generateLightId(): string {
    return 'light_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      light: null,
      lightId: '',
      lightType: '',
      onCreated: false,
      onError: false
    };
  }
}

/**
 * 设置光源属性节点
 */
export class SetLightPropertyNode extends VisualScriptNode {
  public static readonly TYPE = 'SetLightProperty';
  public static readonly NAME = '设置光源属性';
  public static readonly DESCRIPTION = '设置光源的各种属性';

  constructor(nodeType: string = SetLightPropertyNode.TYPE, name: string = SetLightPropertyNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('set', 'trigger', '设置');
    this.addInput('lightId', 'string', '光源ID');
    this.addInput('color', 'object', '光源颜色');
    this.addInput('intensity', 'number', '光源强度');
    this.addInput('position', 'object', '光源位置');
    this.addInput('target', 'object', '目标位置');
    this.addInput('distance', 'number', '光照距离');
    this.addInput('angle', 'number', '光照角度');
    this.addInput('penumbra', 'number', '半影');
    this.addInput('castShadow', 'boolean', '投射阴影');

    // 输出端口
    this.addOutput('light', 'object', '光源对象');
    this.addOutput('success', 'boolean', '设置成功');
    this.addOutput('onSet', 'trigger', '设置完成');
    this.addOutput('onError', 'trigger', '设置失败');
  }

  public execute(inputs?: any): any {
    try {
      const setTrigger = inputs?.set;
      if (!setTrigger) {
        return this.getDefaultOutputs();
      }

      const lightId = inputs?.lightId as string;
      if (!lightId) {
        throw new Error('未提供光源ID');
      }

      const properties: any = {};
      if (inputs?.color) properties.color = inputs.color;
      if (inputs?.intensity !== undefined) properties.intensity = inputs.intensity;
      if (inputs?.position) properties.position = inputs.position;
      if (inputs?.target) properties.target = inputs.target;
      if (inputs?.distance !== undefined) properties.distance = inputs.distance;
      if (inputs?.angle !== undefined) properties.angle = inputs.angle;
      if (inputs?.penumbra !== undefined) properties.penumbra = inputs.penumbra;
      if (inputs?.castShadow !== undefined) properties.castShadow = inputs.castShadow;

      const success = globalLightingManager.updateLight(lightId, properties);
      if (!success) {
        throw new Error('光源不存在或更新失败');
      }

      const light = globalLightingManager.getLight(lightId);

      Debug.log('SetLightPropertyNode', `光源属性设置成功: ${lightId}`);

      return {
        light,
        success: true,
        onSet: true,
        onError: false
      };

    } catch (error) {
      Debug.error('SetLightPropertyNode', '光源属性设置失败', error);
      return {
        light: null,
        success: false,
        onSet: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      light: null,
      success: false,
      onSet: false,
      onError: false
    };
  }
}

/**
 * 光源动画节点
 */
export class LightAnimationNode extends VisualScriptNode {
  public static readonly TYPE = 'LightAnimation';
  public static readonly NAME = '光源动画';
  public static readonly DESCRIPTION = '为光源属性创建动画效果';

  private animationId: number | null = null;
  private startTime: number = 0;

  constructor(nodeType: string = LightAnimationNode.TYPE, name: string = LightAnimationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('start', 'trigger', '开始动画');
    this.addInput('stop', 'trigger', '停止动画');
    this.addInput('lightId', 'string', '光源ID');
    this.addInput('property', 'string', '动画属性');
    this.addInput('fromValue', 'any', '起始值');
    this.addInput('toValue', 'any', '结束值');
    this.addInput('duration', 'number', '持续时间(秒)');
    this.addInput('easing', 'string', '缓动函数');
    this.addInput('loop', 'boolean', '循环播放');

    // 输出端口
    this.addOutput('currentValue', 'any', '当前值');
    this.addOutput('progress', 'number', '进度');
    this.addOutput('isPlaying', 'boolean', '正在播放');
    this.addOutput('onStarted', 'trigger', '动画开始');
    this.addOutput('onCompleted', 'trigger', '动画完成');
    this.addOutput('onStopped', 'trigger', '动画停止');
    this.addOutput('onError', 'trigger', '动画错误');
  }

  public execute(inputs?: any): any {
    try {
      const startTrigger = inputs?.start;
      const stopTrigger = inputs?.stop;

      if (startTrigger) {
        return this.startAnimation(inputs);
      } else if (stopTrigger) {
        return this.stopAnimation();
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('LightAnimationNode', '光源动画操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private startAnimation(inputs: any): any {
    const lightId = inputs?.lightId as string;
    const property = inputs?.property as string;
    const fromValue = inputs?.fromValue;
    const toValue = inputs?.toValue;
    const duration = Math.max(0.1, inputs?.duration as number || 1.0);
    const easing = inputs?.easing as string || 'linear';
    const loop = inputs?.loop as boolean || false;

    if (!lightId || !property || fromValue === undefined || toValue === undefined) {
      throw new Error('缺少必要的动画参数');
    }

    const light = globalLightingManager.getLight(lightId);
    if (!light) {
      throw new Error('光源不存在');
    }

    // 停止现有动画
    if (this.animationId !== null) {
      cancelAnimationFrame(this.animationId);
    }

    this.startTime = performance.now();

    const animate = () => {
      const currentTime = performance.now();
      const elapsed = (currentTime - this.startTime) / 1000;
      let progress = Math.min(elapsed / duration, 1);

      // 应用缓动函数
      progress = this.applyEasing(progress, easing);

      // 计算当前值
      const currentValue = this.interpolateValue(fromValue, toValue, progress);

      // 设置光源属性
      const properties: any = {};
      properties[property] = currentValue;
      globalLightingManager.updateLight(lightId, properties);

      if (elapsed >= duration) {
        if (loop) {
          this.startTime = currentTime;
          this.animationId = requestAnimationFrame(animate);
        } else {
          this.animationId = null;
          return {
            currentValue,
            progress: 1,
            isPlaying: false,
            onCompleted: true,
            onStarted: false,
            onStopped: false,
            onError: false
          };
        }
      } else {
        this.animationId = requestAnimationFrame(animate);
      }
    };

    this.animationId = requestAnimationFrame(animate);

    Debug.log('LightAnimationNode', `光源动画开始: ${lightId}.${property}`);

    return {
      currentValue: fromValue,
      progress: 0,
      isPlaying: true,
      onStarted: true,
      onCompleted: false,
      onStopped: false,
      onError: false
    };
  }

  private stopAnimation(): any {
    if (this.animationId !== null) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;

      Debug.log('LightAnimationNode', '光源动画停止');

      return {
        currentValue: null,
        progress: 0,
        isPlaying: false,
        onStarted: false,
        onCompleted: false,
        onStopped: true,
        onError: false
      };
    }

    return this.getDefaultOutputs();
  }

  private applyEasing(t: number, easing: string): number {
    switch (easing) {
      case 'linear':
        return t;
      case 'easeIn':
        return t * t;
      case 'easeOut':
        return 1 - (1 - t) * (1 - t);
      case 'easeInOut':
        return t < 0.5 ? 2 * t * t : 1 - 2 * (1 - t) * (1 - t);
      default:
        return t;
    }
  }

  private interpolateValue(from: any, to: any, t: number): any {
    if (typeof from === 'number' && typeof to === 'number') {
      return from + (to - from) * t;
    } else if (from instanceof Color && to instanceof Color) {
      return from.clone().lerp(to, t);
    } else if (from instanceof Vector3 && to instanceof Vector3) {
      return from.clone().lerp(to, t);
    }
    return t < 0.5 ? from : to;
  }

  private getDefaultOutputs(): any {
    return {
      currentValue: null,
      progress: 0,
      isPlaying: false,
      onStarted: false,
      onCompleted: false,
      onStopped: false,
      onError: false
    };
  }
}
