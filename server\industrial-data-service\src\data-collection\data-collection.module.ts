import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { DataCollectionService } from './data-collection.service';
import { DataCollectionController } from './data-collection.controller';
import { DataCollectionGateway } from './data-collection.gateway';
import { DeviceDataPoint } from './entities/data-point.entity';
import { CollectionTask } from './entities/collection-task.entity';
import { ProtocolModule } from '../protocol/protocol.module';
import { StorageModule } from '../storage/storage.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([DeviceDataPoint, CollectionTask]),
    ProtocolModule,
    StorageModule,
  ],
  controllers: [DataCollectionController],
  providers: [DataCollectionService, DataCollectionGateway],
  exports: [DataCollectionService],
})
export class DataCollectionModule {}
