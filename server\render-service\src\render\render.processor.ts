/**
 * 渲染处理器
 */
import { Process, Processor } from '@nestjs/bull';
import { Logger, Inject } from '@nestjs/common';
import { Job } from 'bull';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { RenderService } from './render.service';
import type {  RenderJobStatus, RenderJobType  } from './entities/render-job.entity';
import { DefaultRenderEngine } from './engines/default-render.engine';

@Processor('render')
export class RenderProcessor {
  private readonly logger = new Logger(RenderProcessor.name);

  constructor(
    private readonly renderService: RenderService,
    private readonly renderEngine: DefaultRenderEngine,
    @Inject('PROJECT_SERVICE') private readonly projectService: ClientProxy,
  ) {}

  @Process('render')
  async handleRender(job: Job) {
    const { jobId } = job.data;
    this.logger.log(`开始处理渲染任务: ${jobId}`);

    try {
      // 更新任务状态为处理中
      const renderJob = await this.renderService.updateStatus(
        jobId,
        RenderJobStatus.PROCESSING,
        0,
      );

      // 获取场景数据
      const sceneData = await this.getSceneData(renderJob.sceneId, renderJob.userId);

      // 验证渲染选项
      if (!this.renderEngine.validateOptions(renderJob.type, renderJob.settings)) {
        throw new Error('无效的渲染选项');
      }

      // 进度回调函数
      const onProgress = async (progress) => {
        await this.renderService.updateStatus(
          jobId,
          RenderJobStatus.PROCESSING,
          progress.progress,
        );
      };

      let result;
      // 根据任务类型执行不同的渲染逻辑
      if (renderJob.type === RenderJobType.IMAGE) {
        result = await this.renderEngine.renderImage(sceneData, renderJob.settings, onProgress);
      } else if (renderJob.type === RenderJobType.VIDEO) {
        result = await this.renderEngine.renderVideo(sceneData, renderJob.settings, onProgress);
      } else if (renderJob.type === RenderJobType.ANIMATION) {
        result = await this.renderEngine.renderAnimation(sceneData, renderJob.settings, onProgress);
      } else {
        throw new Error(`不支持的渲染类型: ${renderJob.type}`);
      }

      // 保存渲染结果
      await this.renderService.addResult(jobId, result);

      // 更新任务状态为已完成
      await this.renderService.updateStatus(
        jobId,
        RenderJobStatus.COMPLETED,
        100,
      );

      this.logger.log(`渲染任务完成: ${jobId}`);
    } catch (error) {
      this.logger.error(`渲染任务失败: ${jobId}`, error.stack);

      // 更新任务状态为失败
      await this.renderService.updateStatus(
        jobId,
        RenderJobStatus.FAILED,
        undefined,
        error.message,
      );
    }
  }

  /**
   * 获取场景数据
   */
  private async getSceneData(sceneId: string, userId: string): Promise<any> {
    try {
      const sceneData = await firstValueFrom(
        this.projectService.send(
          { cmd: 'getSceneData' },
          { sceneId, userId }
        )
      );
      return sceneData;
    } catch (error) {
      this.logger.error(`获取场景数据失败: ${sceneId}`, error);
      throw new Error('无法获取场景数据');
    }
  }
}
