/**
 * 虚拟化身模块
 */
import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { Avatar } from './entities/avatar.entity';
import { AvatarController } from './avatar.controller';
import { AvatarService } from './avatar.service';
import { AvatarUploadController } from './avatar-upload.controller';
import { AvatarUploadService } from './avatar-upload.service';
import { AvatarSceneController } from './avatar-scene.controller';
import { AvatarSceneService } from './avatar-scene.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Avatar])
  ],
  controllers: [
    AvatarController,
    AvatarUploadController,
    AvatarSceneController
  ],
  providers: [
    AvatarService,
    AvatarUploadService,
    AvatarSceneService
  ],
  exports: [AvatarService]
})
export class AvatarModule {}
