/**
 * 性能查询DTO
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsPositive, Min, Max } from 'class-validator';
import type {  Transform, Type  } from 'class-transformer';

export class PerformanceQueryDto {
  @ApiProperty({
    description: '节点ID',
    example: 'node_hostname_12345',
    required: false,
  })
  @IsOptional()
  @IsString()
  nodeId?: string;

  @ApiProperty({
    description: '开始时间戳',
    example: 1640995200000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @IsPositive()
  startTime?: number;

  @ApiProperty({
    description: '结束时间戳',
    example: 1641081600000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @IsPositive()
  endTime?: number;

  @ApiProperty({
    description: '限制数量',
    example: 100,
    default: 100,
    minimum: 1,
    maximum: 1000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(1000)
  limit?: number = 100;

  @ApiProperty({
    description: '偏移量',
    example: 0,
    default: 0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  offset?: number = 0;

  @ApiProperty({
    description: '统计周期',
    example: '1h',
    enum: ['1h', '6h', '24h', '7d'],
    default: '1h',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.toLowerCase())
  period?: '1h' | '6h' | '24h' | '7d' = '1h';

  @ApiProperty({
    description: '指标类型',
    example: 'cpu',
    enum: ['cpu', 'memory', 'network', 'application', 'optimization'],
    required: false,
  })
  @IsOptional()
  @IsString()
  metricType?: 'cpu' | 'memory' | 'network' | 'application' | 'optimization';

  @ApiProperty({
    description: '排序字段',
    example: 'timestamp',
    enum: ['timestamp', 'cpu.usage', 'memory.usage', 'application.responseTime'],
    default: 'timestamp',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'timestamp';

  @ApiProperty({
    description: '排序方向',
    example: 'desc',
    enum: ['asc', 'desc'],
    default: 'desc',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}
