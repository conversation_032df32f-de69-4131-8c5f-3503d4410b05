{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": true, "paths": {"@shared/*": ["../shared/*"]}, "strict": true, "strictFunctionTypes": true, "noImplicitReturns": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test"], "extends": "../tsconfig.base.json"}