/**
 * 推荐控制器
 */
import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Query, 
  Param, 
  HttpStatus,
  HttpException,
  Logger,
  UseGuards,
  ValidationPipe
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiQuery, 
  ApiParam,
  ApiBearerAuth,
  ApiBody
} from '@nestjs/swagger';
import { RecommendationService } from '../recommendation.service';
import type {  
  RecommendationRequest, 
  RecommendationResponse, 
  RecommendationFeedback,
  RecommendationType 
 } from '../interfaces/recommendation.interface';

// DTO类
class GetRecommendationsDto {
  userId: string;
  type: RecommendationType;
  count?: number = 10;
  filters?: Record<string, any>;
  context?: Record<string, any>;
  diversityWeight?: number = 0.3;
  noveltyWeight?: number = 0.2;
  includeExplanation?: boolean = false;
}

class SubmitFeedbackDto {
  userId: string;
  itemId: string;
  recommendationId?: string;
  rating: number;
  feedbackType: 'explicit' | 'implicit' = 'explicit';
  action: 'like' | 'dislike' | 'click' | 'view' | 'skip' | 'share';
  context?: Record<string, any>;
  comment?: string;
}

@ApiTags('recommendations')
@Controller('recommendations')
export class RecommendationController {
  private readonly logger = new Logger(RecommendationController.name);

  constructor(private readonly recommendationService: RecommendationService) {}

  @Post()
  @ApiOperation({ summary: '获取个性化推荐' })
  @ApiBody({ 
    type: GetRecommendationsDto,
    description: '推荐请求参数'
  })
  @ApiResponse({ 
    status: 200, 
    description: '推荐结果',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              itemId: { type: 'string', example: 'item_123' },
              itemType: { type: 'string', example: 'asset' },
              score: { type: 'number', example: 0.95 },
              confidence: { type: 'number', example: 0.87 },
              algorithm: { type: 'string', example: 'neural_collaborative_filtering' },
              explanation: { type: 'string', example: '基于您的历史行为推荐' },
              timestamp: { type: 'string', format: 'date-time' }
            }
          }
        },
        meta: {
          type: 'object',
          properties: {
            userId: { type: 'string' },
            requestType: { type: 'string' },
            count: { type: 'number' },
            processingTime: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getRecommendations(
    @Body(ValidationPipe) request: GetRecommendationsDto
  ) {
    const startTime = Date.now();
    
    try {
      this.logger.log(`获取推荐请求: 用户=${request.userId}, 类型=${request.type}, 数量=${request.count}`);
      
      const recommendations = await this.recommendationService.getRecommendations(request);
      
      const processingTime = Date.now() - startTime;
      
      return {
        success: true,
        data: recommendations,
        meta: {
          userId: request.userId,
          requestType: request.type,
          count: recommendations.length,
          processingTime
        }
      };
      
    } catch (error) {
      this.logger.error(`推荐生成失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '推荐生成失败',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('user/:userId')
  @ApiOperation({ summary: '获取用户推荐（GET方式）' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiQuery({ name: 'type', enum: RecommendationType, description: '推荐类型' })
  @ApiQuery({ name: 'count', required: false, type: Number, description: '推荐数量' })
  @ApiQuery({ name: 'includeExplanation', required: false, type: Boolean, description: '是否包含解释' })
  @ApiResponse({ status: 200, description: '推荐结果' })
  async getUserRecommendations(
    @Param('userId') userId: string,
    @Query('type') type: RecommendationType,
    @Query('count') count?: number,
    @Query('includeExplanation') includeExplanation?: boolean
  ) {
    try {
      const request: RecommendationRequest = {
        userId,
        type,
        count: count || 10,
        includeExplanation: includeExplanation || false
      };
      
      const recommendations = await this.recommendationService.getRecommendations(request);
      
      return {
        success: true,
        data: recommendations,
        meta: {
          userId,
          type,
          count: recommendations.length
        }
      };
      
    } catch (error) {
      this.logger.error(`获取用户推荐失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '获取推荐失败',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('feedback')
  @ApiOperation({ summary: '提交推荐反馈' })
  @ApiBody({ 
    type: SubmitFeedbackDto,
    description: '反馈数据'
  })
  @ApiResponse({ 
    status: 200, 
    description: '反馈提交成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '反馈已记录' },
        feedbackId: { type: 'string', example: 'feedback_123' }
      }
    }
  })
  async submitFeedback(
    @Body(ValidationPipe) feedbackDto: SubmitFeedbackDto
  ) {
    try {
      const feedback: RecommendationFeedback = {
        ...feedbackDto,
        timestamp: new Date()
      };
      
      await this.recommendationService.processFeedback(feedback);
      
      this.logger.log(`反馈已记录: 用户=${feedback.userId}, 物品=${feedback.itemId}, 评分=${feedback.rating}`);
      
      return {
        success: true,
        message: '反馈已记录',
        feedbackId: `feedback_${Date.now()}`
      };
      
    } catch (error) {
      this.logger.error(`反馈处理失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '反馈处理失败',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('explanation/:recommendationId')
  @ApiOperation({ summary: '获取推荐解释' })
  @ApiParam({ name: 'recommendationId', description: '推荐ID' })
  @ApiQuery({ name: 'userId', description: '用户ID' })
  @ApiResponse({ 
    status: 200, 
    description: '推荐解释',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        explanation: { type: 'string', example: '基于您对类似内容的喜好推荐' },
        details: {
          type: 'object',
          properties: {
            algorithm: { type: 'string' },
            factors: { type: 'array', items: { type: 'string' } },
            confidence: { type: 'number' }
          }
        }
      }
    }
  })
  async getRecommendationExplanation(
    @Param('recommendationId') recommendationId: string,
    @Query('userId') userId: string
  ) {
    try {
      const explanation = await this.recommendationService.getRecommendationExplanation(
        recommendationId,
        userId
      );
      
      return {
        success: true,
        explanation,
        details: {
          algorithm: 'neural_collaborative_filtering',
          factors: ['历史行为', '用户偏好', '内容相似性'],
          confidence: 0.85
        }
      };
      
    } catch (error) {
      this.logger.error(`获取推荐解释失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '获取解释失败',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('types')
  @ApiOperation({ summary: '获取支持的推荐类型' })
  @ApiResponse({ 
    status: 200, 
    description: '推荐类型列表',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        types: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: { type: 'string' },
              name: { type: 'string' },
              description: { type: 'string' }
            }
          }
        }
      }
    }
  })
  getSupportedTypes() {
    return {
      success: true,
      types: [
        {
          type: RecommendationType.ASSET,
          name: '资产推荐',
          description: '推荐3D模型、纹理等资产'
        },
        {
          type: RecommendationType.SCENE_TEMPLATE,
          name: '场景模板推荐',
          description: '推荐预制场景模板'
        },
        {
          type: RecommendationType.COLLABORATOR,
          name: '协作者推荐',
          description: '推荐潜在的项目协作者'
        },
        {
          type: RecommendationType.LEARNING_PATH,
          name: '学习路径推荐',
          description: '推荐个性化学习路径'
        },
        {
          type: RecommendationType.MATERIAL,
          name: '材质推荐',
          description: '推荐材质和着色器'
        },
        {
          type: RecommendationType.COMPONENT,
          name: '组件推荐',
          description: '推荐可复用组件'
        }
      ]
    };
  }
}
