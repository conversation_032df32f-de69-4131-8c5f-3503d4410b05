import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { ProcessController } from './process.controller';
import { ProcessService } from './process.service';
import { ProcessRoute, ProcessOperation } from './entities/process-route.entity';

/**
 * 工艺管理模块
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([ProcessRoute, ProcessOperation]),
  ],
  controllers: [ProcessController],
  providers: [ProcessService],
  exports: [ProcessService],
})
export class ProcessModule {}
