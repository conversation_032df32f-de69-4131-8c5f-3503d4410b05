import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';

// 实体
import { PerformanceMetric } from './entities/performance-metric.entity';
import { SystemAlert } from './entities/system-alert.entity';

// 控制器
import { MonitoringController } from './monitoring.controller';

// 服务
import { PerformanceMonitoringService } from './performance-monitoring.service';
import { AlertService } from './alert.service';
import { MetricsCollectionService } from './metrics-collection.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PerformanceMetric,
      SystemAlert,
    ]),
  ],

  controllers: [MonitoringController],

  providers: [
    PerformanceMonitoringService,
    AlertService,
    MetricsCollectionService,
  ],

  exports: [
    PerformanceMonitoringService,
    AlertService,
    MetricsCollectionService,
  ],
})
export class MonitoringModule {}
