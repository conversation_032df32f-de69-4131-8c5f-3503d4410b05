/**
 * 空间查询和分析DTO
 */
import { IsString, IsOptional, IsObject, IsEnum, IsNumber, IsArray, ValidateNested } from 'class-validator';
import type {  Type  } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SpatialQueryDto {
  @ApiProperty({ 
    description: '查询类型',
    enum: ['intersects', 'within', 'contains', 'touches', 'crosses', 'overlaps', 'disjoint', 'buffer']
  })
  @IsEnum(['intersects', 'within', 'contains', 'touches', 'crosses', 'overlaps', 'disjoint', 'buffer'])
  type: 'intersects' | 'within' | 'contains' | 'touches' | 'crosses' | 'overlaps' | 'disjoint' | 'buffer';

  @ApiProperty({ description: '查询几何（GeoJSON格式）' })
  @IsObject()
  geometry: any;

  @ApiPropertyOptional({ description: '目标图层ID' })
  @IsOptional()
  @IsString()
  layerId?: string;

  @ApiPropertyOptional({ description: '缓冲距离（仅buffer查询）' })
  @IsOptional()
  @IsNumber()
  distance?: number;

  @ApiPropertyOptional({ description: '距离单位', enum: ['meters', 'kilometers', 'degrees'], default: 'meters' })
  @IsOptional()
  @IsEnum(['meters', 'kilometers', 'degrees'])
  unit?: 'meters' | 'kilometers' | 'degrees';

  @ApiPropertyOptional({ description: '边界框过滤 [minX, minY, maxX, maxY]' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  bbox?: [number, number, number, number];

  @ApiPropertyOptional({ description: '属性过滤条件' })
  @IsOptional()
  @IsObject()
  propertyFilter?: Record<string, any>;

  @ApiPropertyOptional({ description: '结果限制数量' })
  @IsOptional()
  @IsNumber()
  limit?: number;

  @ApiPropertyOptional({ description: '结果偏移量' })
  @IsOptional()
  @IsNumber()
  offset?: number;

  @ApiPropertyOptional({ description: '是否返回几何数据', default: true })
  @IsOptional()
  @IsNumber()
  includeGeometry?: boolean;
}

export class SpatialAnalysisDto {
  @ApiProperty({ 
    description: '分析类型',
    enum: ['buffer', 'intersection', 'union', 'difference', 'dissolve', 'clip', 'merge']
  })
  @IsEnum(['buffer', 'intersection', 'union', 'difference', 'dissolve', 'clip', 'merge'])
  type: 'buffer' | 'intersection' | 'union' | 'difference' | 'dissolve' | 'clip' | 'merge';

  @ApiProperty({ description: '分析参数' })
  @IsObject()
  parameters: {
    // 缓冲区分析参数
    inputLayerId?: string;
    distance?: number;
    unit?: 'meters' | 'kilometers' | 'degrees';
    segments?: number;
    endCapStyle?: 'round' | 'flat' | 'square';
    joinStyle?: 'round' | 'mitre' | 'bevel';

    // 相交/联合/差异分析参数
    inputLayer1Id?: string;
    inputLayer2Id?: string;
    inputLayerIds?: string[];

    // 融合分析参数
    dissolveField?: string;
    aggregateFields?: Array<{
      field: string;
      operation: 'sum' | 'avg' | 'min' | 'max' | 'count';
    }>;

    // 裁剪分析参数
    clipLayerId?: string;
    targetLayerId?: string;

    // 合并分析参数
    mergeLayerIds?: string[];
    mergeStrategy?: 'union' | 'append';

    // 通用参数
    outputName?: string;
    outputDescription?: string;
    tolerance?: number;
    preserveAttributes?: boolean;
    [key: string]: any;
  };

  @ApiPropertyOptional({ description: '输出图层ID（如果指定，结果将保存到该图层）' })
  @IsOptional()
  @IsString()
  outputLayerId?: string;

  @ApiPropertyOptional({ description: '是否创建新图层', default: true })
  @IsOptional()
  @IsNumber()
  createNewLayer?: boolean;

  @ApiPropertyOptional({ description: '分析选项' })
  @IsOptional()
  @IsObject()
  options?: {
    precision?: number;
    validateGeometry?: boolean;
    simplifyResult?: boolean;
    simplifyTolerance?: number;
    parallel?: boolean;
    maxFeatures?: number;
    timeout?: number;
  };
}

export class BufferAnalysisDto {
  @ApiProperty({ description: '输入图层ID' })
  @IsString()
  inputLayerId: string;

  @ApiProperty({ description: '缓冲距离' })
  @IsNumber()
  distance: number;

  @ApiPropertyOptional({ description: '距离单位', enum: ['meters', 'kilometers', 'degrees'], default: 'meters' })
  @IsOptional()
  @IsEnum(['meters', 'kilometers', 'degrees'])
  unit?: 'meters' | 'kilometers' | 'degrees';

  @ApiPropertyOptional({ description: '分段数', default: 32 })
  @IsOptional()
  @IsNumber()
  segments?: number;

  @ApiPropertyOptional({ description: '端点样式', enum: ['round', 'flat', 'square'], default: 'round' })
  @IsOptional()
  @IsEnum(['round', 'flat', 'square'])
  endCapStyle?: 'round' | 'flat' | 'square';

  @ApiPropertyOptional({ description: '连接样式', enum: ['round', 'mitre', 'bevel'], default: 'round' })
  @IsOptional()
  @IsEnum(['round', 'mitre', 'bevel'])
  joinStyle?: 'round' | 'mitre' | 'bevel';

  @ApiPropertyOptional({ description: '输出图层名称' })
  @IsOptional()
  @IsString()
  outputName?: string;

  @ApiPropertyOptional({ description: '是否融合重叠区域', default: false })
  @IsOptional()
  @IsNumber()
  dissolve?: boolean;
}

export class IntersectionAnalysisDto {
  @ApiProperty({ description: '输入图层1 ID' })
  @IsString()
  inputLayer1Id: string;

  @ApiProperty({ description: '输入图层2 ID' })
  @IsString()
  inputLayer2Id: string;

  @ApiPropertyOptional({ description: '输出图层名称' })
  @IsOptional()
  @IsString()
  outputName?: string;

  @ApiPropertyOptional({ description: '保留属性策略', enum: ['layer1', 'layer2', 'both', 'merge'], default: 'both' })
  @IsOptional()
  @IsEnum(['layer1', 'layer2', 'both', 'merge'])
  attributeStrategy?: 'layer1' | 'layer2' | 'both' | 'merge';

  @ApiPropertyOptional({ description: '最小相交面积阈值' })
  @IsOptional()
  @IsNumber()
  minArea?: number;
}

export class UnionAnalysisDto {
  @ApiProperty({ description: '输入图层ID列表' })
  @IsArray()
  @IsString({ each: true })
  inputLayerIds: string[];

  @ApiPropertyOptional({ description: '输出图层名称' })
  @IsOptional()
  @IsString()
  outputName?: string;

  @ApiPropertyOptional({ description: '是否融合所有要素', default: true })
  @IsOptional()
  @IsNumber()
  dissolveAll?: boolean;

  @ApiPropertyOptional({ description: '融合字段' })
  @IsOptional()
  @IsString()
  dissolveField?: string;
}

export class DifferenceAnalysisDto {
  @ApiProperty({ description: '输入图层ID' })
  @IsString()
  inputLayerId: string;

  @ApiProperty({ description: '差异图层ID' })
  @IsString()
  differenceLayerId: string;

  @ApiPropertyOptional({ description: '输出图层名称' })
  @IsOptional()
  @IsString()
  outputName?: string;

  @ApiPropertyOptional({ description: '保留原始属性', default: true })
  @IsOptional()
  @IsNumber()
  preserveAttributes?: boolean;
}

export class DissolveAnalysisDto {
  @ApiProperty({ description: '输入图层ID' })
  @IsString()
  inputLayerId: string;

  @ApiPropertyOptional({ description: '融合字段' })
  @IsOptional()
  @IsString()
  dissolveField?: string;

  @ApiPropertyOptional({ description: '聚合字段配置' })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  aggregateFields?: Array<{
    field: string;
    operation: 'sum' | 'avg' | 'min' | 'max' | 'count' | 'first' | 'last';
    alias?: string;
  }>;

  @ApiPropertyOptional({ description: '输出图层名称' })
  @IsOptional()
  @IsString()
  outputName?: string;
}

export class ClipAnalysisDto {
  @ApiProperty({ description: '目标图层ID' })
  @IsString()
  targetLayerId: string;

  @ApiProperty({ description: '裁剪图层ID' })
  @IsString()
  clipLayerId: string;

  @ApiPropertyOptional({ description: '输出图层名称' })
  @IsOptional()
  @IsString()
  outputName?: string;

  @ApiPropertyOptional({ description: '保留边界', default: false })
  @IsOptional()
  @IsNumber()
  preserveBoundary?: boolean;
}

export class SpatialStatisticsDto {
  @ApiProperty({ description: '图层ID' })
  @IsString()
  layerId: string;

  @ApiPropertyOptional({ description: '统计类型' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  statistics?: ('count' | 'area' | 'length' | 'perimeter' | 'centroid' | 'bbox')[];

  @ApiPropertyOptional({ description: '分组字段' })
  @IsOptional()
  @IsString()
  groupBy?: string;

  @ApiPropertyOptional({ description: '数值字段统计' })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  numericFields?: Array<{
    field: string;
    operations: ('sum' | 'avg' | 'min' | 'max' | 'count' | 'std' | 'var')[];
  }>;

  @ApiPropertyOptional({ description: '边界框过滤' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  bbox?: [number, number, number, number];
}

export class SpatialAnalysisResultDto {
  @ApiProperty({ description: '分析ID' })
  id: string;

  @ApiProperty({ description: '分析类型' })
  type: string;

  @ApiProperty({ description: '分析状态', enum: ['pending', 'running', 'completed', 'failed'] })
  status: 'pending' | 'running' | 'completed' | 'failed';

  @ApiPropertyOptional({ description: '进度百分比' })
  progress?: number;

  @ApiPropertyOptional({ description: '结果数据' })
  result?: any;

  @ApiPropertyOptional({ description: '错误信息' })
  error?: string;

  @ApiProperty({ description: '开始时间' })
  startTime: Date;

  @ApiPropertyOptional({ description: '结束时间' })
  endTime?: Date;

  @ApiPropertyOptional({ description: '处理时间（毫秒）' })
  processingTime?: number;

  @ApiProperty({ description: '输入参数' })
  parameters: any;

  @ApiPropertyOptional({ description: '输出图层ID' })
  outputLayerId?: string;

  @ApiPropertyOptional({ description: '元数据' })
  metadata?: {
    inputFeatureCount?: number;
    outputFeatureCount?: number;
    processedArea?: number;
    memoryUsage?: number;
    [key: string]: any;
  };
}
