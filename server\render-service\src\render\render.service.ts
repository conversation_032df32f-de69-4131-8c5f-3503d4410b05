/**
 * 渲染服务
 */
import { Injectable, NotFoundException, ForbiddenException, BadRequestException, Inject, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import type {  RenderJob, RenderJobStatus, RenderJobType  } from './entities/render-job.entity';
import { RenderResult } from './entities/render-result.entity';
import { CreateRenderJobDto } from './dto/create-render-job.dto';
import { UpdateRenderJobDto } from './dto/update-render-job.dto';
import { QueryRenderJobDto } from './dto/query-render-job.dto';

@Injectable()
export class RenderService {
  private readonly logger = new Logger(RenderService.name);

  constructor(
    @InjectRepository(RenderJob)
    private readonly renderJobRepository: Repository<RenderJob>,
    @InjectRepository(RenderResult)
    private readonly renderResultRepository: Repository<RenderResult>,
    @InjectQueue('render') private readonly renderQueue: Queue,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
    @Inject('PROJECT_SERVICE') private readonly projectService: ClientProxy,
  ) {}

  /**
   * 创建渲染任务
   */
  async create(userId: string, createRenderJobDto: CreateRenderJobDto): Promise<RenderJob> {
    // 验证用户是否存在
    try {
      await firstValueFrom(this.userService.send({ cmd: 'findUserById' }, userId));
    } catch (error) {
      throw new BadRequestException('用户不存在');
    }

    // 验证项目和场景是否存在，并检查权限
    try {
      await firstValueFrom(
        this.projectService.send(
          { cmd: 'checkProjectPermission' }, 
          { 
            projectId: createRenderJobDto.projectId, 
            userId, 
            roles: ['owner', 'admin', 'editor'] 
          }
        )
      );

      await firstValueFrom(
        this.projectService.send(
          { cmd: 'findSceneById' }, 
          { 
            id: createRenderJobDto.sceneId, 
            userId 
          }
        )
      );
    } catch (error) {
      throw new ForbiddenException('您没有权限在此项目中创建渲染任务或场景不存在');
    }

    // 创建渲染任务
    const renderJob = this.renderJobRepository.create({
      ...createRenderJobDto,
      userId,
      status: RenderJobStatus.PENDING,
      progress: 0,
    });

    // 保存渲染任务
    const savedJob = await this.renderJobRepository.save(renderJob);

    // 添加到渲染队列
    await this.renderQueue.add(
      'render',
      {
        jobId: savedJob.id,
      },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
      }
    );

    return this.findOne(savedJob.id, userId);
  }

  /**
   * 查找所有渲染任务
   */
  async findAll(userId: string, status?: RenderJobStatus, type?: RenderJobType): Promise<RenderJob[]> {
    const queryBuilder = this.renderJobRepository.createQueryBuilder('job')
      .leftJoinAndSelect('job.results', 'result')
      .where('job.userId = :userId', { userId });

    if (status) {
      queryBuilder.andWhere('job.status = :status', { status });
    }

    if (type) {
      queryBuilder.andWhere('job.type = :type', { type });
    }

    queryBuilder.orderBy('job.createdAt', 'DESC');

    return queryBuilder.getMany();
  }

  /**
   * 分页查找渲染任务
   */
  async findAllPaginated(
    userId: string,
    query: QueryRenderJobDto,
  ): Promise<{ data: RenderJob[]; total: number; page: number; limit: number }> {
    const queryBuilder = this.renderJobRepository.createQueryBuilder('job')
      .leftJoinAndSelect('job.results', 'result')
      .where('job.userId = :userId', { userId });

    // 状态过滤
    if (query.status) {
      queryBuilder.andWhere('job.status = :status', { status: query.status });
    }

    // 类型过滤
    if (query.type) {
      queryBuilder.andWhere('job.type = :type', { type: query.type });
    }

    // 项目过滤
    if (query.projectId) {
      queryBuilder.andWhere('job.projectId = :projectId', { projectId: query.projectId });
    }

    // 场景过滤
    if (query.sceneId) {
      queryBuilder.andWhere('job.sceneId = :sceneId', { sceneId: query.sceneId });
    }

    // 日期范围过滤
    if (query.startDate) {
      queryBuilder.andWhere('job.createdAt >= :startDate', { startDate: query.startDate });
    }

    if (query.endDate) {
      queryBuilder.andWhere('job.createdAt <= :endDate', { endDate: query.endDate });
    }

    // 搜索过滤
    if (query.search) {
      queryBuilder.andWhere(
        '(job.name LIKE :search OR job.description LIKE :search)',
        { search: `%${query.search}%` }
      );
    }

    // 排序
    const sortBy = query.sortBy || 'createdAt';
    const sortOrder = query.sortOrder || 'DESC';
    queryBuilder.orderBy(`job.${sortBy}`, sortOrder);

    // 分页
    const page = query.page || 1;
    const limit = query.limit || 10;
    const offset = (page - 1) * limit;

    queryBuilder.skip(offset).take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      data,
      total,
      page,
      limit,
    };
  }

  /**
   * 查找单个渲染任务
   */
  async findOne(id: string, userId: string): Promise<RenderJob> {
    const job = await this.renderJobRepository.findOne({
      where: { id },
      relations: ['results'],
    });

    if (!job) {
      throw new NotFoundException(`渲染任务ID ${id} 不存在`);
    }

    // 检查权限
    if (job.userId !== userId) {
      throw new ForbiddenException('您没有权限访问此渲染任务');
    }

    return job;
  }

  /**
   * 取消渲染任务
   */
  async cancel(id: string, userId: string): Promise<RenderJob> {
    const job = await this.findOne(id, userId);

    // 只能取消待处理或处理中的任务
    if (job.status !== RenderJobStatus.PENDING && job.status !== RenderJobStatus.PROCESSING) {
      throw new BadRequestException('只能取消待处理或处理中的任务');
    }

    // 更新任务状态
    job.status = RenderJobStatus.CANCELED;
    
    // 从队列中移除任务
    const jobsInQueue = await this.renderQueue.getJobs(['waiting', 'active', 'delayed']);
    for (const queueJob of jobsInQueue) {
      const data = await queueJob.data;
      if (data.jobId === id) {
        await queueJob.remove();
        break;
      }
    }

    return this.renderJobRepository.save(job);
  }

  /**
   * 删除渲染任务
   */
  async remove(id: string, userId: string): Promise<void> {
    const job = await this.findOne(id, userId);

    // 不能删除处理中的任务
    if (job.status === RenderJobStatus.PROCESSING) {
      throw new BadRequestException('不能删除处理中的任务');
    }

    // 从队列中移除任务
    const jobsInQueue = await this.renderQueue.getJobs(['waiting', 'active', 'delayed']);
    for (const queueJob of jobsInQueue) {
      const data = await queueJob.data;
      if (data.jobId === id) {
        await queueJob.remove();
        break;
      }
    }

    await this.renderJobRepository.remove(job);
  }

  /**
   * 更新渲染任务状态
   */
  async updateStatus(id: string, status: RenderJobStatus, progress?: number, errorMessage?: string): Promise<RenderJob> {
    const job = await this.renderJobRepository.findOne({
      where: { id },
    });

    if (!job) {
      throw new NotFoundException(`渲染任务ID ${id} 不存在`);
    }

    job.status = status;
    
    if (progress !== undefined) {
      job.progress = progress;
    }
    
    if (errorMessage) {
      job.errorMessage = errorMessage;
    }

    return this.renderJobRepository.save(job);
  }

  /**
   * 添加渲染结果
   */
  async addResult(jobId: string, resultData: Partial<RenderResult>): Promise<RenderResult> {
    const job = await this.renderJobRepository.findOne({
      where: { id: jobId },
    });

    if (!job) {
      throw new NotFoundException(`渲染任务ID ${jobId} 不存在`);
    }

    const result = this.renderResultRepository.create({
      ...resultData,
      jobId,
    });

    return this.renderResultRepository.save(result);
  }

  /**
   * 获取渲染结果
   */
  async getResult(id: string, userId: string): Promise<RenderResult> {
    const result = await this.renderResultRepository.findOne({
      where: { id },
      relations: ['job'],
    });

    if (!result) {
      throw new NotFoundException(`渲染结果ID ${id} 不存在`);
    }

    // 检查权限
    if (result.job.userId !== userId) {
      throw new ForbiddenException('您没有权限访问此渲染结果');
    }

    return result;
  }

  /**
   * 更新渲染任务
   */
  async update(id: string, userId: string, updateData: UpdateRenderJobDto): Promise<RenderJob> {
    const job = await this.findOne(id, userId);

    // 只能更新待处理状态的任务
    if (job.status !== RenderJobStatus.PENDING) {
      throw new BadRequestException('只能更新待处理状态的任务');
    }

    Object.assign(job, updateData);
    await this.renderJobRepository.save(job);

    return this.findOne(id, userId);
  }

  /**
   * 重试失败的任务
   */
  async retry(id: string, userId: string): Promise<RenderJob> {
    const job = await this.findOne(id, userId);

    // 只能重试失败或取消的任务
    if (job.status !== RenderJobStatus.FAILED && job.status !== RenderJobStatus.CANCELED) {
      throw new BadRequestException('只能重试失败或取消的任务');
    }

    // 重置任务状态
    job.status = RenderJobStatus.PENDING;
    job.progress = 0;
    job.errorMessage = null;
    await this.renderJobRepository.save(job);

    // 重新添加到队列
    await this.renderQueue.add(
      'render',
      { jobId: job.id },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
      }
    );

    return this.findOne(id, userId);
  }

  /**
   * 上传资源文件
   */
  async uploadAssets(
    jobId: string,
    userId: string,
    files: Express.Multer.File[],
  ): Promise<{ message: string; files: any[] }> {
    const job = await this.findOne(jobId, userId);

    if (job.status !== RenderJobStatus.PENDING) {
      throw new BadRequestException('只能为待处理的任务上传资源');
    }

    const uploadedFiles = files.map(file => ({
      originalName: file.originalname,
      filename: file.filename,
      path: file.path,
      size: file.size,
      mimetype: file.mimetype,
    }));

    // 这里可以将文件信息保存到数据库或更新任务设置
    this.logger.log(`为任务 ${jobId} 上传了 ${files.length} 个文件`);

    return {
      message: `成功上传 ${files.length} 个文件`,
      files: uploadedFiles,
    };
  }

  /**
   * 获取统计信息
   */
  async getStats(userId: string): Promise<any> {
    const totalJobs = await this.renderJobRepository.count({ where: { userId } });

    const statusStats = await this.renderJobRepository
      .createQueryBuilder('job')
      .select('job.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .where('job.userId = :userId', { userId })
      .groupBy('job.status')
      .getRawMany();

    const typeStats = await this.renderJobRepository
      .createQueryBuilder('job')
      .select('job.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('job.userId = :userId', { userId })
      .groupBy('job.type')
      .getRawMany();

    const recentJobs = await this.renderJobRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      take: 5,
      relations: ['results'],
    });

    return {
      totalJobs,
      statusStats: statusStats.reduce((acc, stat) => {
        acc[stat.status] = parseInt(stat.count);
        return acc;
      }, {}),
      typeStats: typeStats.reduce((acc, stat) => {
        acc[stat.type] = parseInt(stat.count);
        return acc;
      }, {}),
      recentJobs,
    };
  }

  /**
   * 获取队列状态
   */
  async getQueueStatus(): Promise<any> {
    const waiting = await this.renderQueue.getWaiting();
    const active = await this.renderQueue.getActive();
    const completed = await this.renderQueue.getCompleted();
    const failed = await this.renderQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      total: waiting.length + active.length + completed.length + failed.length,
    };
  }
}
