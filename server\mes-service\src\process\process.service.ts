import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In, Like } from 'typeorm';
import type {  ProcessRoute, ProcessOperation, ProcessRouteStatus, OperationType  } from './entities/process-route.entity';
import { CreateProcessRouteDto, UpdateProcessRouteDto, ProcessRouteQueryDto, CreateOperationDto, UpdateOperationDto } from './dto/process.dto';
import * as moment from 'moment';

/**
 * 工艺路线统计接口
 */
export interface ProcessRouteStatistics {
  totalRoutes: number;
  routesByStatus: Record<ProcessRouteStatus, number>;
  avgOperationsPerRoute: number;
  avgStandardTime: number;
  operationsByType: Record<OperationType, number>;
  activeRoutes: number;
}

/**
 * 工艺管理服务
 */
@Injectable()
export class ProcessService {
  private readonly logger = new Logger(ProcessService.name);

  constructor(
    @InjectRepository(ProcessRoute)
    private readonly processRouteRepository: Repository<ProcessRoute>,
    @InjectRepository(ProcessOperation)
    private readonly processOperationRepository: Repository<ProcessOperation>,
  ) {}

  /**
   * 创建工艺路线
   */
  async createProcessRoute(createProcessRouteDto: CreateProcessRouteDto): Promise<ProcessRoute> {
    try {
      // 检查工艺路线编码是否已存在
      const existingRoute = await this.processRouteRepository.findOne({
        where: { routeCode: createProcessRouteDto.routeCode },
      });

      if (existingRoute) {
        throw new BadRequestException(`工艺路线编码 ${createProcessRouteDto.routeCode} 已存在`);
      }

      const processRoute = this.processRouteRepository.create({
        ...createProcessRouteDto,
        effectiveDate: new Date(createProcessRouteDto.effectiveDate),
        expiryDate: createProcessRouteDto.expiryDate ? new Date(createProcessRouteDto.expiryDate) : null,
      });

      const savedRoute = await this.processRouteRepository.save(processRoute);
      this.logger.log(`工艺路线创建成功: ${savedRoute.routeCode}`);

      return savedRoute;
    } catch (error) {
      this.logger.error(`创建工艺路线失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取工艺路线列表
   */
  async getProcessRoutes(query: ProcessRouteQueryDto) {
    try {
      const { page = 1, limit = 10, routeCode, routeName, productCode, status, sortBy = 'createdAt', sortOrder = 'DESC' } = query;
      
      const queryBuilder = this.processRouteRepository.createQueryBuilder('route')
        .leftJoinAndSelect('route.operations', 'operations');

      // 添加查询条件
      if (routeCode) {
        queryBuilder.andWhere('route.routeCode LIKE :routeCode', { routeCode: `%${routeCode}%` });
      }

      if (routeName) {
        queryBuilder.andWhere('route.routeName LIKE :routeName', { routeName: `%${routeName}%` });
      }

      if (productCode) {
        queryBuilder.andWhere('route.productCode LIKE :productCode', { productCode: `%${productCode}%` });
      }

      if (status) {
        queryBuilder.andWhere('route.status = :status', { status });
      }

      // 排序
      queryBuilder.orderBy(`route.${sortBy}`, sortOrder);

      // 分页
      const skip = (page - 1) * limit;
      queryBuilder.skip(skip).take(limit);

      const [data, total] = await queryBuilder.getManyAndCount();

      return {
        data,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(`获取工艺路线列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据ID获取工艺路线
   */
  async getProcessRouteById(id: string): Promise<ProcessRoute> {
    try {
      const processRoute = await this.processRouteRepository.findOne({
        where: { id },
        relations: ['operations'],
      });

      if (!processRoute) {
        throw new NotFoundException(`工艺路线不存在: ${id}`);
      }

      return processRoute;
    } catch (error) {
      this.logger.error(`获取工艺路线失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新工艺路线
   */
  async updateProcessRoute(id: string, updateProcessRouteDto: UpdateProcessRouteDto): Promise<ProcessRoute> {
    try {
      const processRoute = await this.getProcessRouteById(id);

      // 如果更新工艺路线编码，检查是否重复
      if (updateProcessRouteDto.routeCode && updateProcessRouteDto.routeCode !== processRoute.routeCode) {
        const existingRoute = await this.processRouteRepository.findOne({
          where: { routeCode: updateProcessRouteDto.routeCode },
        });

        if (existingRoute) {
          throw new BadRequestException(`工艺路线编码 ${updateProcessRouteDto.routeCode} 已存在`);
        }
      }

      // 更新数据
      Object.assign(processRoute, {
        ...updateProcessRouteDto,
        effectiveDate: updateProcessRouteDto.effectiveDate ? new Date(updateProcessRouteDto.effectiveDate) : processRoute.effectiveDate,
        expiryDate: updateProcessRouteDto.expiryDate ? new Date(updateProcessRouteDto.expiryDate) : processRoute.expiryDate,
      });

      const updatedRoute = await this.processRouteRepository.save(processRoute);
      this.logger.log(`工艺路线更新成功: ${updatedRoute.routeCode}`);

      return updatedRoute;
    } catch (error) {
      this.logger.error(`更新工艺路线失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除工艺路线
   */
  async deleteProcessRoute(id: string): Promise<void> {
    try {
      const processRoute = await this.getProcessRouteById(id);

      // 删除关联的工序
      await this.processOperationRepository.delete({ processRouteId: id });

      // 删除工艺路线
      await this.processRouteRepository.remove(processRoute);

      this.logger.log(`工艺路线删除成功: ${processRoute.routeCode}`);
    } catch (error) {
      this.logger.error(`删除工艺路线失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 激活工艺路线
   */
  async activateProcessRoute(id: string): Promise<ProcessRoute> {
    try {
      const processRoute = await this.getProcessRouteById(id);
      
      processRoute.status = ProcessRouteStatus.ACTIVE;
      const updatedRoute = await this.processRouteRepository.save(processRoute);

      this.logger.log(`工艺路线激活成功: ${updatedRoute.routeCode}`);
      return updatedRoute;
    } catch (error) {
      this.logger.error(`激活工艺路线失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 停用工艺路线
   */
  async deactivateProcessRoute(id: string): Promise<ProcessRoute> {
    try {
      const processRoute = await this.getProcessRouteById(id);
      
      processRoute.status = ProcessRouteStatus.INACTIVE;
      const updatedRoute = await this.processRouteRepository.save(processRoute);

      this.logger.log(`工艺路线停用成功: ${updatedRoute.routeCode}`);
      return updatedRoute;
    } catch (error) {
      this.logger.error(`停用工艺路线失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 复制工艺路线
   */
  async copyProcessRoute(id: string, newRouteCode: string, newRouteName: string): Promise<ProcessRoute> {
    try {
      const originalRoute = await this.getProcessRouteById(id);

      // 检查新编码是否已存在
      const existingRoute = await this.processRouteRepository.findOne({
        where: { routeCode: newRouteCode },
      });

      if (existingRoute) {
        throw new BadRequestException(`工艺路线编码 ${newRouteCode} 已存在`);
      }

      // 创建新的工艺路线
      const newRoute = this.processRouteRepository.create({
        ...originalRoute,
        id: undefined,
        routeCode: newRouteCode,
        routeName: newRouteName,
        version: 1,
        status: ProcessRouteStatus.DRAFT,
        createdAt: undefined,
        updatedAt: undefined,
      });

      const savedRoute = await this.processRouteRepository.save(newRoute);

      // 复制工序
      if (originalRoute.operations && originalRoute.operations.length > 0) {
        const newOperations = originalRoute.operations.map(operation => 
          this.processOperationRepository.create({
            ...operation,
            id: undefined,
            processRouteId: savedRoute.id,
            createdAt: undefined,
            updatedAt: undefined,
          })
        );

        await this.processOperationRepository.save(newOperations);
      }

      this.logger.log(`工艺路线复制成功: ${originalRoute.routeCode} -> ${newRouteCode}`);
      return await this.getProcessRouteById(savedRoute.id);
    } catch (error) {
      this.logger.error(`复制工艺路线失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 添加工序
   */
  async addOperation(processRouteId: string, createOperationDto: CreateOperationDto): Promise<ProcessOperation> {
    try {
      // 验证工艺路线是否存在
      await this.getProcessRouteById(processRouteId);

      const operation = this.processOperationRepository.create({
        ...createOperationDto,
        processRouteId,
      });

      const savedOperation = await this.processOperationRepository.save(operation);
      this.logger.log(`工序添加成功: ${savedOperation.operationCode}`);

      return savedOperation;
    } catch (error) {
      this.logger.error(`添加工序失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新工序
   */
  async updateOperation(operationId: string, updateOperationDto: UpdateOperationDto): Promise<ProcessOperation> {
    try {
      const operation = await this.processOperationRepository.findOne({
        where: { id: operationId },
      });

      if (!operation) {
        throw new NotFoundException(`工序不存在: ${operationId}`);
      }

      Object.assign(operation, updateOperationDto);
      const updatedOperation = await this.processOperationRepository.save(operation);

      this.logger.log(`工序更新成功: ${updatedOperation.operationCode}`);
      return updatedOperation;
    } catch (error) {
      this.logger.error(`更新工序失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除工序
   */
  async deleteOperation(operationId: string): Promise<void> {
    try {
      const operation = await this.processOperationRepository.findOne({
        where: { id: operationId },
      });

      if (!operation) {
        throw new NotFoundException(`工序不存在: ${operationId}`);
      }

      await this.processOperationRepository.remove(operation);
      this.logger.log(`工序删除成功: ${operation.operationCode}`);
    } catch (error) {
      this.logger.error(`删除工序失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取工艺路线统计
   */
  async getProcessRouteStatistics(): Promise<ProcessRouteStatistics> {
    try {
      const totalRoutes = await this.processRouteRepository.count();
      
      const routesByStatus = await this.processRouteRepository
        .createQueryBuilder('route')
        .select('route.status', 'status')
        .addSelect('COUNT(*)', 'count')
        .groupBy('route.status')
        .getRawMany();

      const statusCounts = routesByStatus.reduce((acc, item) => {
        acc[item.status] = parseInt(item.count);
        return acc;
      }, {} as Record<ProcessRouteStatus, number>);

      // 填充缺失的状态
      Object.values(ProcessRouteStatus).forEach(status => {
        if (!statusCounts[status]) {
          statusCounts[status] = 0;
        }
      });

      const operationStats = await this.processOperationRepository
        .createQueryBuilder('operation')
        .select('operation.operationType', 'type')
        .addSelect('COUNT(*)', 'count')
        .groupBy('operation.operationType')
        .getRawMany();

      const operationsByType = operationStats.reduce((acc, item) => {
        acc[item.type] = parseInt(item.count);
        return acc;
      }, {} as Record<OperationType, number>);

      // 填充缺失的工序类型
      Object.values(OperationType).forEach(type => {
        if (!operationsByType[type]) {
          operationsByType[type] = 0;
        }
      });

      const avgStats = await this.processRouteRepository
        .createQueryBuilder('route')
        .leftJoin('route.operations', 'operations')
        .select('AVG(route.standardTime)', 'avgStandardTime')
        .addSelect('COUNT(operations.id) / COUNT(DISTINCT route.id)', 'avgOperationsPerRoute')
        .getRawOne();

      const activeRoutes = await this.processRouteRepository.count({
        where: { status: ProcessRouteStatus.ACTIVE },
      });

      return {
        totalRoutes,
        routesByStatus: statusCounts,
        avgOperationsPerRoute: parseFloat(avgStats.avgOperationsPerRoute) || 0,
        avgStandardTime: parseFloat(avgStats.avgStandardTime) || 0,
        operationsByType,
        activeRoutes,
      };
    } catch (error) {
      this.logger.error(`获取工艺路线统计失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
