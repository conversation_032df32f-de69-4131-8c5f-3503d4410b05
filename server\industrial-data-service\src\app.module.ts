import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { DataCollectionModule } from './data-collection/data-collection.module';
import { DeviceManagementModule } from './device-management/device-management.module';
import { ProtocolModule } from './protocol/protocol.module';
import { StorageModule } from './storage/storage.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { AlertModule } from './alert/alert.module';
import { WebSocketModule } from './websocket/websocket.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 3306),
        username: configService.get('DB_USERNAME', 'root'),
        password: configService.get('DB_PASSWORD', ''),
        database: configService.get('DB_DATABASE', 'industrial_data'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: configService.get('NODE_ENV') !== 'production',
        logging: configService.get('NODE_ENV') === 'development',
        timezone: '+08:00',
      }),
      inject: [ConfigService],
    }),
    
    // 定时任务模块
    ScheduleModule.forRoot(),
    
    // 业务模块
    DataCollectionModule,
    DeviceManagementModule,
    ProtocolModule,
    StorageModule,
    AnalyticsModule,
    AlertModule,
    WebSocketModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
