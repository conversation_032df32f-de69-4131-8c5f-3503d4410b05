// HTTP通信协议标准
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: any;
  timestamp: string;
  requestId: string;
}

export class HttpProtocol {
  static success<T>(data: T, message?: string) {
    return {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
      requestId: 'req_' + Date.now()
    };
  }

  static error(error: any) {
    return {
      success: false,
      error,
      timestamp: new Date().toISOString(),
      requestId: 'req_' + Date.now()
    };
  }
}