/**
 * 缓冲区分析服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SpatialFeature } from '../entities/spatial-feature.entity';
import { SpatialLayer } from '../entities/spatial-layer.entity';
import type {  BufferAnalysisDto, BufferType  } from './dto/analysis.dto';
import * as turf from '@turf/turf';

@Injectable()
export class BufferAnalysisService {
  private readonly logger = new Logger(BufferAnalysisService.name);

  constructor(
    @InjectRepository(SpatialFeature)
    private spatialFeatureRepository: Repository<SpatialFeature>,
    @InjectRepository(SpatialLayer)
    private spatialLayerRepository: Repository<SpatialLayer>,
  ) {}

  /**
   * 执行缓冲区分析
   */
  async performBufferAnalysis(bufferAnalysisDto: BufferAnalysisDto) {
    const startTime = Date.now();
    this.logger.log(`开始缓冲区分析: ${JSON.stringify(bufferAnalysisDto)}`);

    try {
      // 获取输入图层
      const inputLayer = await this.spatialLayerRepository.findOne({
        where: { id: bufferAnalysisDto.inputLayerId }
      });

      if (!inputLayer) {
        throw new Error(`输入图层不存在: ${bufferAnalysisDto.inputLayerId}`);
      }

      // 获取图层要素
      const features = await this.spatialFeatureRepository.find({
        where: { layerId: bufferAnalysisDto.inputLayerId }
      });

      if (features.length === 0) {
        throw new Error('输入图层没有要素');
      }

      // 执行缓冲区分析
      const bufferResults = await this.createBuffers(features, bufferAnalysisDto);

      // 创建输出图层
      const outputLayer = await this.createOutputLayer(
        inputLayer,
        bufferAnalysisDto.outputLayerName || `${inputLayer.name}_buffer`
      );

      // 保存缓冲区结果
      const savedFeatures = await this.saveBufferResults(bufferResults, outputLayer.id);

      const processingTime = Date.now() - startTime;
      this.logger.log(`缓冲区分析完成，耗时: ${processingTime}ms`);

      return {
        success: true,
        outputLayerId: outputLayer.id,
        outputLayerName: outputLayer.name,
        featureCount: savedFeatures.length,
        processingTime: `${processingTime}ms`,
        parameters: bufferAnalysisDto,
        statistics: this.calculateBufferStatistics(savedFeatures)
      };

    } catch (error) {
      this.logger.error('缓冲区分析失败:', error);
      throw new Error(`缓冲区分析失败: ${error.message}`);
    }
  }

  /**
   * 创建缓冲区
   */
  private async createBuffers(features: SpatialFeature[], params: BufferAnalysisDto) {
    const buffers = [];

    for (const feature of features) {
      try {
        const geometry = JSON.parse(feature.geometry);
        let buffer;

        // 根据缓冲区类型创建缓冲区
        switch (params.bufferType) {
          case BufferType.FIXED:
            buffer = turf.buffer(geometry, params.distance, { units: params.unit as any });
            break;

          case BufferType.VARIABLE:
            // 变量缓冲区：根据要素属性确定距离
            const variableDistance = this.getVariableDistance(feature, params);
            buffer = turf.buffer(geometry, variableDistance, { units: params.unit as any });
            break;

          case BufferType.MULTIPLE:
            // 多重缓冲区：创建多个同心缓冲区
            const multipleBuffers = this.createMultipleBuffers(geometry, params);
            buffers.push(...multipleBuffers);
            continue;

          default:
            buffer = turf.buffer(geometry, params.distance, { units: params.unit as any });
        }

        if (buffer) {
          buffers.push({
            originalFeature: feature,
            bufferGeometry: buffer.geometry,
            bufferDistance: params.distance,
            properties: {
              ...feature.properties,
              buffer_distance: params.distance,
              buffer_type: params.bufferType,
              original_feature_id: feature.id
            }
          });
        }

      } catch (error) {
        this.logger.warn(`要素 ${feature.id} 缓冲区创建失败:`, error);
      }
    }

    // 如果需要合并结果
    if (params.dissolve && buffers.length > 1) {
      return this.dissolveBuffers(buffers);
    }

    return buffers;
  }

  /**
   * 获取变量缓冲区距离
   */
  private getVariableDistance(feature: SpatialFeature, params: BufferAnalysisDto): number {
    // 这里可以根据要素属性计算变量距离
    // 示例：根据某个属性字段的值来确定缓冲区距离
    const properties = feature.properties || {};
    
    // 假设有一个 'buffer_field' 属性用于确定缓冲区距离
    if (properties.buffer_field && typeof properties.buffer_field === 'number') {
      return properties.buffer_field;
    }

    // 默认使用固定距离
    return params.distance;
  }

  /**
   * 创建多重缓冲区
   */
  private createMultipleBuffers(geometry: any, params: BufferAnalysisDto) {
    const buffers = [];
    const distances = [
      params.distance * 0.5,
      params.distance,
      params.distance * 1.5,
      params.distance * 2
    ];

    for (let i = 0; i < distances.length; i++) {
      try {
        const buffer = turf.buffer(geometry, distances[i], { units: params.unit as any });
        buffers.push({
          bufferGeometry: buffer.geometry,
          bufferDistance: distances[i],
          bufferRing: i + 1,
          properties: {
            buffer_distance: distances[i],
            buffer_ring: i + 1,
            buffer_type: 'multiple'
          }
        });
      } catch (error) {
        this.logger.warn(`多重缓冲区 ${i + 1} 创建失败:`, error);
      }
    }

    return buffers;
  }

  /**
   * 合并缓冲区
   */
  private dissolveBuffers(buffers: any[]) {
    try {
      // 提取所有缓冲区几何
      const geometries = buffers.map(b => b.bufferGeometry);
      
      // 使用turf.union合并几何
      let dissolved = geometries[0];
      for (let i = 1; i < geometries.length; i++) {
        dissolved = turf.union(dissolved, geometries[i]);
      }

      return [{
        bufferGeometry: dissolved.geometry,
        bufferDistance: buffers[0].bufferDistance,
        properties: {
          buffer_type: 'dissolved',
          feature_count: buffers.length,
          total_area: turf.area(dissolved)
        }
      }];

    } catch (error) {
      this.logger.warn('缓冲区合并失败，返回原始结果:', error);
      return buffers;
    }
  }

  /**
   * 创建输出图层
   */
  private async createOutputLayer(inputLayer: SpatialLayer, outputName: string): Promise<SpatialLayer> {
    const outputLayer = this.spatialLayerRepository.create({
      name: outputName,
      description: `${inputLayer.name} 的缓冲区分析结果`,
      projectId: inputLayer.projectId,
      layerType: 'vector' as const,
      createdBy: inputLayer.createdBy,
      style: {
        default: {
          fillColor: 'rgba(255, 0, 0, 0.3)',
          color: '#ff0000',
          weight: 2
        }
      },
      metadata: {
        crs: 'EPSG:4326',
        source: {
          type: 'service' as const,
          format: 'buffer_analysis'
        },
        inputLayer: inputLayer.id,
        createdAt: new Date().toISOString()
      }
    });

    return await this.spatialLayerRepository.save(outputLayer);
  }

  /**
   * 保存缓冲区结果
   */
  private async saveBufferResults(buffers: any[], outputLayerId: string) {
    const features = buffers.map(buffer => 
      this.spatialFeatureRepository.create({
        layerId: outputLayerId,
        geometry: JSON.stringify(buffer.bufferGeometry),
        properties: buffer.properties
      })
    );

    return await this.spatialFeatureRepository.save(features);
  }

  /**
   * 计算缓冲区统计信息
   */
  private calculateBufferStatistics(features: SpatialFeature[]) {
    let totalArea = 0;
    let minArea = Infinity;
    let maxArea = 0;

    for (const feature of features) {
      try {
        const geometry = JSON.parse(feature.geometry);
        const area = turf.area(geometry);
        
        totalArea += area;
        minArea = Math.min(minArea, area);
        maxArea = Math.max(maxArea, area);
      } catch (error) {
        this.logger.warn(`计算要素 ${feature.id} 面积失败:`, error);
      }
    }

    return {
      featureCount: features.length,
      totalArea: Math.round(totalArea),
      averageArea: Math.round(totalArea / features.length),
      minArea: Math.round(minArea),
      maxArea: Math.round(maxArea),
      unit: 'square_meters'
    };
  }
}
