/**
 * 微服务间通信协议
 */

export interface ServiceMessage<T = any> {
  id: string;
  type: string;
  source: string;
  target: string;
  payload: T;
  timestamp: string;
  correlationId?: string;
  replyTo?: string;
  headers?: Record<string, any>;
}

export interface ServiceEvent<T = any> {
  id: string;
  type: string;
  source: string;
  payload: T;
  timestamp: string;
  version: string;
  metadata?: Record<string, any>;
}

export class MicroserviceProtocol {
  static createMessage<T>(
    type: string,
    source: string,
    target: string,
    payload: T,
    options?: {
      correlationId?: string;
      replyTo?: string;
      headers?: Record<string, any>;
    }
  ): ServiceMessage<T> {
    return {
      id: this.generateId(),
      type,
      source,
      target,
      payload,
      timestamp: new Date().toISOString(),
      ...options
    };
  }

  static createEvent<T>(
    type: string,
    source: string,
    payload: T,
    metadata?: Record<string, any>
  ): ServiceEvent<T> {
    return {
      id: this.generateId(),
      type,
      source,
      payload,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      metadata
    };
  }

  private static generateId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}