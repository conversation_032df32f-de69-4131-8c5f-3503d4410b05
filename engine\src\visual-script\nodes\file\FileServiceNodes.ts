/**
 * 文件服务节点集合
 * 批次2.1 - 服务器集成节点
 * 提供文件上传下载、存储、压缩加密等文件服务功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 文件信息接口
 */
export interface FileInfo {
  id: string;
  name: string;
  path: string;
  size: number;
  type: string;
  mimeType: string;
  hash: string;
  createdAt: Date;
  modifiedAt: Date;
  metadata?: any;
}

/**
 * 文件上传配置接口
 */
export interface UploadConfig {
  destination: string;
  maxSize: number;
  allowedTypes: string[];
  generateThumbnail: boolean;
  overwrite: boolean;
}

/**
 * 文件上传节点
 * 批次2.1 - 文件服务节点
 */
export class FileUploadNode extends VisualScriptNode {
  public static readonly TYPE = 'FileUpload';
  public static readonly NAME = '文件上传';
  public static readonly DESCRIPTION = '处理文件上传操作';

  constructor(nodeType: string = FileUploadNode.TYPE, name: string = FileUploadNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('file', 'object', '文件对象');
    this.addInput('config', 'object', '上传配置');
    this.addInput('destination', 'string', '目标路径');
    this.addInput('filename', 'string', '文件名');
    this.addInput('metadata', 'object', '文件元数据');

    // 输出端口
    this.addOutput('success', 'boolean', '上传成功');
    this.addOutput('fileInfo', 'object', '文件信息');
    this.addOutput('url', 'string', '文件URL');
    this.addOutput('path', 'string', '文件路径');
    this.addOutput('size', 'number', '文件大小');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('onSuccess', 'trigger', '上传成功事件');
    this.addOutput('onError', 'trigger', '上传错误事件');
  }

  public execute(inputs?: any): any {
    try {
      const file = inputs?.file;
      const config = inputs?.config as UploadConfig;
      const destination = inputs?.destination as string;
      const filename = inputs?.filename as string;
      const metadata = inputs?.metadata || {};

      if (!file) {
        Debug.warn('FileUploadNode', '文件对象为空');
        return this.getErrorOutput('文件对象不能为空');
      }

      // 执行文件上传
      const result = this.uploadFile(file, config, destination, filename, metadata);
      
      Debug.log('FileUploadNode', `文件上传${result.success ? '成功' : '失败'}: ${filename || file.name}`);

      return result;
    } catch (error) {
      Debug.error('FileUploadNode', '文件上传执行失败', error);
      return this.getErrorOutput(error instanceof Error ? error.message : '上传失败');
    }
  }

  private uploadFile(file: any, config: UploadConfig, destination: string, filename: string, metadata: any): any {
    try {
      // 验证文件
      const validation = this.validateFile(file, config);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // 生成文件信息
      const fileId = `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const finalFilename = filename || file.name || `upload_${Date.now()}`;
      const finalDestination = destination || config?.destination || '/uploads';
      const filePath = `${finalDestination}/${finalFilename}`;
      const fileUrl = `https://example.com${filePath}`;

      const fileInfo: FileInfo = {
        id: fileId,
        name: finalFilename,
        path: filePath,
        size: file.size || 0,
        type: this.getFileType(finalFilename),
        mimeType: file.type || 'application/octet-stream',
        hash: this.generateFileHash(file),
        createdAt: new Date(),
        modifiedAt: new Date(),
        metadata: metadata
      };

      return {
        success: true,
        fileInfo: fileInfo,
        url: fileUrl,
        path: filePath,
        size: fileInfo.size,
        errorMessage: null,
        onSuccess: true,
        onError: false
      };
    } catch (error) {
      return this.getErrorOutput(error instanceof Error ? error.message : '上传失败');
    }
  }

  private validateFile(file: any, config: UploadConfig): { valid: boolean; error?: string } {
    if (!file) {
      return { valid: false, error: '文件不能为空' };
    }

    if (config?.maxSize && file.size > config.maxSize) {
      return { valid: false, error: `文件大小超过限制: ${file.size} > ${config.maxSize}` };
    }

    if (config?.allowedTypes && config.allowedTypes.length > 0) {
      const fileType = this.getFileType(file.name);
      if (!config.allowedTypes.includes(fileType)) {
        return { valid: false, error: `不支持的文件类型: ${fileType}` };
      }
    }

    return { valid: true };
  }

  private getFileType(filename: string): string {
    const extension = filename.split('.').pop()?.toLowerCase();
    return extension || 'unknown';
  }

  private generateFileHash(file: any): string {
    // 简化的文件哈希生成
    const content = file.content || file.name || '';
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(16);
  }

  private getErrorOutput(message: string): any {
    return {
      success: false,
      fileInfo: null,
      url: null,
      path: null,
      size: 0,
      errorMessage: message,
      onSuccess: false,
      onError: true
    };
  }
}

/**
 * 文件下载节点
 * 批次2.1 - 文件服务节点
 */
export class FileDownloadNode extends VisualScriptNode {
  public static readonly TYPE = 'FileDownload';
  public static readonly NAME = '文件下载';
  public static readonly DESCRIPTION = '处理文件下载操作';

  constructor(nodeType: string = FileDownloadNode.TYPE, name: string = FileDownloadNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('fileId', 'string', '文件ID');
    this.addInput('filePath', 'string', '文件路径');
    this.addInput('url', 'string', '文件URL');
    this.addInput('range', 'object', '下载范围');
    this.addInput('options', 'object', '下载选项');

    // 输出端口
    this.addOutput('success', 'boolean', '下载成功');
    this.addOutput('fileData', 'any', '文件数据');
    this.addOutput('fileInfo', 'object', '文件信息');
    this.addOutput('downloadUrl', 'string', '下载URL');
    this.addOutput('contentType', 'string', '内容类型');
    this.addOutput('size', 'number', '文件大小');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('onSuccess', 'trigger', '下载成功事件');
    this.addOutput('onError', 'trigger', '下载错误事件');
  }

  public execute(inputs?: any): any {
    try {
      const fileId = inputs?.fileId as string;
      const filePath = inputs?.filePath as string;
      const url = inputs?.url as string;
      const range = inputs?.range;
      const options = inputs?.options || {};

      if (!fileId && !filePath && !url) {
        Debug.warn('FileDownloadNode', '文件标识为空');
        return this.getErrorOutput('必须提供文件ID、路径或URL');
      }

      // 执行文件下载
      const result = this.downloadFile(fileId, filePath, url, range, options);
      
      Debug.log('FileDownloadNode', `文件下载${result.success ? '成功' : '失败'}: ${fileId || filePath || url}`);

      return result;
    } catch (error) {
      Debug.error('FileDownloadNode', '文件下载执行失败', error);
      return this.getErrorOutput(error instanceof Error ? error.message : '下载失败');
    }
  }

  private downloadFile(fileId: string, filePath: string, url: string, range: any, options: any): any {
    try {
      // 确定文件信息
      const fileInfo = this.getFileInfo(fileId, filePath, url);
      if (!fileInfo) {
        throw new Error('文件不存在');
      }

      // 生成下载URL
      const downloadUrl = this.generateDownloadUrl(fileInfo, options);
      
      // 模拟文件数据
      const fileData = this.generateMockFileData(fileInfo, range);

      return {
        success: true,
        fileData: fileData,
        fileInfo: fileInfo,
        downloadUrl: downloadUrl,
        contentType: fileInfo.mimeType,
        size: fileInfo.size,
        errorMessage: null,
        onSuccess: true,
        onError: false
      };
    } catch (error) {
      return this.getErrorOutput(error instanceof Error ? error.message : '下载失败');
    }
  }

  private getFileInfo(fileId: string, filePath: string, url: string): FileInfo | null {
    // 模拟文件信息获取
    const identifier = fileId || filePath || url;
    const filename = this.extractFilename(identifier);
    
    return {
      id: fileId || `file_${Date.now()}`,
      name: filename,
      path: filePath || `/files/${filename}`,
      size: Math.floor(Math.random() * 1000000) + 1000,
      type: this.getFileType(filename),
      mimeType: this.getMimeType(filename),
      hash: this.generateHash(identifier),
      createdAt: new Date(Date.now() - Math.random() * 86400000),
      modifiedAt: new Date()
    };
  }

  private extractFilename(identifier: string): string {
    if (identifier.includes('/')) {
      return identifier.split('/').pop() || 'unknown';
    }
    return identifier || 'download';
  }

  private getFileType(filename: string): string {
    const extension = filename.split('.').pop()?.toLowerCase();
    return extension || 'unknown';
  }

  private getMimeType(filename: string): string {
    const extension = this.getFileType(filename);
    const mimeTypes: { [key: string]: string } = {
      'txt': 'text/plain',
      'pdf': 'application/pdf',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'mp4': 'video/mp4',
      'mp3': 'audio/mpeg',
      'zip': 'application/zip',
      'json': 'application/json'
    };
    return mimeTypes[extension] || 'application/octet-stream';
  }

  private generateDownloadUrl(fileInfo: FileInfo, options: any): string {
    const baseUrl = options.baseUrl || 'https://example.com';
    const token = options.token ? `?token=${options.token}` : '';
    return `${baseUrl}/download/${fileInfo.id}${token}`;
  }

  private generateMockFileData(fileInfo: FileInfo, range: any): any {
    // 模拟文件数据
    if (range) {
      return {
        type: 'partial',
        start: range.start || 0,
        end: range.end || fileInfo.size - 1,
        total: fileInfo.size,
        data: `[Partial file data for ${fileInfo.name}]`
      };
    } else {
      return {
        type: 'full',
        size: fileInfo.size,
        data: `[Full file data for ${fileInfo.name}]`
      };
    }
  }

  private generateHash(input: string): string {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(16);
  }

  private getErrorOutput(message: string): any {
    return {
      success: false,
      fileData: null,
      fileInfo: null,
      downloadUrl: null,
      contentType: null,
      size: 0,
      errorMessage: message,
      onSuccess: false,
      onError: true
    };
  }
}

/**
 * 文件存储节点
 * 批次2.1 - 文件服务节点
 */
export class FileStorageNode extends VisualScriptNode {
  public static readonly TYPE = 'FileStorage';
  public static readonly NAME = '文件存储';
  public static readonly DESCRIPTION = '管理文件存储操作';

  constructor(nodeType: string = FileStorageNode.TYPE, name: string = FileStorageNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('action', 'string', '存储操作');
    this.addInput('fileId', 'string', '文件ID');
    this.addInput('storageType', 'string', '存储类型');
    this.addInput('bucket', 'string', '存储桶');
    this.addInput('path', 'string', '存储路径');
    this.addInput('options', 'object', '存储选项');

    // 输出端口
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('storageInfo', 'object', '存储信息');
    this.addOutput('url', 'string', '访问URL');
    this.addOutput('location', 'string', '存储位置');
    this.addOutput('quota', 'object', '配额信息');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('onSuccess', 'trigger', '操作成功事件');
    this.addOutput('onError', 'trigger', '操作错误事件');
  }

  public execute(inputs?: any): any {
    try {
      const action = inputs?.action as string || 'store';
      const fileId = inputs?.fileId as string;
      const storageType = inputs?.storageType as string || 'local';
      const bucket = inputs?.bucket as string;
      const path = inputs?.path as string;
      const options = inputs?.options || {};

      // 执行文件存储操作
      const result = this.performStorageOperation(action, fileId, storageType, bucket, path, options);

      Debug.log('FileStorageNode', `文件存储${result.success ? '成功' : '失败'}: ${action}`);

      return result;
    } catch (error) {
      Debug.error('FileStorageNode', '文件存储执行失败', error);
      return this.getErrorOutput(error instanceof Error ? error.message : '存储失败');
    }
  }

  private performStorageOperation(action: string, fileId: string, storageType: string, bucket: string, path: string, options: any): any {
    try {
      switch (action) {
        case 'store':
          return this.storeFile(fileId, storageType, bucket, path, options);
        case 'move':
          return this.moveFile(fileId, storageType, bucket, path, options);
        case 'copy':
          return this.copyFile(fileId, storageType, bucket, path, options);
        case 'delete':
          return this.deleteFile(fileId, storageType, bucket, path);
        case 'info':
          return this.getStorageInfo(fileId, storageType, bucket);
        default:
          throw new Error(`不支持的操作: ${action}`);
      }
    } catch (error) {
      return this.getErrorOutput(error instanceof Error ? error.message : '存储操作失败');
    }
  }

  private storeFile(fileId: string, storageType: string, bucket: string, path: string, options: any): any {
    if (!fileId) {
      throw new Error('文件ID不能为空');
    }

    const storageLocation = this.generateStorageLocation(storageType, bucket, path, fileId);
    const accessUrl = this.generateAccessUrl(storageType, storageLocation);

    const storageInfo = {
      fileId: fileId,
      storageType: storageType,
      bucket: bucket,
      path: storageLocation,
      url: accessUrl,
      storedAt: new Date(),
      redundancy: options.redundancy || 'single',
      encryption: options.encryption || false
    };

    const quota = this.getQuotaInfo(storageType, bucket);

    return {
      success: true,
      storageInfo: storageInfo,
      url: accessUrl,
      location: storageLocation,
      quota: quota,
      errorMessage: null,
      onSuccess: true,
      onError: false
    };
  }

  private moveFile(fileId: string, storageType: string, bucket: string, path: string, options: any): any {
    const newLocation = this.generateStorageLocation(storageType, bucket, path, fileId);
    const newUrl = this.generateAccessUrl(storageType, newLocation);

    const storageInfo = {
      fileId: fileId,
      oldLocation: options.oldLocation || 'unknown',
      newLocation: newLocation,
      movedAt: new Date()
    };

    return {
      success: true,
      storageInfo: storageInfo,
      url: newUrl,
      location: newLocation,
      quota: this.getQuotaInfo(storageType, bucket),
      errorMessage: null,
      onSuccess: true,
      onError: false
    };
  }

  private copyFile(fileId: string, storageType: string, bucket: string, path: string, options: any): any {
    const newFileId = `${fileId}_copy_${Date.now()}`;
    const copyLocation = this.generateStorageLocation(storageType, bucket, path, newFileId);
    const copyUrl = this.generateAccessUrl(storageType, copyLocation);

    const storageInfo = {
      originalFileId: fileId,
      copyFileId: newFileId,
      copyLocation: copyLocation,
      copiedAt: new Date()
    };

    return {
      success: true,
      storageInfo: storageInfo,
      url: copyUrl,
      location: copyLocation,
      quota: this.getQuotaInfo(storageType, bucket),
      errorMessage: null,
      onSuccess: true,
      onError: false
    };
  }

  private deleteFile(fileId: string, storageType: string, bucket: string, path: string): any {
    const storageInfo = {
      fileId: fileId,
      storageType: storageType,
      bucket: bucket,
      deletedAt: new Date(),
      permanent: true
    };

    return {
      success: true,
      storageInfo: storageInfo,
      url: null,
      location: null,
      quota: this.getQuotaInfo(storageType, bucket),
      errorMessage: null,
      onSuccess: true,
      onError: false
    };
  }

  private getStorageInfo(fileId: string, storageType: string, bucket: string): any {
    const storageInfo = {
      fileId: fileId,
      storageType: storageType,
      bucket: bucket,
      exists: true,
      size: Math.floor(Math.random() * 1000000) + 1000,
      lastAccessed: new Date(),
      replicas: 2
    };

    return {
      success: true,
      storageInfo: storageInfo,
      url: this.generateAccessUrl(storageType, `${bucket}/${fileId}`),
      location: `${bucket}/${fileId}`,
      quota: this.getQuotaInfo(storageType, bucket),
      errorMessage: null,
      onSuccess: true,
      onError: false
    };
  }

  private generateStorageLocation(storageType: string, bucket: string, path: string, fileId: string): string {
    const basePath = path || 'files';
    switch (storageType) {
      case 'local':
        return `/storage/${bucket}/${basePath}/${fileId}`;
      case 's3':
        return `s3://${bucket}/${basePath}/${fileId}`;
      case 'azure':
        return `azure://${bucket}/${basePath}/${fileId}`;
      case 'gcs':
        return `gs://${bucket}/${basePath}/${fileId}`;
      default:
        return `${storageType}://${bucket}/${basePath}/${fileId}`;
    }
  }

  private generateAccessUrl(storageType: string, location: string): string {
    switch (storageType) {
      case 'local':
        return `https://localhost/files${location}`;
      case 's3':
        return `https://s3.amazonaws.com${location.replace('s3://', '/')}`;
      case 'azure':
        return `https://storage.azure.com${location.replace('azure://', '/')}`;
      case 'gcs':
        return `https://storage.googleapis.com${location.replace('gs://', '/')}`;
      default:
        return `https://storage.example.com${location}`;
    }
  }

  private getQuotaInfo(storageType: string, bucket: string): any {
    return {
      used: Math.floor(Math.random() * 1000000000) + 100000000, // 100MB - 1GB
      total: 10000000000, // 10GB
      available: 9000000000, // 9GB
      files: Math.floor(Math.random() * 10000) + 1000,
      maxFiles: 100000
    };
  }

  private getErrorOutput(message: string): any {
    return {
      success: false,
      storageInfo: null,
      url: null,
      location: null,
      quota: null,
      errorMessage: message,
      onSuccess: false,
      onError: true
    };
  }
}
