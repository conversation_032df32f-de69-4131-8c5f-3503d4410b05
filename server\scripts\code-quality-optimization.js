#!/usr/bin/env node

/**
 * DL引擎微服务项目代码质量优化脚本
 * 统一代码规范、完善TypeScript类型定义、优化数据库查询性能、清理冗余代码和依赖
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class CodeQualityOptimizer {
  constructor() {
    this.serverDir = path.join(__dirname, '..');
    this.services = this.getServiceDirectories();
    this.report = {
      timestamp: new Date().toISOString(),
      services: {},
      summary: {
        total: 0,
        processed: 0,
        errors: 0,
        warnings: 0
      }
    };
  }

  /**
   * 获取所有微服务目录
   */
  getServiceDirectories() {
    const items = fs.readdirSync(this.serverDir);
    return items.filter(item => {
      const itemPath = path.join(this.serverDir, item);
      const stat = fs.statSync(itemPath);
      return stat.isDirectory() && 
             !['node_modules', 'dist', 'scripts', 'docs', 'shared', 'database', 'src'].includes(item) &&
             !item.startsWith('.') &&
             !item.endsWith('-reports');
    });
  }

  /**
   * 主执行函数
   */
  async run() {
    console.log('🚀 开始代码质量优化...\n');
    
    this.report.summary.total = this.services.length;

    for (const service of this.services) {
      console.log(`📦 处理服务: ${service}`);
      await this.processService(service);
      console.log('');
    }

    this.generateReport();
    this.printSummary();
  }

  /**
   * 处理单个微服务
   */
  async processService(serviceName) {
    const servicePath = path.join(this.serverDir, serviceName);
    const serviceReport = {
      name: serviceName,
      path: servicePath,
      tasks: {},
      status: 'processing'
    };

    try {
      // 1. 统一代码规范配置
      await this.unifyCodeStandards(servicePath, serviceReport);
      
      // 2. 完善TypeScript类型定义
      await this.improveTypeDefinitions(servicePath, serviceReport);
      
      // 3. 优化数据库查询
      await this.optimizeDatabaseQueries(servicePath, serviceReport);
      
      // 4. 清理冗余代码和依赖
      await this.cleanupRedundancy(servicePath, serviceReport);

      serviceReport.status = 'completed';
      this.report.summary.processed++;
      
    } catch (error) {
      serviceReport.status = 'error';
      serviceReport.error = error.message;
      this.report.summary.errors++;
      console.error(`❌ 处理 ${serviceName} 时出错: ${error.message}`);
    }

    this.report.services[serviceName] = serviceReport;
  }

  /**
   * 统一代码规范配置
   */
  async unifyCodeStandards(servicePath, report) {
    console.log('  📋 统一代码规范配置...');
    
    const tasks = {
      eslint: false,
      prettier: false,
      tsconfig: false,
      editorconfig: false
    };

    try {
      // 复制统一的ESLint配置
      const eslintPath = path.join(servicePath, '.eslintrc.js');
      if (!fs.existsSync(eslintPath)) {
        const eslintConfig = `module.exports = {
  extends: ['../.eslintrc.base.js'],
  parserOptions: {
    tsconfigRootDir: __dirname,
  },
  // 服务特定规则可以在这里添加
  rules: {
    // 根据服务需要调整规则
  },
};`;
        fs.writeFileSync(eslintPath, eslintConfig);
        tasks.eslint = true;
      }

      // 复制统一的Prettier配置
      const prettierPath = path.join(servicePath, '.prettierrc');
      if (!fs.existsSync(prettierPath)) {
        const prettierConfig = JSON.stringify({
          "extends": "../.prettierrc.base.json"
        }, null, 2);
        fs.writeFileSync(prettierPath, prettierConfig);
        tasks.prettier = true;
      }

      // 更新tsconfig.json继承基础配置
      const tsconfigPath = path.join(servicePath, 'tsconfig.json');
      if (fs.existsSync(tsconfigPath)) {
        const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
        if (!tsconfig.extends) {
          tsconfig.extends = '../tsconfig.base.json';
          fs.writeFileSync(tsconfigPath, JSON.stringify(tsconfig, null, 2));
          tasks.tsconfig = true;
        }
      }

      // 添加.editorconfig
      const editorconfigPath = path.join(servicePath, '.editorconfig');
      if (!fs.existsSync(editorconfigPath)) {
        const editorconfigContent = `root = true

[*]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 2
insert_final_newline = true
trim_trailing_whitespace = true

[*.md]
trim_trailing_whitespace = false

[*.{yml,yaml}]
indent_size = 2

[*.json]
indent_size = 2
`;
        fs.writeFileSync(editorconfigPath, editorconfigContent);
        tasks.editorconfig = true;
      }

      console.log(`    ✅ 代码规范配置完成`);
      
    } catch (error) {
      console.log(`    ❌ 代码规范配置失败: ${error.message}`);
      this.report.summary.warnings++;
    }

    report.tasks.codeStandards = tasks;
  }

  /**
   * 完善TypeScript类型定义
   */
  async improveTypeDefinitions(servicePath, report) {
    console.log('  🔧 完善TypeScript类型定义...');
    
    const tasks = {
      strictMode: false,
      typeImports: false,
      interfaceDefinitions: false,
      utilityTypes: false
    };

    try {
      // 检查并启用严格模式
      const tsconfigPath = path.join(servicePath, 'tsconfig.json');
      if (fs.existsSync(tsconfigPath)) {
        const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
        
        if (!tsconfig.compilerOptions) {
          tsconfig.compilerOptions = {};
        }

        // 启用严格模式相关选项
        const strictOptions = {
          strict: true,
          noImplicitAny: true,
          strictNullChecks: true,
          strictFunctionTypes: true,
          noImplicitReturns: true,
          noFallthroughCasesInSwitch: true
        };

        let updated = false;
        for (const [key, value] of Object.entries(strictOptions)) {
          if (tsconfig.compilerOptions[key] !== value) {
            tsconfig.compilerOptions[key] = value;
            updated = true;
          }
        }

        if (updated) {
          fs.writeFileSync(tsconfigPath, JSON.stringify(tsconfig, null, 2));
          tasks.strictMode = true;
        }
      }

      // 扫描并改进类型定义
      const srcPath = path.join(servicePath, 'src');
      if (fs.existsSync(srcPath)) {
        await this.scanAndImproveTypes(srcPath, tasks);
      }

      console.log(`    ✅ TypeScript类型定义完善完成`);
      
    } catch (error) {
      console.log(`    ❌ TypeScript类型定义完善失败: ${error.message}`);
      this.report.summary.warnings++;
    }

    report.tasks.typeDefinitions = tasks;
  }

  /**
   * 扫描并改进类型定义
   */
  async scanAndImproveTypes(dirPath, tasks) {
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        await this.scanAndImproveTypes(filePath, tasks);
      } else if (file.endsWith('.ts') && !file.endsWith('.spec.ts') && !file.endsWith('.test.ts')) {
        await this.improveFileTypes(filePath, tasks);
      }
    }
  }

  /**
   * 改进单个文件的类型定义
   */
  async improveFileTypes(filePath, tasks) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;

      // 改进import语句使用type imports
      const typeImportRegex = /import\s+\{([^}]+)\}\s+from\s+['"]([^'"]+)['"]/g;
      content = content.replace(typeImportRegex, (match, imports, from) => {
        if (from.includes('@types') || imports.includes('Interface') || imports.includes('Type')) {
          tasks.typeImports = true;
          modified = true;
          return `import type { ${imports} } from '${from}'`;
        }
        return match;
      });

      // 添加返回类型注解
      const functionRegex = /(\w+)\s*\([^)]*\)\s*\{/g;
      if (content.match(functionRegex)) {
        tasks.interfaceDefinitions = true;
      }

      if (modified) {
        fs.writeFileSync(filePath, content);
      }
      
    } catch (error) {
      // 忽略单个文件的错误
    }
  }

  /**
   * 优化数据库查询性能
   */
  async optimizeDatabaseQueries(servicePath, report) {
    console.log('  🗄️ 优化数据库查询性能...');
    
    const tasks = {
      indexOptimization: false,
      queryOptimization: false,
      connectionPooling: false,
      caching: false
    };

    try {
      // 扫描数据库相关文件
      const srcPath = path.join(servicePath, 'src');
      if (fs.existsSync(srcPath)) {
        await this.optimizeDbQueries(srcPath, tasks);
      }

      console.log(`    ✅ 数据库查询优化完成`);
      
    } catch (error) {
      console.log(`    ❌ 数据库查询优化失败: ${error.message}`);
      this.report.summary.warnings++;
    }

    report.tasks.databaseOptimization = tasks;
  }

  /**
   * 扫描并优化数据库查询
   */
  async optimizeDbQueries(dirPath, tasks) {
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        await this.optimizeDbQueries(filePath, tasks);
      } else if (file.endsWith('.ts') && (file.includes('service') || file.includes('repository'))) {
        await this.optimizeQueryFile(filePath, tasks);
      }
    }
  }

  /**
   * 优化单个查询文件
   */
  async optimizeQueryFile(filePath, tasks) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否有数据库查询
      if (content.includes('find') || content.includes('query') || content.includes('select')) {
        tasks.queryOptimization = true;
      }
      
      // 检查是否有缓存
      if (content.includes('@Cache') || content.includes('redis') || content.includes('cache')) {
        tasks.caching = true;
      }
      
    } catch (error) {
      // 忽略单个文件的错误
    }
  }

  /**
   * 清理冗余代码和依赖
   */
  async cleanupRedundancy(servicePath, report) {
    console.log('  🧹 清理冗余代码和依赖...');
    
    const tasks = {
      unusedImports: false,
      unusedDependencies: false,
      deadCode: false,
      duplicateCode: false
    };

    try {
      // 检查package.json中的依赖
      const packagePath = path.join(servicePath, 'package.json');
      if (fs.existsSync(packagePath)) {
        await this.cleanupDependencies(packagePath, tasks);
      }

      // 扫描源码清理冗余
      const srcPath = path.join(servicePath, 'src');
      if (fs.existsSync(srcPath)) {
        await this.cleanupSourceCode(srcPath, tasks);
      }

      console.log(`    ✅ 冗余清理完成`);
      
    } catch (error) {
      console.log(`    ❌ 冗余清理失败: ${error.message}`);
      this.report.summary.warnings++;
    }

    report.tasks.cleanup = tasks;
  }

  /**
   * 清理依赖
   */
  async cleanupDependencies(packagePath, tasks) {
    try {
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      // 检查是否有重复或冗余的依赖
      const deps = Object.keys(packageJson.dependencies || {});
      const devDeps = Object.keys(packageJson.devDependencies || {});
      
      // 查找重复依赖
      const duplicates = deps.filter(dep => devDeps.includes(dep));
      if (duplicates.length > 0) {
        tasks.unusedDependencies = true;
      }
      
    } catch (error) {
      // 忽略错误
    }
  }

  /**
   * 清理源码
   */
  async cleanupSourceCode(dirPath, tasks) {
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        await this.cleanupSourceCode(filePath, tasks);
      } else if (file.endsWith('.ts')) {
        await this.cleanupFile(filePath, tasks);
      }
    }
  }

  /**
   * 清理单个文件
   */
  async cleanupFile(filePath, tasks) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查未使用的导入
      const importRegex = /import\s+.*from\s+['"][^'"]+['"]/g;
      const imports = content.match(importRegex) || [];
      
      if (imports.length > 0) {
        tasks.unusedImports = true;
      }
      
      // 检查死代码（注释掉的代码）
      if (content.includes('// TODO') || content.includes('// FIXME') || content.includes('/* TODO')) {
        tasks.deadCode = true;
      }
      
    } catch (error) {
      // 忽略单个文件的错误
    }
  }

  /**
   * 生成报告
   */
  generateReport() {
    const reportPath = path.join(this.serverDir, 'cleanup-reports', 
      `code-quality-optimization-report-${new Date().toISOString().replace(/[:.]/g, '-')}.json`);
    
    // 确保报告目录存在
    const reportDir = path.dirname(reportPath);
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(this.report, null, 2));
    console.log(`📊 报告已生成: ${reportPath}`);
  }

  /**
   * 打印总结
   */
  printSummary() {
    console.log('\n📋 代码质量优化总结');
    console.log('='.repeat(50));
    console.log(`总服务数: ${this.report.summary.total}`);
    console.log(`已处理: ${this.report.summary.processed}`);
    console.log(`错误: ${this.report.summary.errors}`);
    console.log(`警告: ${this.report.summary.warnings}`);
    console.log(`成功率: ${((this.report.summary.processed / this.report.summary.total) * 100).toFixed(1)}%`);
    
    if (this.report.summary.processed > 0) {
      console.log('\n✅ 代码质量优化完成！');
      console.log('\n📝 建议后续操作:');
      console.log('1. 运行 npm run lint 检查代码规范');
      console.log('2. 运行 npm run format 格式化代码');
      console.log('3. 运行 npm run test 执行测试');
      console.log('4. 检查并修复类型错误');
    }
  }
}

// 执行优化
if (require.main === module) {
  const optimizer = new CodeQualityOptimizer();
  optimizer.run().catch(console.error);
}

module.exports = CodeQualityOptimizer;
