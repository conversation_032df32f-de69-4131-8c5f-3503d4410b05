/**
 * 区块链控制器
 */

import { Controller, Get, Post, Put, Delete, Body, Param, Query, HttpStatus, HttpException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam } from '@nestjs/swagger';
import { BlockchainService } from './blockchain.service';
import { SmartContractService } from './services/smart-contract.service';
import { DigitalAssetService } from './services/digital-asset.service';
import { ConsensusService } from './services/consensus.service';
import { DeFiService } from './services/defi.service';
import { Web3Service } from './services/web3.service';

@ApiTags('Blockchain')
@Controller('blockchain')
export class BlockchainController {
  constructor(
    private readonly blockchainService: BlockchainService,
    private readonly smartContractService: SmartContractService,
    private readonly digitalAssetService: DigitalAssetService,
    private readonly consensusService: ConsensusService,
    private readonly defiService: DeFiService,
    private readonly web3Service: Web3Service
  ) {}

  // 网络和基础功能
  @Get('networks/:blockchain')
  @ApiOperation({ summary: '获取网络信息' })
  @ApiParam({ name: 'blockchain', description: '区块链网络名称' })
  @ApiResponse({ status: 200, description: '网络信息获取成功' })
  async getNetworkInfo(@Param('blockchain') blockchain: string) {
    try {
      return {
        success: true,
        data: await this.blockchainService.getNetworkInfo(blockchain),
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('transactions/:hash')
  @ApiOperation({ summary: '获取交易信息' })
  @ApiParam({ name: 'hash', description: '交易哈希' })
  @ApiResponse({ status: 200, description: '交易信息获取成功' })
  async getTransaction(@Param('hash') hash: string) {
    try {
      return {
        success: true,
        data: await this.blockchainService.getTransaction(hash),
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('network/status')
  @ApiOperation({ summary: '获取当前网络状态' })
  @ApiResponse({ status: 200, description: '网络状态获取成功' })
  async getNetworkStatus() {
    try {
      return {
        success: true,
        data: await this.web3Service.getNetworkInfo(),
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // 智能合约相关API
  @Post('contracts/deploy')
  @ApiOperation({ summary: '部署智能合约' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        symbol: { type: 'string' },
        bytecode: { type: 'string' },
        abi: { type: 'array' },
        constructorArgs: { type: 'array' },
        deployerAddress: { type: 'string' },
        privateKey: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 201, description: '合约部署成功' })
  async deployContract(@Body() deployConfig: any) {
    try {
      const contract = await this.smartContractService.deployContract(
        deployConfig,
        deployConfig.deployerAddress,
        deployConfig.privateKey
      );

      return {
        success: true,
        data: contract,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('contracts/:address/call')
  @ApiOperation({ summary: '调用智能合约方法' })
  @ApiParam({ name: 'address', description: '合约地址' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        methodName: { type: 'string' },
        methodArgs: { type: 'array' },
        fromAddress: { type: 'string' },
        privateKey: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 200, description: '合约方法调用成功' })
  async callContractMethod(
    @Param('address') address: string,
    @Body() callConfig: any
  ) {
    try {
      const result = await this.smartContractService.callContractMethod({
        contractAddress: address,
        methodName: callConfig.methodName,
        methodArgs: callConfig.methodArgs,
        fromAddress: callConfig.fromAddress
      }, callConfig.privateKey);

      return {
        success: true,
        data: result,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('contracts')
  @ApiOperation({ summary: '获取智能合约列表' })
  @ApiResponse({ status: 200, description: '合约列表获取成功' })
  async getContracts(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('status') status?: string
  ) {
    try {
      const pageNum = page ? parseInt(page) : 1;
      const limitNum = limit ? parseInt(limit) : 20;
      const filters = { status };

      const result = await this.smartContractService.getContracts(pageNum, limitNum, filters);

      return {
        success: true,
        data: result.contracts,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: result.total,
          pages: Math.ceil(result.total / limitNum)
        },
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // 数字资产相关API
  @Post('assets/create')
  @ApiOperation({ summary: '创建数字资产' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        symbol: { type: 'string' },
        totalSupply: { type: 'string' },
        decimals: { type: 'number' },
        assetType: { type: 'string', enum: ['token', 'nft', 'security', 'utility', 'governance'] },
        creatorAddress: { type: 'string' },
        privateKey: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 201, description: '数字资产创建成功' })
  async createAsset(@Body() assetConfig: any) {
    try {
      const asset = await this.digitalAssetService.createAsset(
        assetConfig,
        assetConfig.creatorAddress,
        assetConfig.privateKey
      );

      return {
        success: true,
        data: asset,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('assets/:assetId/transfer')
  @ApiOperation({ summary: '转移数字资产' })
  @ApiParam({ name: 'assetId', description: '资产ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        fromAddress: { type: 'string' },
        toAddress: { type: 'string' },
        amount: { type: 'string' },
        privateKey: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 200, description: '资产转移成功' })
  async transferAsset(
    @Param('assetId') assetId: string,
    @Body() transferConfig: any
  ) {
    try {
      const txHash = await this.digitalAssetService.transferAsset({
        assetId,
        fromAddress: transferConfig.fromAddress,
        toAddress: transferConfig.toAddress,
        amount: transferConfig.amount
      }, transferConfig.privateKey);

      return {
        success: true,
        data: { transactionHash: txHash },
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('assets')
  @ApiOperation({ summary: '获取数字资产列表' })
  @ApiResponse({ status: 200, description: '资产列表获取成功' })
  async getAssets(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('assetType') assetType?: string
  ) {
    try {
      const pageNum = page ? parseInt(page) : 1;
      const limitNum = limit ? parseInt(limit) : 20;
      const filters = { assetType };

      const result = await this.digitalAssetService.getAssets(pageNum, limitNum, filters);

      return {
        success: true,
        data: result.assets,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: result.total,
          pages: Math.ceil(result.total / limitNum)
        },
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('assets/:assetId/balance/:address')
  @ApiOperation({ summary: '获取资产余额' })
  @ApiParam({ name: 'assetId', description: '资产ID' })
  @ApiParam({ name: 'address', description: '钱包地址' })
  @ApiResponse({ status: 200, description: '余额获取成功' })
  async getAssetBalance(
    @Param('assetId') assetId: string,
    @Param('address') address: string
  ) {
    try {
      const balance = await this.digitalAssetService.getAssetBalance(assetId, address);

      return {
        success: true,
        data: { balance },
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // DeFi相关API
  @Post('defi/pools/create')
  @ApiOperation({ summary: '创建流动性池' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        tokenA: { type: 'string' },
        tokenB: { type: 'string' },
        feeRate: { type: 'number' },
        creatorAddress: { type: 'string' },
        privateKey: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 201, description: '流动性池创建成功' })
  async createLiquidityPool(@Body() poolConfig: any) {
    try {
      const poolId = await this.defiService.createLiquidityPool(
        poolConfig,
        poolConfig.creatorAddress,
        poolConfig.privateKey
      );

      return {
        success: true,
        data: { poolId },
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('defi/pools/:poolId/add-liquidity')
  @ApiOperation({ summary: '添加流动性' })
  @ApiParam({ name: 'poolId', description: '流动性池ID' })
  @ApiResponse({ status: 200, description: '流动性添加成功' })
  async addLiquidity(
    @Param('poolId') poolId: string,
    @Body() liquidityConfig: any
  ) {
    try {
      const txHash = await this.defiService.addLiquidity({
        poolId,
        ...liquidityConfig
      }, liquidityConfig.privateKey);

      return {
        success: true,
        data: { transactionHash: txHash },
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('defi/pools')
  @ApiOperation({ summary: '获取流动性池列表' })
  @ApiResponse({ status: 200, description: '流动性池列表获取成功' })
  async getLiquidityPools() {
    try {
      const pools = await this.defiService.getLiquidityPools();

      return {
        success: true,
        data: pools,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // 共识机制相关API
  @Post('consensus/validators/register')
  @ApiOperation({ summary: '注册验证者' })
  @ApiResponse({ status: 201, description: '验证者注册成功' })
  async registerValidator(@Body() validatorConfig: any) {
    try {
      const txHash = await this.consensusService.registerValidator(
        validatorConfig.validatorAddress,
        validatorConfig.stakeAmount,
        validatorConfig.commission,
        validatorConfig.description,
        validatorConfig.privateKey
      );

      return {
        success: true,
        data: { transactionHash: txHash },
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('consensus/validators')
  @ApiOperation({ summary: '获取验证者列表' })
  @ApiResponse({ status: 200, description: '验证者列表获取成功' })
  async getValidators() {
    try {
      const validators = await this.consensusService.getValidators();

      return {
        success: true,
        data: validators,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('consensus/network/status')
  @ApiOperation({ summary: '获取网络共识状态' })
  @ApiResponse({ status: 200, description: '网络状态获取成功' })
  async getConsensusNetworkStatus() {
    try {
      const status = await this.consensusService.getNetworkStatus();

      return {
        success: true,
        data: status,
        timestamp: new Date()
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
