/**
 * 区块链服务主入口
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // 全局拦截器
  app.useGlobalInterceptors(new LoggingInterceptor());

  // 全局异常过滤器
  app.useGlobalFilters(new HttpExceptionFilter());

  // CORS配置
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
  });

  // API前缀
  app.setGlobalPrefix('api/v1');

  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('智慧工厂区块链服务API')
    .setDescription('提供智能合约管理、数字资产管理、共识机制和DeFi功能的区块链服务')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('Blockchain', '区块链基础功能')
    .addTag('SmartContracts', '智能合约管理')
    .addTag('DigitalAssets', '数字资产管理')
    .addTag('Consensus', '共识机制')
    .addTag('DeFi', '去中心化金融')
    .addTag('Web3', 'Web3工具')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  // 健康检查端点
  app.getHttpAdapter().get('/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'blockchain-service',
      version: '1.0.0',
    });
  });

  const port = configService.get('PORT', 3006);
  await app.listen(port);

  console.log(`🚀 智慧工厂区块链服务已启动`);
  console.log(`🔗 服务端口: ${port}`);
  console.log(`📚 API文档: http://localhost:${port}/api/docs`);
  console.log(`❤️ 健康检查: http://localhost:${port}/health`);
  console.log(`⛓️ 支持功能: 智能合约、数字资产、共识机制、DeFi`);
}

bootstrap().catch((error) => {
  console.error('启动区块链服务失败:', error);
  process.exit(1);
});
