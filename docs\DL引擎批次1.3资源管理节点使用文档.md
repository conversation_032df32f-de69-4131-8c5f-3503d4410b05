# DL引擎批次1.3资源管理节点使用文档

## 概述

本文档详细介绍DL引擎视觉脚本系统批次1.3开发的22个资源管理节点，包括12个资源加载节点和10个资源优化节点。这些节点为开发者提供了完整的资源管理解决方案，支持在编辑器中通过可视化节点进行应用系统开发。

## 节点分类

### 📁 资源加载节点 (12个)

#### 1. LoadAssetNode - 加载资源
**功能描述：** 加载指定的资源文件

**输入端口：**
- `execute` (trigger): 执行加载
- `resourceId` (string): 资源ID
- `url` (string): 资源URL
- `type` (string): 资源类型
- `priority` (number): 优先级 (0-200)
- `timeout` (number): 超时时间(ms)
- `cache` (boolean): 启用缓存

**输出端口：**
- `resource` (object): 资源对象
- `resourceId` (string): 资源ID
- `onLoaded` (trigger): 加载完成
- `onError` (trigger): 加载失败
- `progress` (number): 加载进度

**使用示例：**
```
输入：
- url: "https://example.com/texture.jpg"
- type: "texture"
- priority: 50

输出：
- resource: 纹理对象
- progress: 100
- onLoaded: true
```

#### 2. UnloadAssetNode - 卸载资源
**功能描述：** 卸载指定的资源，释放内存

**输入端口：**
- `execute` (trigger): 执行卸载
- `resourceId` (string): 资源ID
- `force` (boolean): 强制卸载

**输出端口：**
- `success` (boolean): 卸载成功
- `resourceId` (string): 资源ID
- `onUnloaded` (trigger): 卸载完成
- `onError` (trigger): 卸载失败

#### 3. PreloadAssetNode - 预加载资源
**功能描述：** 预加载资源到缓存中，提高后续访问速度

**输入端口：**
- `execute` (trigger): 执行预加载
- `resourceIds` (array): 资源ID列表
- `urls` (array): URL列表
- `types` (array): 类型列表
- `priority` (number): 优先级

**输出端口：**
- `loadedCount` (number): 已加载数量
- `totalCount` (number): 总数量
- `progress` (number): 总进度
- `onCompleted` (trigger): 预加载完成
- `onProgress` (trigger): 进度更新
- `onError` (trigger): 预加载失败

#### 4. AsyncLoadAssetNode - 异步加载资源
**功能描述：** 异步加载资源，不阻塞主线程

**输入端口：**
- `execute` (trigger): 执行异步加载
- `resourceId` (string): 资源ID
- `url` (string): 资源URL
- `type` (string): 资源类型
- `priority` (number): 优先级
- `callback` (function): 回调函数

**输出端口：**
- `loadingId` (string): 加载ID
- `onStarted` (trigger): 开始加载
- `onProgress` (trigger): 进度更新
- `onCompleted` (trigger): 加载完成
- `onError` (trigger): 加载失败

#### 5. LoadAssetBundleNode - 加载资源包
**功能描述：** 加载包含多个资源的资源包

**输入端口：**
- `execute` (trigger): 执行加载
- `bundleId` (string): 资源包ID
- `bundleUrl` (string): 资源包URL
- `manifest` (object): 资源清单
- `priority` (number): 优先级

**输出端口：**
- `bundle` (object): 资源包对象
- `bundleId` (string): 资源包ID
- `loadedAssets` (array): 已加载资源列表
- `progress` (number): 加载进度
- `onLoaded` (trigger): 加载完成
- `onError` (trigger): 加载失败

#### 6. AssetDependencyNode - 资源依赖
**功能描述：** 管理资源之间的依赖关系

**输入端口：**
- `execute` (trigger): 执行依赖解析
- `resourceId` (string): 资源ID
- `dependencies` (array): 依赖资源列表
- `autoLoad` (boolean): 自动加载依赖

**输出端口：**
- `dependencyTree` (object): 依赖树
- `loadOrder` (array): 加载顺序
- `onResolved` (trigger): 依赖解析完成
- `onError` (trigger): 依赖解析失败

#### 7. AssetCacheNode - 资源缓存
**功能描述：** 管理资源缓存策略和缓存状态

**输入端口：**
- `execute` (trigger): 执行缓存操作
- `operation` (string): 操作类型 (info/clear/setSize/setTTL/optimize)
- `resourceId` (string): 资源ID
- `cacheSize` (number): 缓存大小限制
- `ttl` (number): 缓存生存时间

**输出端口：**
- `cacheInfo` (object): 缓存信息
- `cacheSize` (number): 当前缓存大小
- `hitRate` (number): 缓存命中率
- `onSuccess` (trigger): 操作成功
- `onError` (trigger): 操作失败

#### 8. AssetCompressionNode - 资源压缩
**功能描述：** 压缩和解压缩资源数据

**输入端口：**
- `execute` (trigger): 执行压缩操作
- `operation` (string): 操作类型 (compress/decompress/analyze)
- `resourceId` (string): 资源ID
- `data` (object): 数据
- `compressionLevel` (number): 压缩级别 (1-10)
- `algorithm` (string): 压缩算法

**输出端口：**
- `result` (object): 处理结果
- `originalSize` (number): 原始大小
- `compressedSize` (number): 压缩后大小
- `compressionRatio` (number): 压缩比
- `onSuccess` (trigger): 操作成功
- `onError` (trigger): 操作失败

#### 9. AssetEncryptionNode - 资源加密
**功能描述：** 加密和解密资源数据

**输入端口：**
- `execute` (trigger): 执行加密操作
- `operation` (string): 操作类型 (encrypt/decrypt/generateKey)
- `data` (object): 数据
- `key` (string): 加密密钥
- `algorithm` (string): 加密算法

**输出端口：**
- `result` (object): 处理结果
- `encrypted` (boolean): 是否已加密
- `algorithm` (string): 使用的算法
- `onSuccess` (trigger): 操作成功
- `onError` (trigger): 操作失败

#### 10. AssetValidationNode - 资源验证
**功能描述：** 验证资源的完整性和有效性

**输入端口：**
- `execute` (trigger): 执行验证
- `resourceId` (string): 资源ID
- `data` (object): 资源数据
- `checksum` (string): 校验和
- `schema` (object): 验证模式

**输出端口：**
- `valid` (boolean): 验证结果
- `errors` (array): 错误列表
- `checksum` (string): 计算的校验和
- `onValid` (trigger): 验证通过
- `onInvalid` (trigger): 验证失败

#### 11. AssetMetadataNode - 资源元数据
**功能描述：** 管理资源的元数据信息

**输入端口：**
- `execute` (trigger): 执行元数据操作
- `operation` (string): 操作类型 (get/set/update/delete/list)
- `resourceId` (string): 资源ID
- `metadata` (object): 元数据
- `key` (string): 元数据键
- `value` (object): 元数据值

**输出端口：**
- `metadata` (object): 元数据对象
- `value` (object): 元数据值
- `keys` (array): 元数据键列表
- `onSuccess` (trigger): 操作成功
- `onError` (trigger): 操作失败

#### 12. AssetVersionNode - 资源版本
**功能描述：** 管理资源的版本控制

**输入端口：**
- `execute` (trigger): 执行版本操作
- `operation` (string): 操作类型 (get/set/list/compare/rollback)
- `resourceId` (string): 资源ID
- `version` (string): 版本号
- `description` (string): 版本描述

**输出端口：**
- `currentVersion` (string): 当前版本
- `versions` (array): 版本列表
- `versionInfo` (object): 版本信息
- `onSuccess` (trigger): 操作成功
- `onError` (trigger): 操作失败

### ⚡ 资源优化节点 (10个)

#### 1. AssetOptimizationNode - 资源优化
**功能描述：** 优化资源以提高性能和减少内存使用

**输入端口：**
- `execute` (trigger): 执行优化
- `resourceId` (string): 资源ID
- `optimizationType` (string): 优化类型
- `quality` (number): 质量级别 (0.0-1.0)
- `targetSize` (number): 目标大小

**输出端口：**
- `optimizedResource` (object): 优化后的资源
- `originalSize` (number): 原始大小
- `optimizedSize` (number): 优化后大小
- `compressionRatio` (number): 压缩比
- `onOptimized` (trigger): 优化完成
- `onError` (trigger): 优化失败

#### 2. TextureCompressionNode - 纹理压缩
**功能描述：** 压缩纹理以减少内存使用和提高加载速度

**输入端口：**
- `execute` (trigger): 执行压缩
- `texture` (object): 纹理对象
- `format` (string): 压缩格式 (DXT1/DXT5/ETC1/ETC2/ASTC/PVRTC)
- `quality` (number): 压缩质量 (0.0-1.0)
- `generateMipmaps` (boolean): 生成Mipmap

**输出端口：**
- `compressedTexture` (object): 压缩后的纹理
- `originalSize` (number): 原始大小
- `compressedSize` (number): 压缩后大小
- `compressionRatio` (number): 压缩比
- `onCompressed` (trigger): 压缩完成
- `onError` (trigger): 压缩失败

#### 3. MeshOptimizationNode - 网格优化
**功能描述：** 优化3D网格以减少顶点数和提高渲染性能

**输入端口：**
- `execute` (trigger): 执行优化
- `mesh` (object): 网格对象
- `optimizationType` (string): 优化类型 (simplify/decimate/remesh/weld)
- `targetVertexCount` (number): 目标顶点数
- `preserveUVs` (boolean): 保留UV坐标
- `preserveNormals` (boolean): 保留法线

**输出端口：**
- `optimizedMesh` (object): 优化后的网格
- `originalVertexCount` (number): 原始顶点数
- `optimizedVertexCount` (number): 优化后顶点数
- `reductionRatio` (number): 减少比例
- `onOptimized` (trigger): 优化完成
- `onError` (trigger): 优化失败

#### 4. AudioCompressionNode - 音频压缩
**功能描述：** 压缩音频文件以减少文件大小

**输入端口：**
- `execute` (trigger): 执行压缩
- `audio` (object): 音频对象
- `format` (string): 压缩格式 (mp3/aac/ogg/flac/wav)
- `bitrate` (number): 比特率
- `sampleRate` (number): 采样率
- `channels` (number): 声道数

**输出端口：**
- `compressedAudio` (object): 压缩后的音频
- `originalSize` (number): 原始大小
- `compressedSize` (number): 压缩后大小
- `compressionRatio` (number): 压缩比
- `onCompressed` (trigger): 压缩完成
- `onError` (trigger): 压缩失败

#### 5. AssetBatchingNode - 资源批处理
**功能描述：** 批量处理多个资源以提高效率

**输入端口：**
- `execute` (trigger): 执行批处理
- `resources` (array): 资源列表
- `operation` (string): 批处理操作 (optimize/compress/validate/convert)
- `batchSize` (number): 批次大小
- `parallel` (boolean): 并行处理

**输出端口：**
- `results` (array): 处理结果
- `successCount` (number): 成功数量
- `failureCount` (number): 失败数量
- `progress` (number): 处理进度
- `onCompleted` (trigger): 批处理完成
- `onError` (trigger): 批处理失败

#### 6. AssetStreamingNode - 资源流式传输
**功能描述：** 实现资源的流式加载和传输

**输入端口：**
- `execute` (trigger): 执行流式传输
- `resourceUrl` (string): 资源URL
- `chunkSize` (number): 块大小
- `bufferSize` (number): 缓冲区大小
- `autoStart` (boolean): 自动开始

**输出端口：**
- `stream` (object): 流对象
- `progress` (number): 传输进度
- `bytesLoaded` (number): 已加载字节数
- `totalBytes` (number): 总字节数
- `onProgress` (trigger): 进度更新
- `onCompleted` (trigger): 传输完成
- `onError` (trigger): 传输失败

#### 7. AssetMemoryManagementNode - 资源内存管理
**功能描述：** 管理资源的内存使用和释放

**输入端口：**
- `execute` (trigger): 执行内存管理
- `operation` (string): 操作类型 (status/cleanup/setLimit/optimize/monitor)
- `resourceId` (string): 资源ID
- `memoryLimit` (number): 内存限制
- `autoCleanup` (boolean): 自动清理

**输出端口：**
- `memoryUsage` (number): 内存使用量
- `memoryLimit` (number): 内存限制
- `freeMemory` (number): 可用内存
- `cleanedResources` (array): 已清理资源
- `onSuccess` (trigger): 操作成功
- `onError` (trigger): 操作失败

#### 8. AssetGarbageCollectionNode - 资源垃圾回收
**功能描述：** 自动回收不再使用的资源

**输入端口：**
- `execute` (trigger): 执行垃圾回收
- `mode` (string): 回收模式 (auto/aggressive/conservative/full)
- `threshold` (number): 回收阈值
- `force` (boolean): 强制回收

**输出端口：**
- `collectedCount` (number): 回收数量
- `freedMemory` (number): 释放内存
- `collectedResources` (array): 回收的资源
- `onCompleted` (trigger): 回收完成
- `onError` (trigger): 回收失败

#### 9. AssetPerformanceMonitorNode - 资源性能监控
**功能描述：** 监控资源的性能指标和使用情况

**输入端口：**
- `execute` (trigger): 执行监控
- `operation` (string): 操作类型 (get/start/stop/reset)
- `resourceId` (string): 资源ID
- `interval` (number): 监控间隔

**输出端口：**
- `metrics` (object): 性能指标
- `loadTime` (number): 加载时间
- `memoryUsage` (number): 内存使用
- `accessCount` (number): 访问次数
- `onUpdated` (trigger): 指标更新
- `onError` (trigger): 监控失败

#### 10. AssetUsageAnalyticsNode - 资源使用分析
**功能描述：** 分析资源的使用模式和统计信息

**输入端口：**
- `execute` (trigger): 执行分析
- `analysisType` (string): 分析类型 (usage/performance/optimization)
- `timeRange` (string): 时间范围 (1h/24h/7d/30d)
- `resourceFilter` (string): 资源过滤器

**输出端口：**
- `analytics` (object): 分析结果
- `topResources` (array): 热门资源
- `unusedResources` (array): 未使用资源
- `recommendations` (array): 优化建议
- `onCompleted` (trigger): 分析完成
- `onError` (trigger): 分析失败

## 使用指南

### 基本工作流程

1. **资源加载流程**
   ```
   LoadAssetNode → AssetValidationNode → AssetCacheNode
   ```

2. **资源优化流程**
   ```
   AssetOptimizationNode → TextureCompressionNode → AssetPerformanceMonitorNode
   ```

3. **资源管理流程**
   ```
   AssetMetadataNode → AssetVersionNode → AssetDependencyNode
   ```

### 最佳实践

1. **性能优化**
   - 使用PreloadAssetNode预加载常用资源
   - 通过AssetCacheNode管理缓存策略
   - 定期使用AssetGarbageCollectionNode清理内存

2. **资源安全**
   - 使用AssetValidationNode验证资源完整性
   - 通过AssetEncryptionNode保护敏感资源
   - 使用AssetVersionNode进行版本控制

3. **批量处理**
   - 使用AssetBatchingNode批量处理多个资源
   - 通过AssetStreamingNode实现大文件流式加载
   - 使用AssetUsageAnalyticsNode分析资源使用情况

## 集成说明

所有节点已集成到DL引擎的视觉脚本系统中，可在编辑器的节点面板中找到：
- 资源管理分类：包含12个资源加载节点
- 资源优化分类：包含10个资源优化节点

节点支持拖拽连接，可组合使用构建复杂的资源管理工作流。

## 高级用法示例

### 示例1：智能资源加载系统
```
[LoadAssetNode] → [AssetValidationNode] → [AssetCacheNode]
       ↓                    ↓                    ↓
[AsyncLoadAssetNode] → [AssetMetadataNode] → [AssetVersionNode]
       ↓                    ↓                    ↓
[PreloadAssetNode] → [AssetDependencyNode] → [AssetPerformanceMonitorNode]
```

**功能：** 构建一个完整的智能资源加载系统，包含验证、缓存、元数据管理和性能监控。

### 示例2：资源优化管道
```
[AssetOptimizationNode] → [TextureCompressionNode] → [AssetCompressionNode]
         ↓                        ↓                        ↓
[MeshOptimizationNode] → [AudioCompressionNode] → [AssetBatchingNode]
         ↓                        ↓                        ↓
[AssetMemoryManagementNode] → [AssetGarbageCollectionNode] → [AssetUsageAnalyticsNode]
```

**功能：** 创建资源优化管道，自动优化不同类型的资源并进行内存管理。

### 示例3：安全资源管理
```
[LoadAssetNode] → [AssetValidationNode] → [AssetEncryptionNode]
       ↓                    ↓                    ↓
[AssetVersionNode] → [AssetMetadataNode] → [AssetCacheNode]
```

**功能：** 实现安全的资源管理，包含验证、加密和版本控制。

## 性能优化建议

### 1. 内存管理
- **定期清理：** 使用AssetGarbageCollectionNode定期清理未使用的资源
- **内存监控：** 通过AssetMemoryManagementNode监控内存使用情况
- **缓存策略：** 使用AssetCacheNode设置合适的缓存大小和TTL

### 2. 加载优化
- **预加载：** 使用PreloadAssetNode预加载关键资源
- **异步加载：** 使用AsyncLoadAssetNode避免阻塞主线程
- **依赖管理：** 通过AssetDependencyNode优化资源加载顺序

### 3. 资源压缩
- **纹理压缩：** 使用TextureCompressionNode减少纹理内存占用
- **网格优化：** 通过MeshOptimizationNode减少模型复杂度
- **音频压缩：** 使用AudioCompressionNode优化音频文件大小

## 故障排除

### 常见问题

1. **资源加载失败**
   - 检查URL是否正确
   - 验证网络连接
   - 使用AssetValidationNode检查资源完整性

2. **内存不足**
   - 使用AssetMemoryManagementNode监控内存使用
   - 通过AssetGarbageCollectionNode清理内存
   - 调整缓存大小限制

3. **性能问题**
   - 使用AssetPerformanceMonitorNode分析性能瓶颈
   - 通过AssetUsageAnalyticsNode找出未使用的资源
   - 优化资源加载顺序

### 调试技巧

1. **启用调试模式**
   - 在节点输入中设置debug参数为true
   - 查看控制台输出的详细日志

2. **性能分析**
   - 使用AssetPerformanceMonitorNode监控关键指标
   - 通过AssetUsageAnalyticsNode生成使用报告

3. **资源验证**
   - 使用AssetValidationNode验证资源完整性
   - 检查资源元数据是否正确

## 版本更新说明

### 批次1.3 (当前版本)
- ✅ 新增12个资源加载节点
- ✅ 新增10个资源优化节点
- ✅ 完整的资源管理解决方案
- ✅ 支持编辑器可视化操作

### 后续计划
- 🔄 批次1.4：AI智能节点开发
- 🔄 批次1.5：区块链集成节点
- 🔄 批次1.6：数字人生成节点

## 技术支持

如需技术支持或反馈问题，请联系开发团队：
- 📧 邮箱：<EMAIL>
- 📱 微信群：DL引擎开发者社区
- 🌐 官网：https://dl-engine.com

---

**文档版本：** 1.0
**最后更新：** 2025年1月
**适用版本：** DL引擎 v2.0+
