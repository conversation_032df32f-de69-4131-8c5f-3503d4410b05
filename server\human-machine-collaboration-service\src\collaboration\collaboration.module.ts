import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { CollaborationService } from './collaboration.service';
import { ARVRGuidanceService } from './ar-vr-guidance.service';
import { CollaborationSession } from '../database/entities/collaboration-session.entity';
import { ARVRScene } from '../database/entities/arvr-scene.entity';
import { UserSession } from '../database/entities/user-session.entity';

/**
 * 协作模块
 * 提供AR/VR协作和多用户协作功能
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      CollaborationSession,
      ARVRScene,
      UserSession,
    ]),
  ],
  providers: [
    CollaborationService,
    ARVRGuidanceService,
  ],
  exports: [
    CollaborationService,
    ARVRGuidanceService,
  ],
})
export class CollaborationModule {}
