/**
 * 认证授权节点集合 - 第二部分
 * 批次2.1 - 服务器集成节点
 * 提供权限检查、安全审计、加密解密和安全监控等认证授权功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 权限检查节点
 * 批次2.1 - 认证授权节点
 */
export class PermissionCheckNode extends VisualScriptNode {
  public static readonly TYPE = 'PermissionCheck';
  public static readonly NAME = '权限检查';
  public static readonly DESCRIPTION = '检查用户是否具有特定权限';

  constructor(nodeType: string = PermissionCheckNode.TYPE, name: string = PermissionCheckNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('userId', 'string', '用户ID');
    this.addInput('resource', 'string', '资源');
    this.addInput('action', 'string', '操作');
    this.addInput('context', 'object', '上下文');
    this.addInput('requiredPermissions', 'array', '必需权限');

    // 输出端口
    this.addOutput('allowed', 'boolean', '权限允许');
    this.addOutput('permissions', 'array', '用户权限');
    this.addOutput('missingPermissions', 'array', '缺失权限');
    this.addOutput('reason', 'string', '拒绝原因');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('onAllowed', 'trigger', '权限允许事件');
    this.addOutput('onDenied', 'trigger', '权限拒绝事件');
    this.addOutput('onError', 'trigger', '检查错误事件');
  }

  public execute(inputs?: any): any {
    try {
      const userId = inputs?.userId as string;
      const resource = inputs?.resource as string;
      const action = inputs?.action as string;
      const context = inputs?.context || {};
      const requiredPermissions = inputs?.requiredPermissions as string[] || [];

      if (!userId) {
        Debug.warn('PermissionCheckNode', '用户ID为空');
        return this.getErrorOutput('用户ID不能为空');
      }

      // 执行权限检查
      const result = this.checkPermissions(userId, resource, action, context, requiredPermissions);
      
      Debug.log('PermissionCheckNode', `权限检查${result.allowed ? '通过' : '拒绝'}: ${userId}`);

      return result;
    } catch (error) {
      Debug.error('PermissionCheckNode', '权限检查执行失败', error);
      return this.getErrorOutput(error instanceof Error ? error.message : '权限检查失败');
    }
  }

  private checkPermissions(userId: string, resource: string, action: string, context: any, requiredPermissions: string[]): any {
    try {
      // 获取用户权限
      const userPermissions = this.getUserPermissions(userId);
      
      // 检查资源和操作权限
      let hasResourcePermission = true;
      let reason = '';
      
      if (resource && action) {
        hasResourcePermission = userPermissions.some(permission => 
          (permission.resource === resource || permission.resource === '*') &&
          (permission.action === action || permission.action === '*')
        );
        
        if (!hasResourcePermission) {
          reason = `用户没有对资源 ${resource} 执行 ${action} 操作的权限`;
        }
      }
      
      // 检查必需权限
      const missingPermissions: string[] = [];
      for (const requiredPermission of requiredPermissions) {
        const hasPermission = userPermissions.some(permission => 
          permission.name === requiredPermission || permission.id === requiredPermission
        );
        
        if (!hasPermission) {
          missingPermissions.push(requiredPermission);
        }
      }
      
      const hasAllRequiredPermissions = missingPermissions.length === 0;
      const allowed = hasResourcePermission && hasAllRequiredPermissions;
      
      if (!allowed && !reason) {
        reason = `用户缺少必需权限: ${missingPermissions.join(', ')}`;
      }

      return {
        allowed: allowed,
        permissions: userPermissions,
        missingPermissions: missingPermissions,
        reason: allowed ? null : reason,
        errorMessage: null,
        onAllowed: allowed,
        onDenied: !allowed,
        onError: false
      };
    } catch (error) {
      return this.getErrorOutput(error instanceof Error ? error.message : '权限检查失败');
    }
  }

  private getUserPermissions(userId: string): any[] {
    // 模拟用户权限数据
    return [
      { id: 'read_profile', name: '读取个人资料', resource: 'profile', action: 'read' },
      { id: 'write_profile', name: '修改个人资料', resource: 'profile', action: 'write' },
      { id: 'read_data', name: '读取数据', resource: 'data', action: 'read' }
    ];
  }

  private getErrorOutput(message: string): any {
    return {
      allowed: false,
      permissions: [],
      missingPermissions: [],
      reason: message,
      errorMessage: message,
      onAllowed: false,
      onDenied: false,
      onError: true
    };
  }
}

/**
 * 安全审计节点
 * 批次2.1 - 认证授权节点
 */
export class SecurityAuditNode extends VisualScriptNode {
  public static readonly TYPE = 'SecurityAudit';
  public static readonly NAME = '安全审计';
  public static readonly DESCRIPTION = '记录和分析安全相关事件';

  constructor(nodeType: string = SecurityAuditNode.TYPE, name: string = SecurityAuditNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('action', 'string', '操作类型');
    this.addInput('userId', 'string', '用户ID');
    this.addInput('event', 'string', '事件类型');
    this.addInput('resource', 'string', '资源');
    this.addInput('details', 'object', '事件详情');
    this.addInput('severity', 'string', '严重级别');

    // 输出端口
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('auditId', 'string', '审计ID');
    this.addOutput('logs', 'array', '审计日志');
    this.addOutput('alerts', 'array', '安全警报');
    this.addOutput('riskScore', 'number', '风险评分');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('onLogged', 'trigger', '日志记录事件');
    this.addOutput('onAlert', 'trigger', '安全警报事件');
    this.addOutput('onError', 'trigger', '操作错误事件');
  }

  public execute(inputs?: any): any {
    try {
      const action = inputs?.action as string || 'log';
      const userId = inputs?.userId as string;
      const event = inputs?.event as string;
      const resource = inputs?.resource as string;
      const details = inputs?.details || {};
      const severity = inputs?.severity || 'info';

      // 执行安全审计操作
      const result = this.performAuditOperation(action, userId, event, resource, details, severity);
      
      Debug.log('SecurityAuditNode', `安全审计${result.success ? '成功' : '失败'}: ${action}`);

      return result;
    } catch (error) {
      Debug.error('SecurityAuditNode', '安全审计执行失败', error);
      return this.getErrorOutput(error instanceof Error ? error.message : '安全审计失败');
    }
  }

  private performAuditOperation(action: string, userId: string, event: string, resource: string, details: any, severity: string): any {
    try {
      switch (action) {
        case 'log':
          return this.logSecurityEvent(userId, event, resource, details, severity);
        case 'query':
          return this.queryAuditLogs(userId, event, resource);
        case 'analyze':
          return this.analyzeSecurityEvents(userId);
        default:
          throw new Error(`不支持的操作: ${action}`);
      }
    } catch (error) {
      return this.getErrorOutput(error instanceof Error ? error.message : '安全审计操作失败');
    }
  }

  private logSecurityEvent(userId: string, event: string, resource: string, details: any, severity: string): any {
    const auditId = `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timestamp = new Date();
    
    const auditLog = {
      id: auditId,
      userId: userId,
      event: event,
      resource: resource,
      details: details,
      severity: severity,
      timestamp: timestamp,
      ipAddress: details.ipAddress || 'unknown',
      userAgent: details.userAgent || 'unknown'
    };

    // 检查是否需要生成警报
    const alerts = this.checkForAlerts(auditLog);
    const riskScore = this.calculateRiskScore(auditLog);

    return {
      success: true,
      auditId: auditId,
      logs: [auditLog],
      alerts: alerts,
      riskScore: riskScore,
      errorMessage: null,
      onLogged: true,
      onAlert: alerts.length > 0,
      onError: false
    };
  }

  private queryAuditLogs(userId: string, event: string, resource: string): any {
    // 模拟查询审计日志
    const mockLogs = [
      {
        id: 'audit_1',
        userId: userId,
        event: 'login',
        resource: 'auth',
        severity: 'info',
        timestamp: new Date(Date.now() - 3600000)
      },
      {
        id: 'audit_2',
        userId: userId,
        event: 'access_denied',
        resource: 'admin',
        severity: 'warning',
        timestamp: new Date(Date.now() - 1800000)
      }
    ];

    return {
      success: true,
      auditId: null,
      logs: mockLogs,
      alerts: [],
      riskScore: 0,
      errorMessage: null,
      onLogged: false,
      onAlert: false,
      onError: false
    };
  }

  private analyzeSecurityEvents(userId: string): any {
    // 模拟安全事件分析
    const alerts = [
      {
        id: 'alert_1',
        type: 'suspicious_activity',
        message: '检测到异常登录活动',
        severity: 'high',
        timestamp: new Date()
      }
    ];

    const riskScore = 75; // 高风险评分

    return {
      success: true,
      auditId: null,
      logs: [],
      alerts: alerts,
      riskScore: riskScore,
      errorMessage: null,
      onLogged: false,
      onAlert: true,
      onError: false
    };
  }

  private checkForAlerts(auditLog: any): any[] {
    const alerts: any[] = [];
    
    // 检查失败登录
    if (auditLog.event === 'login_failed') {
      alerts.push({
        id: `alert_${Date.now()}`,
        type: 'login_failure',
        message: '登录失败',
        severity: 'medium',
        timestamp: new Date()
      });
    }
    
    // 检查权限拒绝
    if (auditLog.event === 'access_denied' && auditLog.severity === 'warning') {
      alerts.push({
        id: `alert_${Date.now()}`,
        type: 'access_denied',
        message: '访问被拒绝',
        severity: 'medium',
        timestamp: new Date()
      });
    }
    
    return alerts;
  }

  private calculateRiskScore(auditLog: any): number {
    let score = 0;
    
    switch (auditLog.severity) {
      case 'critical': score += 50; break;
      case 'high': score += 30; break;
      case 'medium': score += 20; break;
      case 'low': score += 10; break;
      default: score += 5;
    }
    
    if (auditLog.event === 'login_failed') score += 15;
    if (auditLog.event === 'access_denied') score += 10;
    if (auditLog.event === 'data_breach') score += 40;
    
    return Math.min(score, 100);
  }

  private getErrorOutput(message: string): any {
    return {
      success: false,
      auditId: null,
      logs: [],
      alerts: [],
      riskScore: 0,
      errorMessage: message,
      onLogged: false,
      onAlert: false,
      onError: true
    };
  }
}

/**
 * 加密节点
 * 批次2.1 - 认证授权节点
 */
export class EncryptionNode extends VisualScriptNode {
  public static readonly TYPE = 'Encryption';
  public static readonly NAME = '加密';
  public static readonly DESCRIPTION = '对数据进行加密处理';

  constructor(nodeType: string = EncryptionNode.TYPE, name: string = EncryptionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('data', 'any', '待加密数据');
    this.addInput('algorithm', 'string', '加密算法');
    this.addInput('key', 'string', '加密密钥');
    this.addInput('iv', 'string', '初始化向量');
    this.addInput('encoding', 'string', '编码格式');

    // 输出端口
    this.addOutput('success', 'boolean', '加密成功');
    this.addOutput('encryptedData', 'string', '加密后数据');
    this.addOutput('algorithm', 'string', '使用的算法');
    this.addOutput('keyInfo', 'object', '密钥信息');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('onSuccess', 'trigger', '加密成功事件');
    this.addOutput('onError', 'trigger', '加密错误事件');
  }

  public execute(inputs?: any): any {
    try {
      const data = inputs?.data;
      const algorithm = inputs?.algorithm as string || 'AES-256-CBC';
      const key = inputs?.key as string;
      const iv = inputs?.iv as string;
      const encoding = inputs?.encoding as string || 'base64';

      if (data === undefined || data === null) {
        Debug.warn('EncryptionNode', '待加密数据为空');
        return this.getErrorOutput('待加密数据不能为空');
      }

      if (!key) {
        Debug.warn('EncryptionNode', '加密密钥为空');
        return this.getErrorOutput('加密密钥不能为空');
      }

      // 执行数据加密
      const result = this.encryptData(data, algorithm, key, iv, encoding);

      Debug.log('EncryptionNode', `数据加密${result.success ? '成功' : '失败'}: ${algorithm}`);

      return result;
    } catch (error) {
      Debug.error('EncryptionNode', '数据加密执行失败', error);
      return this.getErrorOutput(error instanceof Error ? error.message : '加密失败');
    }
  }

  private encryptData(data: any, algorithm: string, key: string, iv: string, encoding: string): any {
    try {
      // 将数据转换为字符串
      const dataString = typeof data === 'string' ? data : JSON.stringify(data);

      // 简化的加密实现（实际应使用真实的加密库）
      const encryptedData = this.simpleEncrypt(dataString, key, algorithm);

      const keyInfo = {
        algorithm: algorithm,
        keyLength: key.length,
        hasIV: !!iv,
        encoding: encoding
      };

      return {
        success: true,
        encryptedData: encryptedData,
        algorithm: algorithm,
        keyInfo: keyInfo,
        errorMessage: null,
        onSuccess: true,
        onError: false
      };
    } catch (error) {
      return this.getErrorOutput(error instanceof Error ? error.message : '加密失败');
    }
  }

  private simpleEncrypt(data: string, key: string, algorithm: string): string {
    // 简化的加密实现（仅用于演示）
    // 实际应使用 crypto 模块或其他加密库
    const keyHash = this.simpleHash(key);
    const encrypted = Buffer.from(data)
      .map((byte, index) => byte ^ keyHash.charCodeAt(index % keyHash.length))
      .toString('base64');

    return `${algorithm}:${encrypted}`;
  }

  private simpleHash(input: string): string {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  private getErrorOutput(message: string): any {
    return {
      success: false,
      encryptedData: null,
      algorithm: null,
      keyInfo: null,
      errorMessage: message,
      onSuccess: false,
      onError: true
    };
  }
}

/**
 * 解密节点
 * 批次2.1 - 认证授权节点
 */
export class DecryptionNode extends VisualScriptNode {
  public static readonly TYPE = 'Decryption';
  public static readonly NAME = '解密';
  public static readonly DESCRIPTION = '对加密数据进行解密处理';

  constructor(nodeType: string = DecryptionNode.TYPE, name: string = DecryptionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('encryptedData', 'string', '加密数据');
    this.addInput('algorithm', 'string', '解密算法');
    this.addInput('key', 'string', '解密密钥');
    this.addInput('iv', 'string', '初始化向量');
    this.addInput('encoding', 'string', '编码格式');

    // 输出端口
    this.addOutput('success', 'boolean', '解密成功');
    this.addOutput('decryptedData', 'any', '解密后数据');
    this.addOutput('algorithm', 'string', '使用的算法');
    this.addOutput('dataType', 'string', '数据类型');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('onSuccess', 'trigger', '解密成功事件');
    this.addOutput('onError', 'trigger', '解密错误事件');
  }

  public execute(inputs?: any): any {
    try {
      const encryptedData = inputs?.encryptedData as string;
      const algorithm = inputs?.algorithm as string;
      const key = inputs?.key as string;
      const iv = inputs?.iv as string;
      const encoding = inputs?.encoding as string || 'base64';

      if (!encryptedData) {
        Debug.warn('DecryptionNode', '加密数据为空');
        return this.getErrorOutput('加密数据不能为空');
      }

      if (!key) {
        Debug.warn('DecryptionNode', '解密密钥为空');
        return this.getErrorOutput('解密密钥不能为空');
      }

      // 执行数据解密
      const result = this.decryptData(encryptedData, algorithm, key, iv, encoding);

      Debug.log('DecryptionNode', `数据解密${result.success ? '成功' : '失败'}`);

      return result;
    } catch (error) {
      Debug.error('DecryptionNode', '数据解密执行失败', error);
      return this.getErrorOutput(error instanceof Error ? error.message : '解密失败');
    }
  }

  private decryptData(encryptedData: string, algorithm: string, key: string, iv: string, encoding: string): any {
    try {
      // 解析加密数据格式
      const parts = encryptedData.split(':');
      if (parts.length !== 2) {
        throw new Error('无效的加密数据格式');
      }

      const [dataAlgorithm, encrypted] = parts;
      const usedAlgorithm = algorithm || dataAlgorithm;

      // 简化的解密实现
      const decryptedString = this.simpleDecrypt(encrypted, key, usedAlgorithm);

      // 尝试解析为JSON，如果失败则返回字符串
      let decryptedData: any;
      let dataType: string;

      try {
        decryptedData = JSON.parse(decryptedString);
        dataType = typeof decryptedData;
      } catch {
        decryptedData = decryptedString;
        dataType = 'string';
      }

      return {
        success: true,
        decryptedData: decryptedData,
        algorithm: usedAlgorithm,
        dataType: dataType,
        errorMessage: null,
        onSuccess: true,
        onError: false
      };
    } catch (error) {
      return this.getErrorOutput(error instanceof Error ? error.message : '解密失败');
    }
  }

  private simpleDecrypt(encryptedData: string, key: string, algorithm: string): string {
    // 简化的解密实现（仅用于演示）
    try {
      const keyHash = this.simpleHash(key);
      const buffer = Buffer.from(encryptedData, 'base64');
      const decrypted = buffer
        .map((byte, index) => byte ^ keyHash.charCodeAt(index % keyHash.length))
        .toString();

      return decrypted;
    } catch (error) {
      throw new Error('解密失败：数据格式错误或密钥不匹配');
    }
  }

  private simpleHash(input: string): string {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  private getErrorOutput(message: string): any {
    return {
      success: false,
      decryptedData: null,
      algorithm: null,
      dataType: null,
      errorMessage: message,
      onSuccess: false,
      onError: true
    };
  }
}

/**
 * 安全监控节点
 * 批次2.1 - 认证授权节点
 */
export class SecurityMonitoringNode extends VisualScriptNode {
  public static readonly TYPE = 'SecurityMonitoring';
  public static readonly NAME = '安全监控';
  public static readonly DESCRIPTION = '实时监控系统安全状态';

  constructor(nodeType: string = SecurityMonitoringNode.TYPE, name: string = SecurityMonitoringNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('action', 'string', '监控操作');
    this.addInput('metrics', 'array', '监控指标');
    this.addInput('thresholds', 'object', '阈值配置');
    this.addInput('timeWindow', 'number', '时间窗口(秒)');

    // 输出端口
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('status', 'string', '安全状态');
    this.addOutput('alerts', 'array', '安全警报');
    this.addOutput('metrics', 'object', '监控指标');
    this.addOutput('threats', 'array', '威胁检测');
    this.addOutput('recommendations', 'array', '安全建议');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('onAlert', 'trigger', '安全警报事件');
    this.addOutput('onThreat', 'trigger', '威胁检测事件');
    this.addOutput('onError', 'trigger', '监控错误事件');
  }

  public execute(inputs?: any): any {
    try {
      const action = inputs?.action as string || 'monitor';
      const metrics = inputs?.metrics as string[] || ['login_attempts', 'failed_requests', 'suspicious_ips'];
      const thresholds = inputs?.thresholds || {};
      const timeWindow = inputs?.timeWindow ?? 300; // 5分钟

      // 执行安全监控操作
      const result = this.performMonitoring(action, metrics, thresholds, timeWindow);

      Debug.log('SecurityMonitoringNode', `安全监控${result.success ? '成功' : '失败'}: ${action}`);

      return result;
    } catch (error) {
      Debug.error('SecurityMonitoringNode', '安全监控执行失败', error);
      return this.getErrorOutput(error instanceof Error ? error.message : '安全监控失败');
    }
  }

  private performMonitoring(action: string, metrics: string[], thresholds: any, timeWindow: number): any {
    try {
      switch (action) {
        case 'monitor':
          return this.monitorSecurity(metrics, thresholds, timeWindow);
        case 'analyze':
          return this.analyzeThreats(metrics, timeWindow);
        case 'report':
          return this.generateSecurityReport(metrics, timeWindow);
        default:
          throw new Error(`不支持的操作: ${action}`);
      }
    } catch (error) {
      return this.getErrorOutput(error instanceof Error ? error.message : '安全监控操作失败');
    }
  }

  private monitorSecurity(metrics: string[], thresholds: any, timeWindow: number): any {
    // 模拟安全监控数据
    const monitoringData = this.collectSecurityMetrics(metrics, timeWindow);
    const alerts = this.checkThresholds(monitoringData, thresholds);
    const threats = this.detectThreats(monitoringData);
    const status = this.calculateSecurityStatus(alerts, threats);
    const recommendations = this.generateRecommendations(alerts, threats);

    return {
      success: true,
      status: status,
      alerts: alerts,
      metrics: monitoringData,
      threats: threats,
      recommendations: recommendations,
      errorMessage: null,
      onAlert: alerts.length > 0,
      onThreat: threats.length > 0,
      onError: false
    };
  }

  private analyzeThreats(metrics: string[], timeWindow: number): any {
    const threats = [
      {
        id: 'threat_1',
        type: 'brute_force',
        severity: 'high',
        description: '检测到暴力破解攻击',
        source: '*************',
        timestamp: new Date()
      }
    ];

    return {
      success: true,
      status: 'threat_detected',
      alerts: [],
      metrics: {},
      threats: threats,
      recommendations: ['立即封禁可疑IP地址', '启用账户锁定机制'],
      errorMessage: null,
      onAlert: false,
      onThreat: true,
      onError: false
    };
  }

  private generateSecurityReport(metrics: string[], timeWindow: number): any {
    const reportData = {
      period: `${timeWindow}秒`,
      totalEvents: 1250,
      securityEvents: 45,
      threats: 3,
      alerts: 8,
      status: 'normal'
    };

    return {
      success: true,
      status: 'normal',
      alerts: [],
      metrics: reportData,
      threats: [],
      recommendations: ['定期更新安全策略', '加强用户安全培训'],
      errorMessage: null,
      onAlert: false,
      onThreat: false,
      onError: false
    };
  }

  private collectSecurityMetrics(metrics: string[], timeWindow: number): any {
    const data: any = {};

    for (const metric of metrics) {
      switch (metric) {
        case 'login_attempts':
          data[metric] = Math.floor(Math.random() * 100) + 50;
          break;
        case 'failed_requests':
          data[metric] = Math.floor(Math.random() * 20) + 5;
          break;
        case 'suspicious_ips':
          data[metric] = Math.floor(Math.random() * 5);
          break;
        default:
          data[metric] = Math.floor(Math.random() * 50);
      }
    }

    return data;
  }

  private checkThresholds(data: any, thresholds: any): any[] {
    const alerts: any[] = [];

    for (const [metric, value] of Object.entries(data)) {
      const threshold = thresholds[metric];
      if (threshold && typeof value === 'number' && value > threshold) {
        alerts.push({
          id: `alert_${Date.now()}_${metric}`,
          metric: metric,
          value: value,
          threshold: threshold,
          severity: 'warning',
          message: `${metric} 超过阈值: ${value} > ${threshold}`,
          timestamp: new Date()
        });
      }
    }

    return alerts;
  }

  private detectThreats(data: any): any[] {
    const threats: any[] = [];

    // 检测异常登录尝试
    if (data.failed_requests > 15) {
      threats.push({
        id: 'threat_failed_requests',
        type: 'suspicious_activity',
        severity: 'medium',
        description: '异常的失败请求数量',
        timestamp: new Date()
      });
    }

    // 检测可疑IP
    if (data.suspicious_ips > 2) {
      threats.push({
        id: 'threat_suspicious_ips',
        type: 'network_threat',
        severity: 'high',
        description: '检测到多个可疑IP地址',
        timestamp: new Date()
      });
    }

    return threats;
  }

  private calculateSecurityStatus(alerts: any[], threats: any[]): string {
    if (threats.some(t => t.severity === 'critical')) return 'critical';
    if (threats.some(t => t.severity === 'high')) return 'high_risk';
    if (alerts.length > 5) return 'warning';
    return 'normal';
  }

  private generateRecommendations(alerts: any[], threats: any[]): string[] {
    const recommendations: string[] = [];

    if (threats.length > 0) {
      recommendations.push('立即调查安全威胁');
      recommendations.push('加强访问控制');
    }

    if (alerts.length > 3) {
      recommendations.push('调整监控阈值');
      recommendations.push('增加安全检查频率');
    }

    if (recommendations.length === 0) {
      recommendations.push('继续保持当前安全策略');
    }

    return recommendations;
  }

  private getErrorOutput(message: string): any {
    return {
      success: false,
      status: 'error',
      alerts: [],
      metrics: {},
      threats: [],
      recommendations: [],
      errorMessage: message,
      onAlert: false,
      onThreat: false,
      onError: true
    };
  }
}
