/**
 * 安全事件仓库
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Between } from 'typeorm';
import type {  SecurityEvent, EventSeverity, EventStatus, EventType  } from '../entities/security-event.entity';

@Injectable()
export class SecurityEventRepository {
  private readonly logger = new Logger(SecurityEventRepository.name);

  constructor(
    @InjectRepository(SecurityEvent)
    private repository: Repository<SecurityEvent>,
  ) {}

  /**
   * 创建安全事件
   */
  async create(eventData: Partial<SecurityEvent>): Promise<SecurityEvent> {
    try {
      const event = this.repository.create(eventData);
      const savedEvent = await this.repository.save(event);
      this.logger.log(`安全事件已记录: ${savedEvent.eventType} - ${savedEvent.severity}`);
      return savedEvent;
    } catch (error) {
      this.logger.error('创建安全事件失败', error);
      throw error;
    }
  }

  /**
   * 根据ID查找事件
   */
  async findById(id: string): Promise<SecurityEvent | null> {
    try {
      return await this.repository.findOne({ where: { id } });
    } catch (error) {
      this.logger.error('查找安全事件失败', error);
      throw error;
    }
  }

  /**
   * 获取最近的安全事件
   */
  async findRecent(limit: number = 50): Promise<SecurityEvent[]> {
    try {
      return await this.repository.find({
        order: { timestamp: 'DESC' },
        take: limit,
      });
    } catch (error) {
      this.logger.error('获取最近安全事件失败', error);
      throw error;
    }
  }

  /**
   * 根据严重程度获取事件
   */
  async findBySeverity(severity: EventSeverity): Promise<SecurityEvent[]> {
    try {
      return await this.repository.find({
        where: { severity },
        order: { timestamp: 'DESC' },
      });
    } catch (error) {
      this.logger.error('根据严重程度获取事件失败', error);
      throw error;
    }
  }

  /**
   * 根据事件类型获取事件
   */
  async findByType(eventType: EventType): Promise<SecurityEvent[]> {
    try {
      return await this.repository.find({
        where: { eventType },
        order: { timestamp: 'DESC' },
      });
    } catch (error) {
      this.logger.error('根据事件类型获取事件失败', error);
      throw error;
    }
  }

  /**
   * 获取未解决的事件
   */
  async findUnresolved(): Promise<SecurityEvent[]> {
    try {
      return await this.repository.find({
        where: {
          status: EventStatus.OPEN,
        },
        order: { severity: 'DESC', timestamp: 'DESC' },
      });
    } catch (error) {
      this.logger.error('获取未解决事件失败', error);
      throw error;
    }
  }

  /**
   * 更新事件状态
   */
  async updateStatus(id: string, status: string, resolvedBy?: string): Promise<SecurityEvent> {
    try {
      const updateData: any = { status };
      if (status === 'resolved' && resolvedBy) {
        updateData.resolvedBy = resolvedBy;
        updateData.resolvedAt = new Date();
      }

      await this.repository.update(id, updateData);
      const updatedEvent = await this.findById(id);
      if (updatedEvent) {
        this.logger.log(`安全事件状态已更新: ${id} -> ${status}`);
      }
      return updatedEvent;
    } catch (error) {
      this.logger.error('更新事件状态失败', error);
      throw error;
    }
  }

  /**
   * 获取事件统计
   */
  async getStatistics(timeRange?: { start: Date; end: Date }): Promise<any> {
    try {
      const whereCondition: FindOptionsWhere<SecurityEvent> = {};
      
      if (timeRange) {
        whereCondition.timestamp = Between(timeRange.start, timeRange.end);
      }

      const [
        total,
        critical,
        high,
        medium,
        low,
        open,
        investigating,
        resolved,
      ] = await Promise.all([
        this.repository.count({ where: whereCondition }),
        this.repository.count({ where: { ...whereCondition, severity: EventSeverity.CRITICAL } }),
        this.repository.count({ where: { ...whereCondition, severity: EventSeverity.HIGH } }),
        this.repository.count({ where: { ...whereCondition, severity: EventSeverity.MEDIUM } }),
        this.repository.count({ where: { ...whereCondition, severity: EventSeverity.LOW } }),
        this.repository.count({ where: { ...whereCondition, status: EventStatus.OPEN } }),
        this.repository.count({ where: { ...whereCondition, status: EventStatus.INVESTIGATING } }),
        this.repository.count({ where: { ...whereCondition, status: EventStatus.RESOLVED } }),
      ]);

      return {
        total,
        bySeverity: {
          critical,
          high,
          medium,
          low,
        },
        byStatus: {
          open,
          investigating,
          resolved,
        },
        timeRange,
      };
    } catch (error) {
      this.logger.error('获取事件统计失败', error);
      throw error;
    }
  }

  /**
   * 分页查询事件
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 10,
    filters?: any,
  ): Promise<{ events: SecurityEvent[]; total: number; totalPages: number }> {
    try {
      const skip = (page - 1) * limit;
      const where: FindOptionsWhere<SecurityEvent> = {};

      // 应用过滤器
      if (filters?.severity) {
        where.severity = filters.severity;
      }
      if (filters?.eventType) {
        where.eventType = filters.eventType;
      }
      if (filters?.status) {
        where.status = filters.status;
      }
      if (filters?.source) {
        where.source = filters.source;
      }

      const [events, total] = await this.repository.findAndCount({
        where,
        skip,
        take: limit,
        order: { timestamp: 'DESC' },
      });

      return {
        events,
        total,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error('分页查询事件失败', error);
      throw error;
    }
  }

  /**
   * 获取事件趋势数据
   */
  async getEventTrends(days: number = 7): Promise<any> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - days);

      const events = await this.repository
        .createQueryBuilder('event')
        .select([
          'DATE(event.timestamp) as date',
          'event.severity',
          'COUNT(*) as count',
        ])
        .where('event.timestamp BETWEEN :start AND :end', {
          start: startDate,
          end: endDate,
        })
        .groupBy('DATE(event.timestamp), event.severity')
        .orderBy('date', 'ASC')
        .getRawMany();

      // 组织数据为趋势格式
      const trends = {};
      events.forEach(event => {
        if (!trends[event.date]) {
          trends[event.date] = {
            date: event.date,
            critical: 0,
            high: 0,
            medium: 0,
            low: 0,
            total: 0,
          };
        }
        trends[event.date][event.severity] = parseInt(event.count);
        trends[event.date].total += parseInt(event.count);
      });

      return Object.values(trends);
    } catch (error) {
      this.logger.error('获取事件趋势失败', error);
      throw error;
    }
  }

  /**
   * 删除旧事件
   */
  async deleteOldEvents(daysToKeep: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await this.repository
        .createQueryBuilder()
        .delete()
        .where('timestamp < :cutoffDate', { cutoffDate })
        .execute();

      const deletedCount = result.affected || 0;
      this.logger.log(`已删除 ${deletedCount} 个旧安全事件`);
      return deletedCount;
    } catch (error) {
      this.logger.error('删除旧事件失败', error);
      throw error;
    }
  }

  /**
   * 搜索事件
   */
  async search(keyword: string): Promise<SecurityEvent[]> {
    try {
      return await this.repository
        .createQueryBuilder('event')
        .where('event.description LIKE :keyword', { keyword: `%${keyword}%` })
        .orWhere('event.source LIKE :keyword', { keyword: `%${keyword}%` })
        .orWhere('event.target LIKE :keyword', { keyword: `%${keyword}%` })
        .orderBy('event.timestamp', 'DESC')
        .getMany();
    } catch (error) {
      this.logger.error('搜索事件失败', error);
      throw error;
    }
  }
}
