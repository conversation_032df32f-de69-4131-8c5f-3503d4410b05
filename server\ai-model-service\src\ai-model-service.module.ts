/**
 * AI模型服务主模块
 */
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ScheduleModule } from '@nestjs/schedule';
import { BullModule } from '@nestjs/bull';
import { CacheModule } from '@nestjs/cache-manager';

// 配置
import { databaseConfig } from './config/database.config';
import { redisConfig } from './config/redis.config';
import { aiConfig } from './config/ai.config';

// 实体
import { AIModel } from './entities/ai-model.entity';
import { ModelVersion } from './entities/model-version.entity';
import { InferenceLog } from './entities/inference-log.entity';
import { ModelMetrics } from './entities/model-metrics.entity';

// 模块
import { ModelsModule } from './modules/models/models.module';
import { InferenceModule } from './modules/inference/inference.module';
import { MetricsModule } from './modules/metrics/metrics.module';
import { HealthModule } from './modules/health/health.module';
// import { CacheModule } from './modules/cache/cache.module'; // 已在主模块中配置

// 控制器
import { AIModelController } from './controllers/ai-model.controller';

// 服务
import { AIModelService } from './services/ai-model.service';

// 中间件 - 暂时注释掉，稍后创建
// import { RequestIdMiddleware } from './common/middleware/request-id.middleware';
// import { RateLimitMiddleware } from './common/middleware/rate-limit.middleware';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, redisConfig, aiConfig],
      envFilePath: ['.env.local', '.env'],
      cache: true,
      expandVariables: true
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get<string>('database.host'),
        port: configService.get<number>('database.port'),
        username: configService.get<string>('database.username'),
        password: configService.get<string>('database.password'),
        database: configService.get<string>('database.name'),
        entities: [AIModel, ModelVersion, InferenceLog, ModelMetrics],
        synchronize: configService.get<boolean>('database.synchronize', false),
        logging: configService.get<boolean>('database.logging', false),
        retryAttempts: 3,
        retryDelay: 3000,
        autoLoadEntities: true,
        keepConnectionAlive: true,
        timezone: '+08:00',
        charset: 'utf8mb4',
        extra: {
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000
        }
      }),
      inject: [ConfigService]
    }),

    // TypeORM实体模块
    TypeOrmModule.forFeature([
      AIModel,
      ModelVersion,
      InferenceLog,
      ModelMetrics
    ]),

    // Redis缓存模块
    CacheModule.registerAsync({
      useFactory: (configService: ConfigService) => configService.get('redis'),
      inject: [ConfigService],
      isGlobal: true,
    }),

    // 队列模块
    BullModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get<string>('redis.host'),
          port: configService.get<number>('redis.port'),
          password: configService.get<string>('redis.password'),
          db: configService.get<number>('redis.queueDb', 1)
        },
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000
          }
        }
      }),
      inject: [ConfigService]
    }),

    // 微服务客户端
    ClientsModule.registerAsync([
      {
        name: 'SERVICE_REGISTRY',
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('microservices.registry.host'),
            port: configService.get<number>('microservices.registry.port')
          }
        }),
        inject: [ConfigService]
      },
      {
        name: 'RECOMMENDATION_SERVICE',
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('microservices.recommendation.host'),
            port: configService.get<number>('microservices.recommendation.port')
          }
        }),
        inject: [ConfigService]
      },
      {
        name: 'ANALYTICS_SERVICE',
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('microservices.analytics.host'),
            port: configService.get<number>('microservices.analytics.port')
          }
        }),
        inject: [ConfigService]
      }
    ]),

    // 定时任务模块
    ScheduleModule.forRoot(),

    // 功能模块
    ModelsModule,
    InferenceModule,
    MetricsModule,
    HealthModule
  ],

  controllers: [AIModelController],

  providers: [
    AIModelService,
    
    // 全局提供者
    {
      provide: 'APP_CONFIG',
      useFactory: (configService: ConfigService) => ({
        name: 'ai-model-service',
        version: '1.0.0',
        environment: configService.get<string>('NODE_ENV', 'development'),
        port: configService.get<number>('PORT', 3008),
        debug: configService.get<boolean>('DEBUG', false)
      }),
      inject: [ConfigService]
    }
  ],

  exports: [AIModelService]
})
export class AIModelServiceModule {
  constructor(private configService: ConfigService) {
    // 模块初始化日志
    console.log('AI模型服务模块已加载');
    console.log(`环境: ${this.configService.get<string>('NODE_ENV', 'development')}`);
    console.log(`数据库: ${this.configService.get<string>('database.host')}:${this.configService.get<number>('database.port')}`);
    console.log(`Redis: ${this.configService.get<string>('redis.host')}:${this.configService.get<number>('redis.port')}`);
  }
}
