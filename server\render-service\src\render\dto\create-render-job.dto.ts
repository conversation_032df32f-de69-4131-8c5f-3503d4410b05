/**
 * 创建渲染任务DTO
 */
import { IsString, IsNotEmpty, IsOptional, IsEnum, IsObject, IsUUID, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import type {  Type  } from 'class-transformer';
import type {  RenderJobType  } from '../entities/render-job.entity';

class RenderSettingsDto {
  @ApiProperty({ description: '渲染宽度', example: 1920 })
  width: number;

  @ApiProperty({ description: '渲染高度', example: 1080 })
  height: number;

  @ApiProperty({ description: '渲染质量', example: 80 })
  quality: number;

  @ApiProperty({ description: '输出格式', example: 'png' })
  format: string;

  @ApiProperty({ description: '帧数', required: false, example: 1 })
  frames?: number;

  @ApiProperty({ description: '帧率', required: false, example: 30 })
  fps?: number;

  @ApiProperty({ description: '相机ID', required: false })
  camera?: string;

  @ApiProperty({ description: '光照设置', required: false })
  lighting?: string;

  @ApiProperty({ description: '是否启用后期处理', required: false, default: false })
  postProcessing?: boolean;

  [key: string]: any;
}

export class CreateRenderJobDto {
  @ApiProperty({ description: '任务名称', example: '场景渲染' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '任务描述', required: false, example: '这是一个场景渲染任务' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '渲染类型', enum: RenderJobType, default: RenderJobType.IMAGE })
  @IsEnum(RenderJobType)
  @IsOptional()
  type?: RenderJobType;

  @ApiProperty({ description: '项目ID' })
  @IsUUID()
  @IsNotEmpty()
  projectId: string;

  @ApiProperty({ description: '场景ID' })
  @IsUUID()
  @IsNotEmpty()
  sceneId: string;

  @ApiProperty({ description: '渲染设置', type: RenderSettingsDto })
  @IsObject()
  @ValidateNested()
  @Type(() => RenderSettingsDto)
  settings: RenderSettingsDto;
}
