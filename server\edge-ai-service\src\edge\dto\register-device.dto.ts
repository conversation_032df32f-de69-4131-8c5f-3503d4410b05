import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsObject, ValidateNested } from 'class-validator';
import type {  Type  } from 'class-transformer';

export enum EdgeDeviceType {
  INDUSTRIAL_PC = 'industrial_pc',
  EMBEDDED_CONTROLLER = 'embedded_controller',
  SMART_SENSOR = 'smart_sensor',
  GATEWAY = 'gateway',
  MOBILE_DEVICE = 'mobile_device',
  FPGA = 'fpga',
  GPU_ACCELERATOR = 'gpu_accelerator',
  TPU = 'tpu'
}

export class DeviceCapabilitiesDto {
  @ApiProperty({ description: 'CPU信息' })
  @IsObject()
  cpu: {
    cores: number;
    frequency: number;
    architecture: string;
  };

  @ApiProperty({ description: '内存信息' })
  @IsObject()
  memory: {
    total: number;
    available: number;
  };

  @ApiProperty({ description: '存储信息' })
  @IsObject()
  storage: {
    total: number;
    available: number;
  };

  @ApiPropertyOptional({ description: '加速器信息' })
  @IsOptional()
  @IsObject()
  accelerators?: {
    type: 'gpu' | 'tpu' | 'fpga' | 'npu';
    model: string;
    memory: number;
  }[];

  @ApiPropertyOptional({ description: '传感器列表' })
  @IsOptional()
  sensors?: string[];

  @ApiProperty({ description: '连接方式' })
  connectivity: string[];
}

export class NetworkInfoDto {
  @ApiProperty({ description: 'IP地址' })
  @IsString()
  ipAddress: string;

  @ApiProperty({ description: '带宽 (Mbps)' })
  bandwidth: number;

  @ApiProperty({ description: '延迟 (ms)' })
  latency: number;

  @ApiProperty({ description: '可靠性 (%)' })
  reliability: number;

  @ApiProperty({ description: '协议' })
  @IsString()
  protocol: string;

  @ApiProperty({ description: '是否加密' })
  encryption: boolean;
}

export class RegisterDeviceDto {
  @ApiPropertyOptional({ description: '设备ID' })
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiProperty({ description: '设备名称' })
  @IsString()
  name: string;

  @ApiProperty({ 
    description: '设备类型',
    enum: EdgeDeviceType,
    example: EdgeDeviceType.INDUSTRIAL_PC
  })
  @IsEnum(EdgeDeviceType)
  type: EdgeDeviceType;

  @ApiProperty({ description: '设备位置' })
  @IsString()
  location: string;

  @ApiProperty({ description: '设备能力', type: DeviceCapabilitiesDto })
  @ValidateNested()
  @Type(() => DeviceCapabilitiesDto)
  capabilities: DeviceCapabilitiesDto;

  @ApiPropertyOptional({ description: '网络信息', type: NetworkInfoDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => NetworkInfoDto)
  networkInfo?: NetworkInfoDto;
}
