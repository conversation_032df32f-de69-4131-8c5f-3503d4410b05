import { IsString, <PERSON><PERSON><PERSON><PERSON>, IsO<PERSON>al, IsEnum, IsDateString, IsObject, IsArray, ValidateNested } from 'class-validator';
import type {  Type  } from 'class-transformer';
import { DataQuality } from '../entities/time-series-data.entity';

export class StoreDataPointDto {
  @IsString()
  tagName: string;

  @IsOptional()
  @IsString()
  tagDescription?: string;

  value: any;

  @IsOptional()
  @IsString()
  unit?: string;

  @IsOptional()
  @IsEnum(DataQuality)
  quality?: DataQuality;

  @IsOptional()
  @IsDateString()
  timestamp?: string;

  @IsOptional()
  @IsObject()
  metadata?: any;
}

export class StoreDataDto {
  @IsUUID()
  deviceId: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StoreDataPointDto)
  dataPoints: StoreDataPointDto[];
}

export class BatchStoreDataDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StoreDataDto)
  devices: StoreDataDto[];
}
