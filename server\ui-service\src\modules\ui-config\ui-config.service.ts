import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { InjectRedis } from '@nestjs-modules/ioredis';
import type {  Model, Types  } from 'mongoose';
import Redis from 'ioredis';
import type {  UIConfig, UIConfigDocument, ConfigType, ConfigScope  } from './schemas/ui-config.schema';
import { CreateUIConfigDto, UpdateUIConfigDto, QueryUIConfigDto } from './dto/ui-config.dto';

@Injectable()
export class UIConfigService {
  constructor(
    @InjectModel(UIConfig.name) private configModel: Model<UIConfigDocument>,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  /**
   * 创建UI配置
   */
  async create(createConfigDto: CreateUIConfigDto, userId: Types.ObjectId): Promise<UIConfig> {
    try {
      // 检查配置名称是否已存在
      const existingConfig = await this.configModel.findOne({
        name: createConfigDto.name,
        type: createConfigDto.type,
        scope: createConfigDto.scope,
        ...(createConfigDto.userId && { userId: createConfigDto.userId }),
        ...(createConfigDto.organizationId && { organizationId: createConfigDto.organizationId }),
        ...(createConfigDto.projectId && { projectId: createConfigDto.projectId }),
      });

      if (existingConfig) {
        throw new BadRequestException('配置名称已存在');
      }

      // 创建配置
      const config = new this.configModel({
        ...createConfigDto,
        createdBy: userId,
        updatedBy: userId,
      });

      const savedConfig = await config.save();

      // 清除相关缓存
      await this.clearConfigCache(createConfigDto.type, createConfigDto.scope);

      return savedConfig;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('创建配置失败');
    }
  }

  /**
   * 查询UI配置列表
   */
  async findAll(queryDto: QueryUIConfigDto, userId: Types.ObjectId): Promise<{
    configs: UIConfig[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { page = 1, limit = 20, search, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = queryDto;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const query: any = {
      isActive: true,
    };

    // 添加过滤条件
    if (filters.type) query.type = filters.type;
    if (filters.scope) query.scope = filters.scope;
    if (filters.userId) query.userId = filters.userId;
    if (filters.organizationId) query.organizationId = filters.organizationId;
    if (filters.projectId) query.projectId = filters.projectId;
    if (filters.category) query['metadata.category'] = filters.category;
    if (filters.tags) query['metadata.tags'] = { $in: filters.tags.split(',') };
    if (typeof filters.isActive === 'boolean') query.isActive = filters.isActive;

    // 添加搜索条件
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { 'metadata.category': { $regex: search, $options: 'i' } },
      ];
    }

    // 添加权限过滤
    query.$or = [
      { scope: ConfigScope.SYSTEM },
      { scope: ConfigScope.USER, userId: userId },
      { scope: ConfigScope.ORGANIZATION, organizationId: { $in: await this.getUserOrganizations(userId) } },
    ];

    // 排序
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // 执行查询
    const [configs, total] = await Promise.all([
      this.configModel
        .find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('createdBy', 'username email')
        .populate('updatedBy', 'username email')
        .exec(),
      this.configModel.countDocuments(query),
    ]);

    return {
      configs,
      total,
      page,
      limit,
    };
  }

  /**
   * 获取单个UI配置
   */
  async findOne(id: string, userId: Types.ObjectId): Promise<UIConfig> {
    // 尝试从缓存获取
    const cacheKey = `ui_config:${id}`;
    const cached = await this.redis.get(cacheKey);
    if (cached) {
      const config = JSON.parse(cached);
      if (this.canAccessConfig(config, userId)) {
        return config;
      }
    }

    // 从数据库获取
    const config = await this.configModel
      .findById(id)
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email')
      .exec();

    if (!config) {
      throw new NotFoundException('配置不存在');
    }

    // 检查访问权限
    if (!this.canAccessConfig(config, userId)) {
      throw new ForbiddenException('没有访问权限');
    }

    // 缓存配置
    await this.redis.setex(cacheKey, 1800, JSON.stringify(config));

    return config;
  }

  /**
   * 更新UI配置
   */
  async update(id: string, updateConfigDto: UpdateUIConfigDto, userId: Types.ObjectId): Promise<UIConfig> {
    const config = await this.configModel.findById(id);

    if (!config) {
      throw new NotFoundException('配置不存在');
    }

    // 检查编辑权限
    if (!this.canEditConfig(config, userId)) {
      throw new ForbiddenException('没有编辑权限');
    }

    // 更新配置
    Object.assign(config, updateConfigDto);
    config.updatedBy = userId;
    config.updatedAt = new Date();

    const updatedConfig = await config.save();

    // 清除缓存
    await this.redis.del(`ui_config:${id}`);
    await this.clearConfigCache(config.type, config.scope);

    return updatedConfig;
  }

  /**
   * 删除UI配置
   */
  async remove(id: string, userId: Types.ObjectId): Promise<void> {
    const config = await this.configModel.findById(id);

    if (!config) {
      throw new NotFoundException('配置不存在');
    }

    // 检查删除权限
    if (!this.canEditConfig(config, userId)) {
      throw new ForbiddenException('没有删除权限');
    }

    await this.configModel.findByIdAndDelete(id);

    // 清除缓存
    await this.redis.del(`ui_config:${id}`);
    await this.clearConfigCache(config.type, config.scope);
  }

  /**
   * 获取配置值
   */
  async getConfigValue(type: ConfigType, scope: ConfigScope, key: string, userId?: Types.ObjectId): Promise<any> {
    const cacheKey = `ui_config_value:${type}:${scope}:${key}:${userId || 'system'}`;
    const cached = await this.redis.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }

    const query: any = { type, scope, isActive: true };
    if (userId && scope === ConfigScope.USER) {
      query.userId = userId;
    }

    const config = await this.configModel.findOne(query).exec();
    const value = config?.data[key] || null;

    // 缓存结果
    await this.redis.setex(cacheKey, 600, JSON.stringify(value));

    return value;
  }

  /**
   * 批量获取配置
   */
  async getBatchConfigs(type: ConfigType, scope: ConfigScope, userId?: Types.ObjectId): Promise<any> {
    const cacheKey = `ui_config_batch:${type}:${scope}:${userId || 'system'}`;
    const cached = await this.redis.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }

    const query: any = { type, scope, isActive: true };
    if (userId && scope === ConfigScope.USER) {
      query.userId = userId;
    }

    const configs = await this.configModel.find(query).exec();
    const result = configs.reduce((acc, config) => {
      acc[config.name] = config.data;
      return acc;
    }, {});

    // 缓存结果
    await this.redis.setex(cacheKey, 600, JSON.stringify(result));

    return result;
  }

  /**
   * 检查配置访问权限
   */
  private canAccessConfig(config: UIConfig, userId: Types.ObjectId): boolean {
    if (config.scope === ConfigScope.SYSTEM) {
      return true;
    }

    if (config.scope === ConfigScope.USER) {
      return config.userId?.equals(userId);
    }

    // 简化的组织权限检查
    return true;
  }

  /**
   * 检查配置编辑权限
   */
  private canEditConfig(config: UIConfig, userId: Types.ObjectId): boolean {
    if (config.metadata?.readonly) {
      return false;
    }

    return config.createdBy.equals(userId) || config.scope === ConfigScope.USER;
  }

  /**
   * 获取用户组织列表
   */
  private async getUserOrganizations(userId: Types.ObjectId): Promise<Types.ObjectId[]> {
    // 这里应该调用用户服务获取用户的组织列表
    // 暂时返回空数组
    return [];
  }

  /**
   * 清除配置缓存
   */
  private async clearConfigCache(type: ConfigType, scope: ConfigScope): Promise<void> {
    const pattern = `ui_config*:${type}:${scope}:*`;
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}
