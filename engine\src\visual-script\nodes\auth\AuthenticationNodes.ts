/**
 * 认证授权节点集合
 * 批次2.1 - 服务器集成节点
 * 提供JWT令牌、OAuth2认证、RBAC、权限检查等认证授权功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * JWT令牌接口
 */
export interface JWTToken {
  token: string;
  payload: any;
  header: any;
  expiresAt: Date;
  issuedAt: Date;
  issuer?: string;
  audience?: string;
}

/**
 * OAuth2配置接口
 */
export interface OAuth2Config {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scope: string[];
  authorizationUrl: string;
  tokenUrl: string;
  userInfoUrl?: string;
}

/**
 * 权限接口
 */
export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  conditions?: any;
}

/**
 * 角色接口
 */
export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  inherits?: string[];
}

/**
 * JWT令牌节点
 * 批次2.1 - 认证授权节点
 */
export class JWTTokenNode extends VisualScriptNode {
  public static readonly TYPE = 'JWTToken';
  public static readonly NAME = 'JWT令牌';
  public static readonly DESCRIPTION = '生成、验证和解析JWT令牌';

  constructor(nodeType: string = JWTTokenNode.TYPE, name: string = JWTTokenNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('action', 'string', '操作类型');
    this.addInput('payload', 'object', '载荷数据');
    this.addInput('secret', 'string', '密钥');
    this.addInput('token', 'string', 'JWT令牌');
    this.addInput('algorithm', 'string', '算法');
    this.addInput('expiresIn', 'string', '过期时间');

    // 输出端口
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('token', 'string', '生成的令牌');
    this.addOutput('payload', 'object', '解析的载荷');
    this.addOutput('valid', 'boolean', '令牌有效');
    this.addOutput('expired', 'boolean', '令牌过期');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('onSuccess', 'trigger', '操作成功事件');
    this.addOutput('onError', 'trigger', '操作错误事件');
  }

  public execute(inputs?: any): any {
    try {
      const action = inputs?.action as string || 'generate';
      const payload = inputs?.payload;
      const secret = inputs?.secret as string;
      const token = inputs?.token as string;
      const algorithm = inputs?.algorithm || 'HS256';
      const expiresIn = inputs?.expiresIn || '1h';

      if (!secret) {
        Debug.warn('JWTTokenNode', '密钥为空');
        return this.getErrorOutput('密钥不能为空');
      }

      // 执行JWT操作
      const result = this.performJWTOperation(action, payload, secret, token, algorithm, expiresIn);
      
      Debug.log('JWTTokenNode', `JWT操作${result.success ? '成功' : '失败'}: ${action}`);

      return result;
    } catch (error) {
      Debug.error('JWTTokenNode', 'JWT操作执行失败', error);
      return this.getErrorOutput(error instanceof Error ? error.message : 'JWT操作失败');
    }
  }

  private performJWTOperation(action: string, payload: any, secret: string, token: string, algorithm: string, expiresIn: string): any {
    try {
      switch (action) {
        case 'generate':
          return this.generateToken(payload, secret, algorithm, expiresIn);
        case 'verify':
          return this.verifyToken(token, secret);
        case 'decode':
          return this.decodeToken(token);
        default:
          throw new Error(`不支持的操作: ${action}`);
      }
    } catch (error) {
      return this.getErrorOutput(error instanceof Error ? error.message : 'JWT操作失败');
    }
  }

  private generateToken(payload: any, secret: string, algorithm: string, expiresIn: string): any {
    // 简化的JWT生成实现
    const header = {
      alg: algorithm,
      typ: 'JWT'
    };

    const now = new Date();
    const exp = new Date(now.getTime() + this.parseExpiresIn(expiresIn));

    const tokenPayload = {
      ...payload,
      iat: Math.floor(now.getTime() / 1000),
      exp: Math.floor(exp.getTime() / 1000)
    };

    // 模拟JWT生成
    const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
    const encodedPayload = Buffer.from(JSON.stringify(tokenPayload)).toString('base64url');
    const signature = this.generateSignature(encodedHeader, encodedPayload, secret);
    const token = `${encodedHeader}.${encodedPayload}.${signature}`;

    return {
      success: true,
      token: token,
      payload: tokenPayload,
      valid: true,
      expired: false,
      errorMessage: null,
      onSuccess: true,
      onError: false
    };
  }

  private verifyToken(token: string, secret: string): any {
    if (!token) {
      return this.getErrorOutput('令牌不能为空');
    }

    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('无效的JWT格式');
      }

      const [encodedHeader, encodedPayload, signature] = parts;
      const expectedSignature = this.generateSignature(encodedHeader, encodedPayload, secret);

      if (signature !== expectedSignature) {
        throw new Error('令牌签名无效');
      }

      const payload = JSON.parse(Buffer.from(encodedPayload, 'base64url').toString());
      const now = Math.floor(Date.now() / 1000);
      const expired = payload.exp && payload.exp < now;

      return {
        success: true,
        token: token,
        payload: payload,
        valid: !expired,
        expired: expired,
        errorMessage: expired ? '令牌已过期' : null,
        onSuccess: !expired,
        onError: expired
      };
    } catch (error) {
      return this.getErrorOutput(error instanceof Error ? error.message : '令牌验证失败');
    }
  }

  private decodeToken(token: string): any {
    if (!token) {
      return this.getErrorOutput('令牌不能为空');
    }

    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('无效的JWT格式');
      }

      const [encodedHeader, encodedPayload] = parts;
      const header = JSON.parse(Buffer.from(encodedHeader, 'base64url').toString());
      const payload = JSON.parse(Buffer.from(encodedPayload, 'base64url').toString());

      const now = Math.floor(Date.now() / 1000);
      const expired = payload.exp && payload.exp < now;

      return {
        success: true,
        token: token,
        payload: payload,
        valid: !expired,
        expired: expired,
        errorMessage: null,
        onSuccess: true,
        onError: false
      };
    } catch (error) {
      return this.getErrorOutput(error instanceof Error ? error.message : '令牌解析失败');
    }
  }

  private parseExpiresIn(expiresIn: string): number {
    // 简化的过期时间解析
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) {
      return 3600000; // 默认1小时
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value * 1000;
      case 'm': return value * 60 * 1000;
      case 'h': return value * 60 * 60 * 1000;
      case 'd': return value * 24 * 60 * 60 * 1000;
      default: return 3600000;
    }
  }

  private generateSignature(header: string, payload: string, secret: string): string {
    // 简化的签名生成（实际应使用真实的HMAC算法）
    const data = `${header}.${payload}`;
    return Buffer.from(`${data}.${secret}`).toString('base64url').substring(0, 43);
  }

  private getErrorOutput(message: string): any {
    return {
      success: false,
      token: null,
      payload: null,
      valid: false,
      expired: false,
      errorMessage: message,
      onSuccess: false,
      onError: true
    };
  }
}

/**
 * OAuth2节点
 * 批次2.1 - 认证授权节点
 */
export class OAuth2Node extends VisualScriptNode {
  public static readonly TYPE = 'OAuth2';
  public static readonly NAME = 'OAuth2认证';
  public static readonly DESCRIPTION = '处理OAuth2认证流程';

  constructor(nodeType: string = OAuth2Node.TYPE, name: string = OAuth2Node.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('action', 'string', '操作类型');
    this.addInput('config', 'object', 'OAuth2配置');
    this.addInput('authCode', 'string', '授权码');
    this.addInput('accessToken', 'string', '访问令牌');
    this.addInput('refreshToken', 'string', '刷新令牌');

    // 输出端口
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('authUrl', 'string', '授权URL');
    this.addOutput('accessToken', 'string', '访问令牌');
    this.addOutput('refreshToken', 'string', '刷新令牌');
    this.addOutput('userInfo', 'object', '用户信息');
    this.addOutput('expiresIn', 'number', '过期时间');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('onSuccess', 'trigger', '操作成功事件');
    this.addOutput('onError', 'trigger', '操作错误事件');
  }

  public execute(inputs?: any): any {
    try {
      const action = inputs?.action as string || 'getAuthUrl';
      const config = inputs?.config as OAuth2Config;
      const authCode = inputs?.authCode as string;
      const accessToken = inputs?.accessToken as string;
      const refreshToken = inputs?.refreshToken as string;

      if (!config) {
        Debug.warn('OAuth2Node', 'OAuth2配置为空');
        return this.getErrorOutput('OAuth2配置不能为空');
      }

      // 执行OAuth2操作
      const result = this.performOAuth2Operation(action, config, authCode, accessToken, refreshToken);
      
      Debug.log('OAuth2Node', `OAuth2操作${result.success ? '成功' : '失败'}: ${action}`);

      return result;
    } catch (error) {
      Debug.error('OAuth2Node', 'OAuth2操作执行失败', error);
      return this.getErrorOutput(error instanceof Error ? error.message : 'OAuth2操作失败');
    }
  }

  private performOAuth2Operation(action: string, config: OAuth2Config, authCode: string, accessToken: string, refreshToken: string): any {
    try {
      switch (action) {
        case 'getAuthUrl':
          return this.getAuthorizationUrl(config);
        case 'exchangeToken':
          return this.exchangeCodeForToken(config, authCode);
        case 'refreshToken':
          return this.refreshAccessToken(config, refreshToken);
        case 'getUserInfo':
          return this.getUserInfo(config, accessToken);
        default:
          throw new Error(`不支持的操作: ${action}`);
      }
    } catch (error) {
      return this.getErrorOutput(error instanceof Error ? error.message : 'OAuth2操作失败');
    }
  }

  private getAuthorizationUrl(config: OAuth2Config): any {
    const params = new URLSearchParams({
      client_id: config.clientId,
      redirect_uri: config.redirectUri,
      scope: config.scope.join(' '),
      response_type: 'code',
      state: Math.random().toString(36).substring(2)
    });

    const authUrl = `${config.authorizationUrl}?${params.toString()}`;

    return {
      success: true,
      authUrl: authUrl,
      accessToken: null,
      refreshToken: null,
      userInfo: null,
      expiresIn: null,
      errorMessage: null,
      onSuccess: true,
      onError: false
    };
  }

  private exchangeCodeForToken(config: OAuth2Config, authCode: string): any {
    if (!authCode) {
      return this.getErrorOutput('授权码不能为空');
    }

    // 模拟令牌交换
    const accessToken = `access_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const refreshToken = `refresh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const expiresIn = 3600; // 1小时

    return {
      success: true,
      authUrl: null,
      accessToken: accessToken,
      refreshToken: refreshToken,
      userInfo: null,
      expiresIn: expiresIn,
      errorMessage: null,
      onSuccess: true,
      onError: false
    };
  }

  private refreshAccessToken(config: OAuth2Config, refreshToken: string): any {
    if (!refreshToken) {
      return this.getErrorOutput('刷新令牌不能为空');
    }

    // 模拟令牌刷新
    const newAccessToken = `access_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const expiresIn = 3600; // 1小时

    return {
      success: true,
      authUrl: null,
      accessToken: newAccessToken,
      refreshToken: refreshToken,
      userInfo: null,
      expiresIn: expiresIn,
      errorMessage: null,
      onSuccess: true,
      onError: false
    };
  }

  private getUserInfo(config: OAuth2Config, accessToken: string): any {
    if (!accessToken) {
      return this.getErrorOutput('访问令牌不能为空');
    }

    // 模拟用户信息获取
    const userInfo = {
      id: '12345',
      name: '示例用户',
      email: '<EMAIL>',
      avatar: 'https://example.com/avatar.jpg'
    };

    return {
      success: true,
      authUrl: null,
      accessToken: accessToken,
      refreshToken: null,
      userInfo: userInfo,
      expiresIn: null,
      errorMessage: null,
      onSuccess: true,
      onError: false
    };
  }

  private getErrorOutput(message: string): any {
    return {
      success: false,
      authUrl: null,
      accessToken: null,
      refreshToken: null,
      userInfo: null,
      expiresIn: null,
      errorMessage: message,
      onSuccess: false,
      onError: true
    };
  }
}

/**
 * RBAC节点
 * 批次2.1 - 认证授权节点
 */
export class RBACNode extends VisualScriptNode {
  public static readonly TYPE = 'RBAC';
  public static readonly NAME = '基于角色的访问控制';
  public static readonly DESCRIPTION = '管理基于角色的访问控制';

  constructor(nodeType: string = RBACNode.TYPE, name: string = RBACNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('action', 'string', '操作类型');
    this.addInput('userId', 'string', '用户ID');
    this.addInput('roleId', 'string', '角色ID');
    this.addInput('resource', 'string', '资源');
    this.addInput('operation', 'string', '操作');
    this.addInput('context', 'object', '上下文');

    // 输出端口
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('allowed', 'boolean', '访问允许');
    this.addOutput('roles', 'array', '用户角色');
    this.addOutput('permissions', 'array', '用户权限');
    this.addOutput('reason', 'string', '拒绝原因');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('onAllowed', 'trigger', '访问允许事件');
    this.addOutput('onDenied', 'trigger', '访问拒绝事件');
    this.addOutput('onError', 'trigger', '操作错误事件');
  }

  public execute(inputs?: any): any {
    try {
      const action = inputs?.action as string || 'checkAccess';
      const userId = inputs?.userId as string;
      const roleId = inputs?.roleId as string;
      const resource = inputs?.resource as string;
      const operation = inputs?.operation as string;
      const context = inputs?.context || {};

      // 执行RBAC操作
      const result = this.performRBACOperation(action, userId, roleId, resource, operation, context);

      Debug.log('RBACNode', `RBAC操作${result.success ? '成功' : '失败'}: ${action}`);

      return result;
    } catch (error) {
      Debug.error('RBACNode', 'RBAC操作执行失败', error);
      return this.getErrorOutput(error instanceof Error ? error.message : 'RBAC操作失败');
    }
  }

  private performRBACOperation(action: string, userId: string, roleId: string, resource: string, operation: string, context: any): any {
    try {
      switch (action) {
        case 'checkAccess':
          return this.checkAccess(userId, resource, operation, context);
        case 'getUserRoles':
          return this.getUserRoles(userId);
        case 'getUserPermissions':
          return this.getUserPermissions(userId);
        case 'assignRole':
          return this.assignRole(userId, roleId);
        case 'revokeRole':
          return this.revokeRole(userId, roleId);
        default:
          throw new Error(`不支持的操作: ${action}`);
      }
    } catch (error) {
      return this.getErrorOutput(error instanceof Error ? error.message : 'RBAC操作失败');
    }
  }

  private checkAccess(userId: string, resource: string, operation: string, context: any): any {
    if (!userId || !resource || !operation) {
      return this.getErrorOutput('用户ID、资源和操作不能为空');
    }

    // 模拟权限检查
    const userRoles = this.mockGetUserRoles(userId);
    const userPermissions = this.mockGetUserPermissions(userRoles);

    const hasPermission = userPermissions.some(permission =>
      permission.resource === resource &&
      (permission.action === operation || permission.action === '*')
    );

    return {
      success: true,
      allowed: hasPermission,
      roles: userRoles,
      permissions: userPermissions,
      reason: hasPermission ? null : `用户没有对资源 ${resource} 执行 ${operation} 操作的权限`,
      errorMessage: null,
      onAllowed: hasPermission,
      onDenied: !hasPermission,
      onError: false
    };
  }

  private getUserRoles(userId: string): any {
    if (!userId) {
      return this.getErrorOutput('用户ID不能为空');
    }

    const roles = this.mockGetUserRoles(userId);

    return {
      success: true,
      allowed: null,
      roles: roles,
      permissions: [],
      reason: null,
      errorMessage: null,
      onAllowed: false,
      onDenied: false,
      onError: false
    };
  }

  private getUserPermissions(userId: string): any {
    if (!userId) {
      return this.getErrorOutput('用户ID不能为空');
    }

    const roles = this.mockGetUserRoles(userId);
    const permissions = this.mockGetUserPermissions(roles);

    return {
      success: true,
      allowed: null,
      roles: roles,
      permissions: permissions,
      reason: null,
      errorMessage: null,
      onAllowed: false,
      onDenied: false,
      onError: false
    };
  }

  private assignRole(userId: string, roleId: string): any {
    if (!userId || !roleId) {
      return this.getErrorOutput('用户ID和角色ID不能为空');
    }

    // 模拟角色分配
    return {
      success: true,
      allowed: null,
      roles: [roleId],
      permissions: [],
      reason: null,
      errorMessage: null,
      onAllowed: false,
      onDenied: false,
      onError: false
    };
  }

  private revokeRole(userId: string, roleId: string): any {
    if (!userId || !roleId) {
      return this.getErrorOutput('用户ID和角色ID不能为空');
    }

    // 模拟角色撤销
    return {
      success: true,
      allowed: null,
      roles: [],
      permissions: [],
      reason: null,
      errorMessage: null,
      onAllowed: false,
      onDenied: false,
      onError: false
    };
  }

  private mockGetUserRoles(userId: string): Role[] {
    // 模拟用户角色数据
    return [
      {
        id: 'user',
        name: '普通用户',
        description: '基础用户权限',
        permissions: [
          { id: 'read_profile', name: '读取个人资料', resource: 'profile', action: 'read' }
        ]
      }
    ];
  }

  private mockGetUserPermissions(roles: Role[]): Permission[] {
    // 从角色中提取权限
    const permissions: Permission[] = [];
    for (const role of roles) {
      permissions.push(...role.permissions);
    }
    return permissions;
  }

  private getErrorOutput(message: string): any {
    return {
      success: false,
      allowed: false,
      roles: [],
      permissions: [],
      reason: message,
      errorMessage: message,
      onAllowed: false,
      onDenied: false,
      onError: true
    };
  }
}
