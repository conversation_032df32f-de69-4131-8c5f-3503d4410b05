import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import Web3 from 'web3';
import { Contract } from 'web3-eth-contract';
import { BlockTransactionString } from 'web3-eth';

/**
 * 网络配置接口
 */
interface NetworkConfig {
  name: string;
  chainId: number;
  rpcUrl: string;
  explorerUrl: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
}

/**
 * 钱包信息接口
 */
interface WalletInfo {
  address: string;
  balance: string;
  nonce: number;
  isContract: boolean;
}

/**
 * 交易配置接口
 */
interface TransactionConfig {
  from: string;
  to: string;
  value?: string;
  data?: string;
  gas?: number;
  gasPrice?: string;
  nonce?: number;
}

/**
 * 事件监听配置接口
 */
interface EventListenerConfig {
  contractAddress: string;
  eventName: string;
  fromBlock?: number;
  toBlock?: number;
  filter?: any;
  callback: (event: any) => void;
}

/**
 * Web3服务
 */
@Injectable()
export class Web3Service {
  private readonly logger = new Logger(Web3Service.name);
  private web3: Web3;
  private networks: Map<string, NetworkConfig> = new Map();
  private contracts: Map<string, Contract> = new Map();
  private eventListeners: Map<string, any> = new Map();

  constructor(
    private configService: ConfigService,
    private eventEmitter: EventEmitter2
  ) {
    this.initializeNetworks();
    this.initializeWeb3();
  }

  /**
   * 获取当前网络信息
   */
  async getNetworkInfo(): Promise<any> {
    try {
      const chainId = await this.web3.eth.getChainId();
      const blockNumber = await this.web3.eth.getBlockNumber();
      const gasPrice = await this.web3.eth.getGasPrice();
      
      return {
        chainId,
        blockNumber,
        gasPrice,
        isConnected: true
      };

    } catch (error) {
      this.logger.error('获取网络信息失败', error);
      return {
        isConnected: false,
        error: error.message
      };
    }
  }

  /**
   * 获取区块信息
   */
  async getBlock(blockNumber: number | 'latest' = 'latest'): Promise<BlockTransactionString | null> {
    try {
      return await this.web3.eth.getBlock(blockNumber, true);
    } catch (error) {
      this.logger.error('获取区块信息失败', error);
      return null;
    }
  }

  /**
   * 获取交易信息
   */
  async getTransaction(txHash: string): Promise<any> {
    try {
      const [transaction, receipt] = await Promise.all([
        this.web3.eth.getTransaction(txHash),
        this.web3.eth.getTransactionReceipt(txHash)
      ]);

      return {
        transaction,
        receipt,
        confirmations: receipt ? await this.getConfirmations(receipt.blockNumber) : 0
      };

    } catch (error) {
      this.logger.error('获取交易信息失败', error);
      return null;
    }
  }

  /**
   * 获取钱包信息
   */
  async getWalletInfo(address: string): Promise<WalletInfo> {
    try {
      const [balance, nonce, code] = await Promise.all([
        this.web3.eth.getBalance(address),
        this.web3.eth.getTransactionCount(address),
        this.web3.eth.getCode(address)
      ]);

      return {
        address,
        balance: this.web3.utils.fromWei(balance, 'ether'),
        nonce,
        isContract: code !== '0x'
      };

    } catch (error) {
      this.logger.error('获取钱包信息失败', error);
      throw error;
    }
  }

  /**
   * 创建钱包
   */
  createWallet(): { address: string; privateKey: string; mnemonic?: string } {
    try {
      const account = this.web3.eth.accounts.create();
      
      return {
        address: account.address,
        privateKey: account.privateKey
      };

    } catch (error) {
      this.logger.error('创建钱包失败', error);
      throw error;
    }
  }

  /**
   * 从私钥导入钱包
   */
  importWalletFromPrivateKey(privateKey: string): { address: string; privateKey: string } {
    try {
      const account = this.web3.eth.accounts.privateKeyToAccount(privateKey);
      
      return {
        address: account.address,
        privateKey: account.privateKey
      };

    } catch (error) {
      this.logger.error('从私钥导入钱包失败', error);
      throw error;
    }
  }

  /**
   * 发送交易
   */
  async sendTransaction(config: TransactionConfig, privateKey: string): Promise<string> {
    try {
      // 估算Gas
      if (!config.gas) {
        config.gas = await this.web3.eth.estimateGas({
          from: config.from,
          to: config.to,
          value: config.value || '0',
          data: config.data
        });
      }

      // 获取Gas价格
      if (!config.gasPrice) {
        config.gasPrice = await this.web3.eth.getGasPrice();
      }

      // 获取nonce
      if (!config.nonce) {
        config.nonce = await this.web3.eth.getTransactionCount(config.from);
      }

      // 签名交易
      const signedTx = await this.web3.eth.accounts.signTransaction({
        from: config.from,
        to: config.to,
        value: config.value || '0',
        data: config.data || '0x',
        gas: config.gas.toString(),
        gasPrice: config.gasPrice.toString(),
        nonce: config.nonce
      }, privateKey);

      // 发送交易
      const receipt = await this.web3.eth.sendSignedTransaction(signedTx.rawTransaction!);
      
      this.logger.log(`交易发送成功: ${receipt.transactionHash}`);
      return receipt.transactionHash;

    } catch (error) {
      this.logger.error('发送交易失败', error);
      throw error;
    }
  }

  /**
   * 批量发送交易
   */
  async sendBatchTransactions(
    configs: TransactionConfig[],
    privateKeys: string[]
  ): Promise<string[]> {
    const results: string[] = [];

    for (let i = 0; i < configs.length; i++) {
      try {
        const txHash = await this.sendTransaction(configs[i], privateKeys[i]);
        results.push(txHash);
      } catch (error) {
        this.logger.error(`批量交易第${i + 1}个失败`, error);
        results.push('');
      }
    }

    return results;
  }

  /**
   * 等待交易确认
   */
  async waitForTransaction(
    txHash: string,
    confirmations: number = 1,
    timeout: number = 300000 // 5分钟
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkTransaction = async () => {
        try {
          const receipt = await this.web3.eth.getTransactionReceipt(txHash);
          
          if (receipt) {
            const currentConfirmations = await this.getConfirmations(receipt.blockNumber);
            
            if (currentConfirmations >= confirmations) {
              resolve({
                receipt,
                confirmations: currentConfirmations
              });
              return;
            }
          }

          // 检查超时
          if (Date.now() - startTime > timeout) {
            reject(new Error('交易确认超时'));
            return;
          }

          // 继续等待
          setTimeout(checkTransaction, 5000);

        } catch (error) {
          reject(error);
        }
      };

      checkTransaction();
    });
  }

  /**
   * 获取合约实例
   */
  getContract(address: string, abi: any[]): Contract {
    const key = `${address}_${JSON.stringify(abi).slice(0, 100)}`;
    
    if (this.contracts.has(key)) {
      return this.contracts.get(key)!;
    }

    const contract = new this.web3.eth.Contract(abi, address);
    this.contracts.set(key, contract);
    
    return contract;
  }

  /**
   * 调用合约只读方法
   */
  async callContractMethod(
    contractAddress: string,
    abi: any[],
    methodName: string,
    methodArgs: any[] = [],
    fromAddress?: string
  ): Promise<any> {
    try {
      const contract = this.getContract(contractAddress, abi);
      
      if (!contract.methods[methodName]) {
        throw new Error(`合约方法不存在: ${methodName}`);
      }

      const result = await contract.methods[methodName](...methodArgs).call({
        from: fromAddress
      });

      return result;

    } catch (error) {
      this.logger.error('调用合约方法失败', error);
      throw error;
    }
  }

  /**
   * 监听合约事件
   */
  async listenToContractEvents(config: EventListenerConfig): Promise<string> {
    try {
      const contract = this.contracts.get(config.contractAddress);
      if (!contract) {
        throw new Error(`合约实例不存在: ${config.contractAddress}`);
      }

      const eventEmitter = contract.events[config.eventName]({
        fromBlock: config.fromBlock || 'latest',
        toBlock: config.toBlock || 'latest',
        filter: config.filter
      });

      const listenerId = `${config.contractAddress}_${config.eventName}_${Date.now()}`;

      eventEmitter.on('data', (event) => {
        this.logger.log(`收到合约事件: ${config.eventName} from ${config.contractAddress}`);
        config.callback(event);
        
        // 发送全局事件
        this.eventEmitter.emit('contract.event', {
          contractAddress: config.contractAddress,
          eventName: config.eventName,
          event
        });
      });

      eventEmitter.on('error', (error) => {
        this.logger.error(`合约事件监听错误: ${config.contractAddress} - ${config.eventName}`, error);
      });

      this.eventListeners.set(listenerId, eventEmitter);

      this.logger.log(`开始监听合约事件: ${config.contractAddress} - ${config.eventName}`);
      return listenerId;

    } catch (error) {
      this.logger.error('设置合约事件监听失败', error);
      throw error;
    }
  }

  /**
   * 停止监听合约事件
   */
  stopListeningToEvents(listenerId: string): boolean {
    const eventEmitter = this.eventListeners.get(listenerId);
    if (eventEmitter) {
      eventEmitter.removeAllListeners();
      this.eventListeners.delete(listenerId);
      this.logger.log(`停止监听合约事件: ${listenerId}`);
      return true;
    }
    return false;
  }

  /**
   * 编码函数调用数据
   */
  encodeFunctionCall(abi: any[], methodName: string, methodArgs: any[]): string {
    try {
      const contract = new this.web3.eth.Contract(abi);
      return contract.methods[methodName](...methodArgs).encodeABI();
    } catch (error) {
      this.logger.error('编码函数调用失败', error);
      throw error;
    }
  }

  /**
   * 解码函数调用数据
   */
  decodeFunctionCall(abi: any[], data: string): any {
    try {
      // 简化实现，实际需要更复杂的解码逻辑
      return this.web3.eth.abi.decodeParameters(abi, data);
    } catch (error) {
      this.logger.error('解码函数调用失败', error);
      throw error;
    }
  }

  /**
   * 获取事件日志
   */
  async getEventLogs(
    contractAddress: string,
    abi: any[],
    eventName: string,
    fromBlock: number = 0,
    toBlock: number | 'latest' = 'latest',
    filter?: any
  ): Promise<any[]> {
    try {
      const contract = this.getContract(contractAddress, abi);
      
      const events = await contract.getPastEvents(eventName, {
        fromBlock,
        toBlock,
        filter
      });

      return events;

    } catch (error) {
      this.logger.error('获取事件日志失败', error);
      throw error;
    }
  }

  /**
   * 验证地址格式
   */
  isValidAddress(address: string): boolean {
    return this.web3.utils.isAddress(address);
  }

  /**
   * 转换单位
   */
  convertUnits(value: string, fromUnit: string, toUnit: string): string {
    try {
      if (fromUnit === 'wei' && toUnit === 'ether') {
        return this.web3.utils.fromWei(value, 'ether');
      } else if (fromUnit === 'ether' && toUnit === 'wei') {
        return this.web3.utils.toWei(value, 'ether');
      }
      
      // 其他单位转换
      return this.web3.utils.fromWei(this.web3.utils.toWei(value, fromUnit as any), toUnit as any);

    } catch (error) {
      this.logger.error('单位转换失败', error);
      throw error;
    }
  }

  /**
   * 获取支持的网络列表
   */
  getSupportedNetworks(): NetworkConfig[] {
    return Array.from(this.networks.values());
  }

  /**
   * 切换网络
   */
  async switchNetwork(networkName: string): Promise<boolean> {
    try {
      const network = this.networks.get(networkName);
      if (!network) {
        throw new Error(`不支持的网络: ${networkName}`);
      }

      this.web3 = new Web3(network.rpcUrl);
      
      // 验证连接
      await this.web3.eth.getChainId();
      
      this.logger.log(`已切换到网络: ${networkName}`);
      return true;

    } catch (error) {
      this.logger.error('切换网络失败', error);
      return false;
    }
  }

  // 私有方法

  /**
   * 初始化网络配置
   */
  private initializeNetworks(): void {
    // 以太坊主网
    this.networks.set('ethereum', {
      name: 'Ethereum Mainnet',
      chainId: 1,
      rpcUrl: this.configService.get('blockchain.ethereum.rpcUrl'),
      explorerUrl: 'https://etherscan.io',
      nativeCurrency: {
        name: 'Ether',
        symbol: 'ETH',
        decimals: 18
      }
    });

    // 以太坊测试网
    this.networks.set('goerli', {
      name: 'Goerli Testnet',
      chainId: 5,
      rpcUrl: this.configService.get('blockchain.goerli.rpcUrl'),
      explorerUrl: 'https://goerli.etherscan.io',
      nativeCurrency: {
        name: 'Goerli Ether',
        symbol: 'GoerliETH',
        decimals: 18
      }
    });

    // BSC主网
    this.networks.set('bsc', {
      name: 'Binance Smart Chain',
      chainId: 56,
      rpcUrl: this.configService.get('blockchain.bsc.rpcUrl'),
      explorerUrl: 'https://bscscan.com',
      nativeCurrency: {
        name: 'Binance Coin',
        symbol: 'BNB',
        decimals: 18
      }
    });

    this.logger.log('网络配置初始化完成');
  }

  /**
   * 初始化Web3
   */
  private initializeWeb3(): void {
    const defaultRpcUrl = this.configService.get('blockchain.ethereum.rpcUrl');
    this.web3 = new Web3(defaultRpcUrl);
    this.logger.log('Web3初始化完成');
  }

  /**
   * 获取确认数
   */
  private async getConfirmations(blockNumber: number): Promise<number> {
    try {
      const currentBlock = await this.web3.eth.getBlockNumber();
      return currentBlock - blockNumber;
    } catch (error) {
      return 0;
    }
  }
}
