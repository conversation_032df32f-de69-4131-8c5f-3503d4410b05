# DL引擎微服务架构文档

## 📋 概述

DL引擎是一个基于微服务架构的分布式系统，包含60个微服务，涵盖AI/ML、工业制造、游戏渲染、边缘计算等多个领域。

## 🏗️ 系统架构

### 核心组件

#### 1. API网关 (api-gateway)
- **功能**: 统一入口，路由分发，认证授权
- **端口**: 3000
- **技术栈**: NestJS + Express

#### 2. 服务注册中心 (service-registry)
- **功能**: 服务注册与发现
- **端口**: 3001
- **技术栈**: NestJS + Redis

#### 3. 用户服务 (user-service)
- **功能**: 用户管理，认证授权
- **端口**: 3002
- **技术栈**: NestJS + MySQL

### 服务分类

#### 🤖 AI/ML服务 (8个)
- ai-model-service: AI模型管理和推理
- deeplearning-service: 深度学习训练服务
- recommendation-service: 智能推荐系统
- emotion-service: 情感分析服务
- nlp-scene-service: NLP场景生成
- perception-service: 感知数据处理
- ai-service: 基础AI服务
- ai-engine-service: AI引擎核心

#### 🏭 工业/制造服务 (7个)
- mes-service: 制造执行系统
- predictive-maintenance-service: 预测性维护
- industrial-data-service: 工业数据管理
- intelligent-scheduling-service: 智能调度系统
- knowledge-graph-service: 工业知识图谱
- behavior-decision-service: 行为决策服务
- human-machine-collaboration-service: 人机协作

#### 🎮 游戏/渲染服务 (6个)
- render-service: 3D渲染服务
- voice-service: 语音处理服务
- avatar-service: 虚拟角色服务
- visual-script-service: 可视化脚本
- game-server: 游戏服务器
- ui-service: UI组件管理

#### 🌐 边缘计算服务 (8个)
- edge-registry: 边缘节点注册
- edge-router: 边缘路由服务
- edge-game-server: 边缘游戏服务器
- edge-ai-service: 边缘AI计算
- edge-enhancement: 边缘增强
- cloud-edge-orchestration-service: 云边协调
- enterprise-integration-service: 企业集成
- 5g-network-service: 5G网络服务

## 🔗 通信协议

### HTTP API标准
所有服务遵循统一的HTTP API响应格式：

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2025-07-02T13:47:09.508Z",
  "requestId": "req_1719928029508_abc123"
}
```

### 微服务间通信
- **同步通信**: HTTP/REST API
- **异步通信**: Redis Pub/Sub, Message Queue
- **服务发现**: 基于Redis的服务注册中心

## 🛡️ 安全架构

### 认证授权
- JWT Token认证
- RBAC权限控制
- API密钥管理

### 数据安全
- 数据加密传输 (HTTPS)
- 敏感数据脱敏
- 审计日志记录

## 📊 监控体系

### 指标监控
- Prometheus + Grafana
- 服务健康检查
- 性能指标收集

### 日志管理
- 结构化日志格式
- 集中式日志收集
- 日志分析和告警

## 🚀 部署架构

### 容器化部署
- Docker容器化
- Docker Compose编排
- Kubernetes集群部署

### 环境管理
- 开发环境 (Development)
- 测试环境 (Testing)
- 生产环境 (Production)

## 📈 扩展性设计

### 水平扩展
- 无状态服务设计
- 负载均衡
- 数据库分片

### 高可用性
- 服务冗余部署
- 故障转移机制
- 数据备份策略
