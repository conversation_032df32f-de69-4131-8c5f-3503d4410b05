/**
 * 初始数据库迁移
 */

import type {  MigrationInterface, QueryRunner  } from 'typeorm';

export class InitialMigration1703000000000 implements MigrationInterface {
  name = 'InitialMigration1703000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 这里可以添加数据库表创建的SQL语句
    // 由于我们使用了synchronize: true，在开发环境中会自动创建表
    // 在生产环境中，应该使用具体的迁移SQL
    
    await queryRunner.query(`
      -- 创建系统配置表（如果不存在）
      CREATE TABLE IF NOT EXISTS system_configs (
        id VARCHAR(36) PRIMARY KEY,
        config_key VARCHAR(255) NOT NULL UNIQUE,
        config_value TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 插入默认配置
    await queryRunner.query(`
      INSERT IGNORE INTO system_configs (id, config_key, config_value, description) VALUES
      (UUID(), 'recommendation_algorithm', '{"name": "personalized_hybrid", "version": "1.0", "enabled": true}', '推荐算法配置'),
      (UUID(), 'sync_settings', '{"batch_size": 50, "max_retries": 3, "retry_delay": 5000}', '数据同步设置'),
      (UUID(), 'profile_analysis', '{"min_data_points": 10, "analysis_depth": "detailed", "update_frequency": "daily"}', '用户画像分析配置'),
      (UUID(), 'xapi_settings', '{"version": "2.0.0", "timeout": 30000, "enable_validation": true}', 'xAPI协议设置');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 回滚操作
    await queryRunner.query(`DROP TABLE IF EXISTS system_configs;`);
  }
}
