import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ThrottlerModule } from '@nestjs/throttler';
import { TerminusModule } from '@nestjs/terminus';

// 导入业务模块
import { EnterpriseIntegrationModule } from './integration/enterprise-integration.module';
import { HealthModule } from './health/health.module';

// 导入配置
import databaseConfig from './config/database.config';
import redisConfig from './config/redis.config';

/**
 * 企业系统深度集成服务主应用模块
 * 整合所有功能模块和基础设施配置
 */
@Module({
  imports: [
    // 配置模块 - 全局配置管理
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      load: [databaseConfig, redisConfig],
      cache: true,
      expandVariables: true,
    }),

    // 数据库模块 - TypeORM配置
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get<string>('database.host'),
        port: configService.get<number>('database.port'),
        username: configService.get<string>('database.username'),
        password: configService.get<string>('database.password'),
        database: configService.get<string>('database.database'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: configService.get<boolean>('database.synchronize', false),
        logging: configService.get<boolean>('database.logging', false),
        retryAttempts: 3,
        retryDelay: 3000,
        autoLoadEntities: true,
        keepConnectionAlive: true,
        timezone: '+08:00',
        charset: 'utf8mb4',
        extra: {
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
        },
      }),
      inject: [ConfigService],
    }),

    // 事件发射器模块 - 事件驱动架构
    EventEmitterModule.forRoot({
      wildcard: true,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 20,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // 定时任务模块 - 计划任务调度
    ScheduleModule.forRoot(),

    // 限流模块 - API访问频率控制
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        ttl: configService.get<number>('THROTTLE_TTL', 60),
        limit: configService.get<number>('THROTTLE_LIMIT', 100),
      }),
      inject: [ConfigService],
    }),

    // 健康检查模块
    TerminusModule,

    // 业务功能模块
    EnterpriseIntegrationModule,
    HealthModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    // 应用启动时的配置验证
    this.validateConfiguration();
  }

  /**
   * 验证关键配置项
   */
  private validateConfiguration(): void {
    const requiredConfigs = [
      'database.host',
      'database.port',
      'database.username',
      'database.database',
    ];

    for (const config of requiredConfigs) {
      if (!this.configService.get(config)) {
        throw new Error(`缺少必要配置: ${config}`);
      }
    }
  }
}
