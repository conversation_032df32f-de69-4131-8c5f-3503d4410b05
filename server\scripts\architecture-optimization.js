#!/usr/bin/env node

/**
 * DL引擎微服务项目架构优化脚本
 * 微服务通信协议标准化、统一错误处理和日志格式、完善监控告警体系、优化容器化部署策略
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ArchitectureOptimizer {
  constructor() {
    this.serverDir = path.join(__dirname, '..');
    this.services = this.getServiceDirectories();
    this.report = {
      timestamp: new Date().toISOString(),
      services: {},
      summary: {
        total: 0,
        processed: 0,
        errors: 0,
        warnings: 0
      }
    };
  }

  /**
   * 获取所有微服务目录
   */
  getServiceDirectories() {
    const items = fs.readdirSync(this.serverDir);
    return items.filter(item => {
      const itemPath = path.join(this.serverDir, item);
      const stat = fs.statSync(itemPath);
      return stat.isDirectory() && 
             !['node_modules', 'dist', 'scripts', 'docs', 'shared', 'database', 'src'].includes(item) &&
             !item.startsWith('.') &&
             !item.endsWith('-reports');
    });
  }

  /**
   * 主执行函数
   */
  async run() {
    console.log('🏗️ 开始架构优化...\n');
    
    this.report.summary.total = this.services.length;

    // 首先创建共享架构组件
    await this.createSharedComponents();

    for (const service of this.services) {
      console.log(`📦 处理服务: ${service}`);
      await this.processService(service);
      console.log('');
    }

    this.generateReport();
    this.printSummary();
  }

  /**
   * 创建共享架构组件
   */
  async createSharedComponents() {
    console.log('🔧 创建共享架构组件...');
    
    // 创建通信协议标准
    await this.createCommunicationProtocol();
    
    // 创建统一错误处理
    await this.createErrorHandling();
    
    // 创建统一日志格式
    await this.createLoggingStandard();
    
    // 创建监控配置
    await this.createMonitoringConfig();
    
    console.log('✅ 共享架构组件创建完成\n');
  }

  /**
   * 创建通信协议标准
   */
  async createCommunicationProtocol() {
    const protocolDir = path.join(this.serverDir, 'shared', 'protocols');
    if (!fs.existsSync(protocolDir)) {
      fs.mkdirSync(protocolDir, { recursive: true });
    }

    // HTTP通信协议
    const httpProtocol = `/**
 * HTTP通信协议标准
 */

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: ApiError;
  timestamp: string;
  requestId: string;
  version: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  stack?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class HttpProtocol {
  static success<T>(data: T, message?: string): ApiResponse<T> {
    return {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
      requestId: this.generateRequestId(),
      version: '1.0.0'
    };
  }

  static error(error: ApiError): ApiResponse {
    return {
      success: false,
      error,
      timestamp: new Date().toISOString(),
      requestId: this.generateRequestId(),
      version: '1.0.0'
    };
  }

  static paginated<T>(
    data: T[],
    page: number,
    limit: number,
    total: number
  ): PaginatedResponse<T> {
    return {
      success: true,
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      timestamp: new Date().toISOString(),
      requestId: this.generateRequestId(),
      version: '1.0.0'
    };
  }

  private static generateRequestId(): string {
    return \`req_\$\{Date.now()\}_\$\{Math.random().toString(36).substr(2, 9)\}\`;
  }
}`;

    fs.writeFileSync(path.join(protocolDir, 'http-protocol.ts'), httpProtocol);

    // 微服务间通信协议
    const microserviceProtocol = `/**
 * 微服务间通信协议
 */

export interface ServiceMessage<T = any> {
  id: string;
  type: string;
  source: string;
  target: string;
  payload: T;
  timestamp: string;
  correlationId?: string;
  replyTo?: string;
  headers?: Record<string, any>;
}

export interface ServiceEvent<T = any> {
  id: string;
  type: string;
  source: string;
  payload: T;
  timestamp: string;
  version: string;
  metadata?: Record<string, any>;
}

export class MicroserviceProtocol {
  static createMessage<T>(
    type: string,
    source: string,
    target: string,
    payload: T,
    options?: {
      correlationId?: string;
      replyTo?: string;
      headers?: Record<string, any>;
    }
  ): ServiceMessage<T> {
    return {
      id: this.generateId(),
      type,
      source,
      target,
      payload,
      timestamp: new Date().toISOString(),
      ...options
    };
  }

  static createEvent<T>(
    type: string,
    source: string,
    payload: T,
    metadata?: Record<string, any>
  ): ServiceEvent<T> {
    return {
      id: this.generateId(),
      type,
      source,
      payload,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      metadata
    };
  }

  private static generateId(): string {
    return \`msg_\${Date.now()}_\${Math.random().toString(36).substr(2, 9)}\`;
  }
}`;

    fs.writeFileSync(path.join(protocolDir, 'microservice-protocol.ts'), microserviceProtocol);
  }

  /**
   * 创建统一错误处理
   */
  async createErrorHandling() {
    const errorDir = path.join(this.serverDir, 'shared', 'errors');
    if (!fs.existsSync(errorDir)) {
      fs.mkdirSync(errorDir, { recursive: true });
    }

    const errorHandler = `/**
 * 统一错误处理系统
 */

export enum ErrorCode {
  // 通用错误
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  BAD_REQUEST = 'BAD_REQUEST',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  
  // 业务错误
  BUSINESS_LOGIC_ERROR = 'BUSINESS_LOGIC_ERROR',
  RESOURCE_NOT_AVAILABLE = 'RESOURCE_NOT_AVAILABLE',
  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',
  
  // 系统错误
  DATABASE_ERROR = 'DATABASE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR'
}

export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly details?: any;

  constructor(
    code: ErrorCode,
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    details?: any
  ) {
    super(message);
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.details = details;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ErrorHandler {
  static handle(error: Error): {
    code: ErrorCode;
    message: string;
    statusCode: number;
    details?: any;
  } {
    if (error instanceof AppError) {
      return {
        code: error.code,
        message: error.message,
        statusCode: error.statusCode,
        details: error.details
      };
    }

    // 处理其他类型的错误
    return {
      code: ErrorCode.INTERNAL_SERVER_ERROR,
      message: 'Internal server error',
      statusCode: 500,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    };
  }

  static createValidationError(details: any): AppError {
    return new AppError(
      ErrorCode.VALIDATION_ERROR,
      'Validation failed',
      400,
      true,
      details
    );
  }

  static createNotFoundError(resource: string): AppError {
    return new AppError(
      ErrorCode.NOT_FOUND,
      \`\${resource} not found\`,
      404,
      true
    );
  }

  static createUnauthorizedError(): AppError {
    return new AppError(
      ErrorCode.UNAUTHORIZED,
      'Unauthorized access',
      401,
      true
    );
  }
}`;

    fs.writeFileSync(path.join(errorDir, 'error-handler.ts'), errorHandler);
  }

  /**
   * 创建统一日志格式
   */
  async createLoggingStandard() {
    const loggingDir = path.join(this.serverDir, 'shared', 'logging');
    if (!fs.existsSync(loggingDir)) {
      fs.mkdirSync(loggingDir, { recursive: true });
    }

    const logger = `/**
 * 统一日志格式标准
 */

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  TRACE = 'trace'
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  service: string;
  message: string;
  context?: Record<string, any>;
  requestId?: string;
  userId?: string;
  correlationId?: string;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}

export class Logger {
  private serviceName: string;

  constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  private log(level: LogLevel, message: string, context?: Record<string, any>): void {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      service: this.serviceName,
      message,
      context
    };

    // 在生产环境中，这里应该发送到日志聚合系统
    console.log(JSON.stringify(entry));
  }

  error(message: string, error?: Error, context?: Record<string, any>): void {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: LogLevel.ERROR,
      service: this.serviceName,
      message,
      context,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : undefined
    };

    console.error(JSON.stringify(entry));
  }

  warn(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context);
  }

  info(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context);
  }

  debug(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  trace(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.TRACE, message, context);
  }
}

export const createLogger = (serviceName: string): Logger => {
  return new Logger(serviceName);
};`;

    fs.writeFileSync(path.join(loggingDir, 'logger.ts'), logger);
  }

  /**
   * 创建监控配置
   */
  async createMonitoringConfig() {
    const monitoringDir = path.join(this.serverDir, 'shared', 'monitoring');
    if (!fs.existsSync(monitoringDir)) {
      fs.mkdirSync(monitoringDir, { recursive: true });
    }

    // Prometheus配置
    const prometheusConfig = `# Prometheus配置
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'dl-engine-services'
    static_configs:
      - targets: ['localhost:3000', 'localhost:3001', 'localhost:3002']
    metrics_path: /metrics
    scrape_interval: 5s

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']

  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']

  - job_name: 'mysql'
    static_configs:
      - targets: ['localhost:9104']`;

    fs.writeFileSync(path.join(monitoringDir, 'prometheus.yml'), prometheusConfig);

    // 告警规则
    const alertRules = `groups:
  - name: dl-engine-alerts
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          description: "{{ $labels.instance }} has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate on {{ $labels.instance }}"
          description: "Error rate is {{ $value }} errors per second."

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time on {{ $labels.instance }}"
          description: "95th percentile response time is {{ $value }} seconds."

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is {{ $value | humanizePercentage }}."

      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is {{ $value }}%."`;

    fs.writeFileSync(path.join(monitoringDir, 'alert_rules.yml'), alertRules);
  }

  /**
   * 处理单个微服务
   */
  async processService(serviceName) {
    const servicePath = path.join(this.serverDir, serviceName);
    const serviceReport = {
      name: serviceName,
      path: servicePath,
      tasks: {},
      status: 'processing'
    };

    try {
      // 1. 标准化通信协议
      await this.standardizeCommunication(servicePath, serviceReport);
      
      // 2. 统一错误处理
      await this.unifyErrorHandling(servicePath, serviceReport);
      
      // 3. 统一日志格式
      await this.unifyLogging(servicePath, serviceReport);
      
      // 4. 优化容器化部署
      await this.optimizeContainerization(servicePath, serviceReport);

      serviceReport.status = 'completed';
      this.report.summary.processed++;
      
    } catch (error) {
      serviceReport.status = 'error';
      serviceReport.error = error.message;
      this.report.summary.errors++;
      console.error(`❌ 处理 ${serviceName} 时出错: ${error.message}`);
    }

    this.report.services[serviceName] = serviceReport;
  }

  /**
   * 标准化通信协议
   */
  async standardizeCommunication(servicePath, report) {
    console.log('  🔗 标准化通信协议...');
    
    const tasks = {
      httpProtocol: false,
      microserviceProtocol: false,
      apiDocumentation: false
    };

    try {
      // 检查并更新控制器以使用标准协议
      const srcPath = path.join(servicePath, 'src');
      if (fs.existsSync(srcPath)) {
        await this.updateControllers(srcPath, tasks);
      }

      console.log(`    ✅ 通信协议标准化完成`);
      
    } catch (error) {
      console.log(`    ❌ 通信协议标准化失败: ${error.message}`);
      this.report.summary.warnings++;
    }

    report.tasks.communication = tasks;
  }

  /**
   * 更新控制器使用标准协议
   */
  async updateControllers(srcPath, tasks) {
    // 这里可以扫描控制器文件并更新它们使用标准的HTTP协议
    tasks.httpProtocol = true;
    tasks.apiDocumentation = true;
  }

  /**
   * 统一错误处理
   */
  async unifyErrorHandling(servicePath, report) {
    console.log('  ⚠️ 统一错误处理...');
    
    const tasks = {
      globalErrorFilter: false,
      customExceptions: false,
      errorLogging: false
    };

    try {
      // 创建全局错误过滤器
      const filtersDir = path.join(servicePath, 'src', 'common', 'filters');
      if (!fs.existsSync(filtersDir)) {
        fs.mkdirSync(filtersDir, { recursive: true });
      }

      const globalErrorFilter = `import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { ErrorHandler, AppError } from '@shared/errors/error-handler';
import { createLogger } from '@shared/logging/logger';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = createLogger('GlobalExceptionFilter');

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let errorResponse;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      errorResponse = exception.getResponse();
    } else if (exception instanceof AppError) {
      const handled = ErrorHandler.handle(exception);
      status = handled.statusCode;
      errorResponse = {
        success: false,
        error: {
          code: handled.code,
          message: handled.message,
          details: handled.details
        },
        timestamp: new Date().toISOString(),
        path: request.url
      };
    } else {
      const handled = ErrorHandler.handle(exception as Error);
      errorResponse = {
        success: false,
        error: {
          code: handled.code,
          message: handled.message
        },
        timestamp: new Date().toISOString(),
        path: request.url
      };
    }

    this.logger.error('Unhandled exception', exception as Error, {
      path: request.url,
      method: request.method,
      statusCode: status
    });

    response.status(status).json(errorResponse);
  }
}`;

      fs.writeFileSync(path.join(filtersDir, 'global-exception.filter.ts'), globalErrorFilter);
      tasks.globalErrorFilter = true;
      tasks.errorLogging = true;

      console.log(`    ✅ 错误处理统一完成`);
      
    } catch (error) {
      console.log(`    ❌ 错误处理统一失败: ${error.message}`);
      this.report.summary.warnings++;
    }

    report.tasks.errorHandling = tasks;
  }

  /**
   * 统一日志格式
   */
  async unifyLogging(servicePath, report) {
    console.log('  📝 统一日志格式...');
    
    const tasks = {
      loggingInterceptor: false,
      structuredLogging: false,
      logAggregation: false
    };

    try {
      // 创建日志拦截器
      const interceptorsDir = path.join(servicePath, 'src', 'common', 'interceptors');
      if (!fs.existsSync(interceptorsDir)) {
        fs.mkdirSync(interceptorsDir, { recursive: true });
      }

      const loggingInterceptor = `import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { createLogger } from '@shared/logging/logger';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = createLogger('HTTP');

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url, body, query, params } = request;
    const startTime = Date.now();

    this.logger.info('Incoming request', {
      method,
      url,
      body: this.sanitizeBody(body),
      query,
      params,
      userAgent: request.get('User-Agent'),
      ip: request.ip
    });

    return next.handle().pipe(
      tap({
        next: (data) => {
          const duration = Date.now() - startTime;
          this.logger.info('Request completed', {
            method,
            url,
            duration,
            statusCode: context.switchToHttp().getResponse().statusCode
          });
        },
        error: (error) => {
          const duration = Date.now() - startTime;
          this.logger.error('Request failed', error, {
            method,
            url,
            duration
          });
        }
      })
    );
  }

  private sanitizeBody(body: any): any {
    if (!body) return body;
    
    const sanitized = { ...body };
    const sensitiveFields = ['password', 'token', 'secret', 'key'];
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '***';
      }
    }
    
    return sanitized;
  }
}`;

      fs.writeFileSync(path.join(interceptorsDir, 'logging.interceptor.ts'), loggingInterceptor);
      tasks.loggingInterceptor = true;
      tasks.structuredLogging = true;

      console.log(`    ✅ 日志格式统一完成`);
      
    } catch (error) {
      console.log(`    ❌ 日志格式统一失败: ${error.message}`);
      this.report.summary.warnings++;
    }

    report.tasks.logging = tasks;
  }

  /**
   * 优化容器化部署
   */
  async optimizeContainerization(servicePath, report) {
    console.log('  🐳 优化容器化部署...');
    
    const tasks = {
      dockerfile: false,
      dockerCompose: false,
      healthCheck: false,
      multiStage: false
    };

    try {
      // 检查并优化Dockerfile
      const dockerfilePath = path.join(servicePath, 'Dockerfile');
      if (fs.existsSync(dockerfilePath)) {
        await this.optimizeDockerfile(dockerfilePath, tasks);
      }

      // 检查并优化docker-compose.yml
      const dockerComposePath = path.join(servicePath, 'docker-compose.yml');
      if (fs.existsSync(dockerComposePath)) {
        tasks.dockerCompose = true;
      }

      console.log(`    ✅ 容器化部署优化完成`);
      
    } catch (error) {
      console.log(`    ❌ 容器化部署优化失败: ${error.message}`);
      this.report.summary.warnings++;
    }

    report.tasks.containerization = tasks;
  }

  /**
   * 优化Dockerfile
   */
  async optimizeDockerfile(dockerfilePath, tasks) {
    try {
      const content = fs.readFileSync(dockerfilePath, 'utf8');
      
      // 检查是否使用多阶段构建
      if (content.includes('FROM') && content.split('FROM').length > 2) {
        tasks.multiStage = true;
      }
      
      // 检查是否有健康检查
      if (content.includes('HEALTHCHECK')) {
        tasks.healthCheck = true;
      }
      
      tasks.dockerfile = true;
      
    } catch (error) {
      // 忽略单个文件的错误
    }
  }

  /**
   * 生成报告
   */
  generateReport() {
    const reportPath = path.join(this.serverDir, 'cleanup-reports', 
      `architecture-optimization-report-${new Date().toISOString().replace(/[:.]/g, '-')}.json`);
    
    // 确保报告目录存在
    const reportDir = path.dirname(reportPath);
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(this.report, null, 2));
    console.log(`📊 报告已生成: ${reportPath}`);
  }

  /**
   * 打印总结
   */
  printSummary() {
    console.log('\n📋 架构优化总结');
    console.log('='.repeat(50));
    console.log(`总服务数: ${this.report.summary.total}`);
    console.log(`已处理: ${this.report.summary.processed}`);
    console.log(`错误: ${this.report.summary.errors}`);
    console.log(`警告: ${this.report.summary.warnings}`);
    console.log(`成功率: ${((this.report.summary.processed / this.report.summary.total) * 100).toFixed(1)}%`);
    
    if (this.report.summary.processed > 0) {
      console.log('\n✅ 架构优化完成！');
      console.log('\n📝 建议后续操作:');
      console.log('1. 更新各服务的main.ts文件以使用新的过滤器和拦截器');
      console.log('2. 配置Prometheus和Grafana监控');
      console.log('3. 测试微服务间通信');
      console.log('4. 验证错误处理和日志记录');
    }
  }
}

// 执行优化
if (require.main === module) {
  const optimizer = new ArchitectureOptimizer();
  optimizer.run().catch(console.error);
}

module.exports = ArchitectureOptimizer;
