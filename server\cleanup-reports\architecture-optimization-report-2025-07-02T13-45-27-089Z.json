{"timestamp": "2025-07-02T13:45:26.263Z", "services": {"5g-network-service": {"name": "5g-network-service", "path": "F:\\newsystem\\server\\5g-network-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": false, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "ai-engine-service": {"name": "ai-engine-service", "path": "F:\\newsystem\\server\\ai-engine-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": false, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "ai-model-service": {"name": "ai-model-service", "path": "F:\\newsystem\\server\\ai-model-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "ai-service": {"name": "ai-service", "path": "F:\\newsystem\\server\\ai-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": false, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "analytics-service": {"name": "analytics-service", "path": "F:\\newsystem\\server\\analytics-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": false, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "api-gateway": {"name": "api-gateway", "path": "F:\\newsystem\\server\\api-gateway", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": false, "healthCheck": false, "multiStage": true}}, "status": "completed"}, "asset-service": {"name": "asset-service", "path": "F:\\newsystem\\server\\asset-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": false, "healthCheck": false, "multiStage": true}}, "status": "completed"}, "avatar-service": {"name": "avatar-service", "path": "F:\\newsystem\\server\\avatar-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": false, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "behavior-decision-service": {"name": "behavior-decision-service", "path": "F:\\newsystem\\server\\behavior-decision-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": false, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "blockchain-service": {"name": "blockchain-service", "path": "F:\\newsystem\\server\\blockchain-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": false, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "cloud-edge-orchestration-service": {"name": "cloud-edge-orchestration-service", "path": "F:\\newsystem\\server\\cloud-edge-orchestration-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": false, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "collaboration-service": {"name": "collaboration-service", "path": "F:\\newsystem\\server\\collaboration-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": false, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "coordination-service": {"name": "coordination-service", "path": "F:\\newsystem\\server\\coordination-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "deeplearning-service": {"name": "deeplearning-service", "path": "F:\\newsystem\\server\\deeplearning-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": false, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "ecosystem-service": {"name": "ecosystem-service", "path": "F:\\newsystem\\server\\ecosystem-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "edge-ai-service": {"name": "edge-ai-service", "path": "F:\\newsystem\\server\\edge-ai-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": false}}, "status": "completed"}, "edge-enhancement": {"name": "edge-enhancement", "path": "F:\\newsystem\\server\\edge-enhancement", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "edge-game-server": {"name": "edge-game-server", "path": "F:\\newsystem\\server\\edge-game-server", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "edge-registry": {"name": "edge-registry", "path": "F:\\newsystem\\server\\edge-registry", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "edge-router": {"name": "edge-router", "path": "F:\\newsystem\\server\\edge-router", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "emotion-service": {"name": "emotion-service", "path": "F:\\newsystem\\server\\emotion-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "enterprise-integration-service": {"name": "enterprise-integration-service", "path": "F:\\newsystem\\server\\enterprise-integration-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "game-server": {"name": "game-server", "path": "F:\\newsystem\\server\\game-server", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": false, "healthCheck": false, "multiStage": true}}, "status": "completed"}, "human-machine-collaboration-service": {"name": "human-machine-collaboration-service", "path": "F:\\newsystem\\server\\human-machine-collaboration-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": false, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "industrial-data-service": {"name": "industrial-data-service", "path": "F:\\newsystem\\server\\industrial-data-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "intelligent-scheduling-service": {"name": "intelligent-scheduling-service", "path": "F:\\newsystem\\server\\intelligent-scheduling-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": false, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "knowledge-base-service": {"name": "knowledge-base-service", "path": "F:\\newsystem\\server\\knowledge-base-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "knowledge-graph-service": {"name": "knowledge-graph-service", "path": "F:\\newsystem\\server\\knowledge-graph-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": false, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "learning-tracking-service": {"name": "learning-tracking-service", "path": "F:\\newsystem\\server\\learning-tracking-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "mes-service": {"name": "mes-service", "path": "F:\\newsystem\\server\\mes-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": false, "healthCheck": true, "multiStage": false}}, "status": "completed"}, "mobile-service": {"name": "mobile-service", "path": "F:\\newsystem\\server\\mobile-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": false, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "monitoring-service": {"name": "monitoring-service", "path": "F:\\newsystem\\server\\monitoring-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "nlp-scene-service": {"name": "nlp-scene-service", "path": "F:\\newsystem\\server\\nlp-scene-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": false, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "perception-service": {"name": "perception-service", "path": "F:\\newsystem\\server\\perception-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "performance-service": {"name": "performance-service", "path": "F:\\newsystem\\server\\performance-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "predictive-maintenance-service": {"name": "predictive-maintenance-service", "path": "F:\\newsystem\\server\\predictive-maintenance-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "project-service": {"name": "project-service", "path": "F:\\newsystem\\server\\project-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": false, "healthCheck": false, "multiStage": true}}, "status": "completed"}, "rag-dialogue-service": {"name": "rag-dialogue-service", "path": "F:\\newsystem\\server\\rag-dialogue-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": false, "dockerCompose": false, "healthCheck": false, "multiStage": false}}, "status": "completed"}, "recommendation-service": {"name": "recommendation-service", "path": "F:\\newsystem\\server\\recommendation-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "render-service": {"name": "render-service", "path": "F:\\newsystem\\server\\render-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": false, "multiStage": true}}, "status": "completed"}, "security-service": {"name": "security-service", "path": "F:\\newsystem\\server\\security-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "service-registry": {"name": "service-registry", "path": "F:\\newsystem\\server\\service-registry", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": false, "healthCheck": false, "multiStage": true}}, "status": "completed"}, "signaling-service": {"name": "signaling-service", "path": "F:\\newsystem\\server\\signaling-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "spatial-service": {"name": "spatial-service", "path": "F:\\newsystem\\server\\spatial-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "ui-service": {"name": "ui-service", "path": "F:\\newsystem\\server\\ui-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": false, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "user-service": {"name": "user-service", "path": "F:\\newsystem\\server\\user-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": false, "healthCheck": false, "multiStage": true}}, "status": "completed"}, "visual-script-service": {"name": "visual-script-service", "path": "F:\\newsystem\\server\\visual-script-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": false, "healthCheck": true, "multiStage": true}}, "status": "completed"}, "voice-service": {"name": "voice-service", "path": "F:\\newsystem\\server\\voice-service", "tasks": {"communication": {"httpProtocol": true, "microserviceProtocol": false, "apiDocumentation": true}, "errorHandling": {"globalErrorFilter": true, "customExceptions": false, "errorLogging": true}, "logging": {"loggingInterceptor": true, "structuredLogging": true, "logAggregation": false}, "containerization": {"dockerfile": true, "dockerCompose": true, "healthCheck": true, "multiStage": true}}, "status": "completed"}}, "summary": {"total": 48, "processed": 48, "errors": 0, "warnings": 0}}