# DL引擎故障排除手册

## 🚨 常见问题诊断

### 服务启动问题

#### 1. 端口占用错误
**症状**: `Error: listen EADDRINUSE :::3000`

**解决方案**:
```bash
# 查找占用端口的进程
netstat -tulpn | grep :3000
# 或者使用lsof
lsof -i :3000

# 终止进程
kill -9 <PID>

# 或者修改服务端口
export PORT=3001
npm start
```

#### 2. 数据库连接失败
**症状**: `Error: connect ECONNREFUSED 127.0.0.1:3306`

**解决方案**:
```bash
# 检查MySQL服务状态
systemctl status mysql
# 或者
service mysql status

# 启动MySQL服务
systemctl start mysql

# 检查数据库配置
mysql -u root -p -e "SHOW DATABASES;"

# 验证连接参数
ping <DB_HOST>
telnet <DB_HOST> 3306
```

#### 3. Redis连接失败
**症状**: `Error: connect ECONNREFUSED 127.0.0.1:6379`

**解决方案**:
```bash
# 检查Redis服务状态
systemctl status redis
redis-cli ping

# 启动Redis服务
systemctl start redis

# 检查Redis配置
redis-cli info server
```

### 依赖安装问题

#### 1. npm install失败
**症状**: `npm ERR! peer dep missing`

**解决方案**:
```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules和package-lock.json
rm -rf node_modules package-lock.json

# 重新安装
npm install

# 如果仍有问题，使用yarn
yarn install
```

#### 2. TypeScript编译错误
**症状**: `TS2307: Cannot find module`

**解决方案**:
```bash
# 检查tsconfig.json路径配置
cat tsconfig.json | grep paths

# 安装缺失的类型定义
npm install @types/node @types/express

# 重新构建
npm run build
```

## 🔍 性能问题诊断

### 内存泄漏检测

#### 1. 监控内存使用
```bash
# 使用top命令监控
top -p <PID>

# 使用htop
htop

# Node.js内存使用情况
node --inspect app.js
# 然后在Chrome中打开 chrome://inspect
```

#### 2. 内存泄漏分析
```javascript
// 在代码中添加内存监控
setInterval(() => {
  const memUsage = process.memoryUsage();
  console.log('Memory Usage:', {
    rss: Math.round(memUsage.rss / 1024 / 1024) + ' MB',
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + ' MB',
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + ' MB',
    external: Math.round(memUsage.external / 1024 / 1024) + ' MB'
  });
}, 10000);
```

### 数据库性能问题

#### 1. 慢查询分析
```sql
-- 启用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log%';

-- 分析慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;
```

#### 2. 索引优化
```sql
-- 查看表索引
SHOW INDEX FROM users;

-- 分析查询执行计划
EXPLAIN SELECT * FROM users WHERE email = '<EMAIL>';

-- 添加索引
CREATE INDEX idx_users_email ON users(email);
```

## 🛠️ 调试工具

### 日志分析

#### 1. 结构化日志查询
```bash
# 使用jq解析JSON日志
tail -f app.log | jq '.level == "error"'

# 过滤特定服务的日志
grep "user-service" app.log | jq '.'

# 统计错误数量
grep "ERROR" app.log | wc -l
```

#### 2. 日志聚合分析
```bash
# 使用ELK Stack
# Elasticsearch查询示例
curl -X GET "localhost:9200/logs/_search" -H 'Content-Type: application/json' -d'
{
  "query": {
    "bool": {
      "must": [
        {"match": {"level": "error"}},
        {"range": {"timestamp": {"gte": "now-1h"}}}
      ]
    }
  }
}'
```

### 网络问题诊断

#### 1. 服务间通信检测
```bash
# 检查服务可达性
curl -I http://localhost:3000/health

# 检查网络延迟
ping api-gateway

# 检查端口连通性
telnet api-gateway 3000

# 使用nmap扫描端口
nmap -p 3000-3010 localhost
```

#### 2. 负载均衡检测
```bash
# 检查负载均衡器状态
curl http://load-balancer/status

# 测试负载分发
for i in {1..10}; do
  curl -s http://api-gateway/health | grep instance
done
```

## 🔧 故障恢复

### 服务重启策略

#### 1. 优雅重启
```bash
# 发送SIGTERM信号
kill -TERM <PID>

# 等待进程结束
wait <PID>

# 重新启动
npm start
```

#### 2. 滚动更新
```bash
# Kubernetes滚动更新
kubectl rollout restart deployment/api-gateway

# Docker Compose滚动更新
docker-compose up -d --no-deps api-gateway
```

### 数据恢复

#### 1. 数据库恢复
```bash
# 从备份恢复
mysql -u root -p dl_engine < backup.sql

# 检查数据完整性
mysql -u root -p -e "CHECK TABLE users;"

# 修复损坏的表
mysql -u root -p -e "REPAIR TABLE users;"
```

#### 2. Redis数据恢复
```bash
# 从RDB文件恢复
cp backup.rdb /var/lib/redis/dump.rdb
systemctl restart redis

# 从AOF文件恢复
redis-check-aof --fix appendonly.aof
```

## 📊 监控告警

### Prometheus告警规则

```yaml
groups:
  - name: dl-engine-alerts
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
```

### 健康检查端点

```typescript
// health.controller.ts
@Controller('health')
export class HealthController {
  constructor(
    private readonly healthCheckService: HealthCheckService,
    private readonly typeOrmHealthIndicator: TypeOrmHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.healthCheckService.check([
      () => this.typeOrmHealthIndicator.pingCheck('database'),
      () => this.redisHealthIndicator.pingCheck('redis'),
    ]);
  }
}
```

## 🚨 紧急响应流程

### 1. 服务完全宕机
1. 立即检查基础设施状态
2. 查看最近的部署记录
3. 回滚到上一个稳定版本
4. 通知相关团队
5. 分析根本原因

### 2. 数据库故障
1. 切换到备用数据库
2. 检查数据一致性
3. 修复主数据库
4. 同步数据
5. 切回主数据库

### 3. 安全事件
1. 立即隔离受影响的服务
2. 收集日志和证据
3. 评估影响范围
4. 修复安全漏洞
5. 恢复服务并加强监控

## 📞 联系信息

### 紧急联系人
- 系统管理员: <EMAIL>
- 开发团队负责人: <EMAIL>
- 运维团队: <EMAIL>

### 支持渠道
- 内部工单系统: https://tickets.company.com
- 技术支持热线: +86-xxx-xxxx-xxxx
- 在线文档: https://docs.dl-engine.com

## 📚 参考资源

### 官方文档
- [NestJS文档](https://docs.nestjs.com/)
- [TypeORM文档](https://typeorm.io/)
- [Docker文档](https://docs.docker.com/)
- [Kubernetes文档](https://kubernetes.io/docs/)

### 监控工具
- [Prometheus](https://prometheus.io/docs/)
- [Grafana](https://grafana.com/docs/)
- [ELK Stack](https://www.elastic.co/guide/)

### 最佳实践
- [12-Factor App](https://12factor.net/)
- [微服务设计模式](https://microservices.io/)
- [Node.js最佳实践](https://github.com/goldbergyoni/nodebestpractices)
