import {
  Is<PERSON><PERSON>y,
  <PERSON><PERSON><PERSON>,
  IsOptional,
  IsObject,
  ValidateNested,
} from 'class-validator';
import type {  Type  } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CreateTaskDto } from './create-task.dto';
import { OptimizationAlgorithm, SchedulingObjective } from '../entities/scheduling-solution.entity';

/**
 * 调度请求DTO
 */
export class SchedulingRequestDto {
  @ApiProperty({ 
    description: '任务列表',
    type: [CreateTaskDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateTaskDto)
  tasks: CreateTaskDto[];

  @ApiPropertyOptional({ 
    description: '优化目标列表',
    enum: SchedulingObjective,
    isArray: true,
    example: [SchedulingObjective.MINIMIZE_MAKESPAN, SchedulingObjective.MINIMIZE_COST]
  })
  @IsOptional()
  @IsArray()
  @IsEnum(SchedulingObjective, { each: true })
  objectives?: SchedulingObjective[];

  @ApiPropertyOptional({ 
    description: '优化算法',
    enum: OptimizationAlgorithm,
    example: OptimizationAlgorithm.GENETIC_ALGORITHM
  })
  @IsOptional()
  @IsEnum(OptimizationAlgorithm)
  algorithm?: OptimizationAlgorithm;

  @ApiPropertyOptional({ description: '调度配置参数' })
  @IsOptional()
  @IsObject()
  config?: {
    planningHorizon?: number;
    optimizationTimeLimit?: number;
    populationSize?: number;
    maxIterations?: number;
    convergenceThreshold?: number;
  };

  @ApiPropertyOptional({ description: '资源约束' })
  @IsOptional()
  @IsObject()
  resourceConstraints?: any;

  @ApiPropertyOptional({ description: '扩展参数' })
  @IsOptional()
  @IsObject()
  metadata?: any;
}
