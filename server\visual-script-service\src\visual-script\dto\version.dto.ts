import { IsString, IsOptional, IsObject, <PERSON>N<PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import type {  Type  } from 'class-transformer';

export class CreateVersionDto {
  @ApiPropertyOptional({ description: '版本名称' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '版本描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '版本内容', type: 'object' })
  @IsOptional()
  @IsObject()
  content?: any;

  @ApiPropertyOptional({ description: '版本元数据', type: 'object' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class QueryVersionDto {
  @ApiPropertyOptional({ description: '页码', minimum: 1, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100, default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({ description: '排序字段', default: 'versionNumber' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'versionNumber';

  @ApiPropertyOptional({ description: '排序方向', enum: ['ASC', 'DESC'], default: 'DESC' })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

export class CompareVersionDto {
  @ApiProperty({ description: '源版本ID' })
  @IsString()
  sourceVersionId: string;

  @ApiProperty({ description: '目标版本ID' })
  @IsString()
  targetVersionId: string;
}

export class VersionDiffDto {
  @ApiProperty({ description: '是否有变更' })
  hasChanges: boolean;

  @ApiProperty({ description: '变更列表', type: [Object] })
  changes: Array<{
    type: 'added' | 'removed' | 'modified';
    path: string;
    oldValue?: any;
    newValue?: any;
  }>;
}

export class VersionCompareResultDto {
  @ApiProperty({ description: '源版本信息' })
  sourceVersion: {
    id: string;
    name: string;
    versionNumber: number;
  };

  @ApiProperty({ description: '目标版本信息' })
  targetVersion: {
    id: string;
    name: string;
    versionNumber: number;
  };

  @ApiProperty({ description: '差异信息', type: VersionDiffDto })
  diff: VersionDiffDto;
}
