/**
 * 场景切换节点集合
 * 批次1.2：场景切换节点 (8个)
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Scene, Color } from 'three';

/**
 * 场景切换类型枚举
 */
export enum SceneTransitionType {
  INSTANT = 'instant',
  FADE = 'fade',
  SLIDE = 'slide',
  DISSOLVE = 'dissolve',
  WIPE = 'wipe',
  ZOOM = 'zoom',
  FLIP = 'flip',
  CUSTOM = 'custom'
}

/**
 * 场景切换状态枚举
 */
export enum TransitionState {
  IDLE = 'idle',
  PREPARING = 'preparing',
  TRANSITIONING = 'transitioning',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

/**
 * 场景切换管理器
 */
class SceneTransitionManager {
  private currentTransition: any = null;
  private transitionState: TransitionState = TransitionState.IDLE;
  private eventListeners: Map<string, Function[]> = new Map();
  private transitionHistory: string[] = [];
  private maxHistorySize: number = 10;

  /**
   * 执行场景切换
   */
  async executeTransition(
    fromSceneId: string,
    toSceneId: string,
    transitionType: SceneTransitionType,
    config: any = {}
  ): Promise<boolean> {
    try {
      if (this.transitionState !== TransitionState.IDLE) {
        throw new Error('已有场景切换正在进行中');
      }

      this.transitionState = TransitionState.PREPARING;
      this.emit('transitionStart', { fromSceneId, toSceneId, transitionType });

      // 创建切换配置
      const transitionConfig = {
        fromSceneId,
        toSceneId,
        type: transitionType,
        duration: config.duration || 1000,
        easing: config.easing || 'easeInOut',
        direction: config.direction || 'forward',
        color: config.color || new Color(0x000000),
        ...config
      };

      this.currentTransition = transitionConfig;
      this.transitionState = TransitionState.TRANSITIONING;

      // 执行具体的切换逻辑
      const success = await this.performTransition(transitionConfig);

      if (success) {
        this.transitionState = TransitionState.COMPLETED;
        this.addToHistory(toSceneId);
        this.emit('transitionComplete', { fromSceneId, toSceneId, transitionType });
      } else {
        this.transitionState = TransitionState.FAILED;
        this.emit('transitionFailed', { fromSceneId, toSceneId, transitionType });
      }

      this.currentTransition = null;
      this.transitionState = TransitionState.IDLE;

      Debug.log('SceneTransitionManager', `场景切换${success ? '成功' : '失败'}: ${fromSceneId} -> ${toSceneId}`);
      return success;

    } catch (error) {
      this.transitionState = TransitionState.FAILED;
      this.currentTransition = null;
      this.emit('transitionError', { fromSceneId, toSceneId, error });
      Debug.error('SceneTransitionManager', '场景切换失败', error);
      return false;
    }
  }

  /**
   * 执行具体的切换效果
   */
  private async performTransition(config: any): Promise<boolean> {
    return new Promise((resolve) => {
      const startTime = performance.now();
      const duration = config.duration;

      const animate = () => {
        const elapsed = performance.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 应用缓动函数
        const easedProgress = this.applyEasing(progress, config.easing);

        // 根据切换类型执行不同的效果
        this.applyTransitionEffect(config, easedProgress);

        if (progress >= 1) {
          resolve(true);
        } else {
          requestAnimationFrame(animate);
        }
      };

      requestAnimationFrame(animate);
    });
  }

  /**
   * 应用切换效果
   */
  private applyTransitionEffect(config: any, progress: number): void {
    switch (config.type) {
      case SceneTransitionType.FADE:
        this.applyFadeEffect(config, progress);
        break;
      case SceneTransitionType.SLIDE:
        this.applySlideEffect(config, progress);
        break;
      case SceneTransitionType.DISSOLVE:
        this.applyDissolveEffect(config, progress);
        break;
      case SceneTransitionType.WIPE:
        this.applyWipeEffect(config, progress);
        break;
      case SceneTransitionType.ZOOM:
        this.applyZoomEffect(config, progress);
        break;
      case SceneTransitionType.FLIP:
        this.applyFlipEffect(config, progress);
        break;
      case SceneTransitionType.INSTANT:
        // 瞬间切换，无需动画
        break;
      default:
        Debug.warn('SceneTransitionManager', `未知的切换类型: ${config.type}`);
    }

    this.emit('transitionProgress', { config, progress });
  }

  private applyFadeEffect(config: any, progress: number): void {
    // 淡入淡出效果实现
    const opacity = progress < 0.5 ? 1 - progress * 2 : (progress - 0.5) * 2;
    this.emit('fadeEffect', { opacity, progress });
  }

  private applySlideEffect(config: any, progress: number): void {
    // 滑动效果实现
    const offset = config.direction === 'left' ? -progress : progress;
    this.emit('slideEffect', { offset, progress });
  }

  private applyDissolveEffect(config: any, progress: number): void {
    // 溶解效果实现
    this.emit('dissolveEffect', { dissolveAmount: progress });
  }

  private applyWipeEffect(config: any, progress: number): void {
    // 擦除效果实现
    this.emit('wipeEffect', { wipeProgress: progress, direction: config.direction });
  }

  private applyZoomEffect(config: any, progress: number): void {
    // 缩放效果实现
    const scale = 1 + progress * 0.5;
    this.emit('zoomEffect', { scale, progress });
  }

  private applyFlipEffect(config: any, progress: number): void {
    // 翻转效果实现
    const rotation = progress * Math.PI;
    this.emit('flipEffect', { rotation, progress });
  }

  /**
   * 应用缓动函数
   */
  private applyEasing(t: number, easing: string): number {
    switch (easing) {
      case 'linear':
        return t;
      case 'easeIn':
        return t * t;
      case 'easeOut':
        return 1 - (1 - t) * (1 - t);
      case 'easeInOut':
        return t < 0.5 ? 2 * t * t : 1 - 2 * (1 - t) * (1 - t);
      case 'bounce':
        if (t < 1 / 2.75) {
          return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
          return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        } else if (t < 2.5 / 2.75) {
          return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        } else {
          return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
      default:
        return t;
    }
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(sceneId: string): void {
    this.transitionHistory.push(sceneId);
    if (this.transitionHistory.length > this.maxHistorySize) {
      this.transitionHistory.shift();
    }
  }

  /**
   * 获取切换历史
   */
  getTransitionHistory(): string[] {
    return [...this.transitionHistory];
  }

  /**
   * 获取当前切换状态
   */
  getTransitionState(): TransitionState {
    return this.transitionState;
  }

  /**
   * 获取当前切换信息
   */
  getCurrentTransition(): any {
    return this.currentTransition;
  }

  /**
   * 取消当前切换
   */
  cancelTransition(): boolean {
    if (this.transitionState === TransitionState.TRANSITIONING) {
      this.transitionState = TransitionState.IDLE;
      this.currentTransition = null;
      this.emit('transitionCancelled');
      Debug.log('SceneTransitionManager', '场景切换已取消');
      return true;
    }
    return false;
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('SceneTransitionManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }
}

// 全局场景切换管理器实例
const globalTransitionManager = new SceneTransitionManager();

/**
 * 场景切换节点
 */
export class SceneTransitionNode extends VisualScriptNode {
  public static readonly TYPE = 'SceneTransition';
  public static readonly NAME = '场景切换';
  public static readonly DESCRIPTION = '执行场景之间的切换';

  constructor(nodeType: string = SceneTransitionNode.TYPE, name: string = SceneTransitionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('transition', 'trigger', '切换');
    this.addInput('fromSceneId', 'string', '源场景ID');
    this.addInput('toSceneId', 'string', '目标场景ID');
    this.addInput('transitionType', 'string', '切换类型');
    this.addInput('duration', 'number', '持续时间(毫秒)');
    this.addInput('easing', 'string', '缓动函数');
    this.addInput('direction', 'string', '切换方向');
    this.addInput('color', 'object', '切换颜色');

    // 输出端口
    this.addOutput('fromSceneId', 'string', '源场景ID');
    this.addOutput('toSceneId', 'string', '目标场景ID');
    this.addOutput('transitionType', 'string', '切换类型');
    this.addOutput('progress', 'number', '切换进度');
    this.addOutput('onStarted', 'trigger', '切换开始');
    this.addOutput('onProgress', 'trigger', '切换进行中');
    this.addOutput('onCompleted', 'trigger', '切换完成');
    this.addOutput('onFailed', 'trigger', '切换失败');
    this.addOutput('onError', 'trigger', '切换错误');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const transitionTrigger = inputs?.transition;
      if (!transitionTrigger) {
        return this.getDefaultOutputs();
      }

      const fromSceneId = inputs?.fromSceneId as string;
      const toSceneId = inputs?.toSceneId as string;
      
      if (!fromSceneId || !toSceneId) {
        throw new Error('未提供源场景ID或目标场景ID');
      }

      const transitionType = inputs?.transitionType as string || 'fade';
      const duration = inputs?.duration as number || 1000;
      const easing = inputs?.easing as string || 'easeInOut';
      const direction = inputs?.direction as string || 'forward';
      const color = inputs?.color as Color || new Color(0x000000);

      const config = {
        duration,
        easing,
        direction,
        color
      };

      // 监听切换进度
      const progressHandler = (data: any) => {
        this.emit('onProgress', { progress: data.progress });
      };

      globalTransitionManager.on('transitionProgress', progressHandler);

      const success = await globalTransitionManager.executeTransition(
        fromSceneId,
        toSceneId,
        transitionType as SceneTransitionType,
        config
      );

      globalTransitionManager.off('transitionProgress', progressHandler);

      Debug.log('SceneTransitionNode', `场景切换${success ? '成功' : '失败'}: ${fromSceneId} -> ${toSceneId}`);

      return {
        fromSceneId,
        toSceneId,
        transitionType,
        progress: 1,
        onStarted: true,
        onProgress: false,
        onCompleted: success,
        onFailed: !success,
        onError: false
      };

    } catch (error) {
      Debug.error('SceneTransitionNode', '场景切换失败', error);
      return {
        fromSceneId: '',
        toSceneId: '',
        transitionType: '',
        progress: 0,
        onStarted: false,
        onProgress: false,
        onCompleted: false,
        onFailed: false,
        onError: true
      };
    }
  }

  private emit(event: string, data?: any): void {
    // 简化的事件发射，实际应该通过节点系统处理
    Debug.log('SceneTransitionNode', `事件: ${event}`, data);
  }

  private getDefaultOutputs(): any {
    return {
      fromSceneId: '',
      toSceneId: '',
      transitionType: '',
      progress: 0,
      onStarted: false,
      onProgress: false,
      onCompleted: false,
      onFailed: false,
      onError: false
    };
  }
}
