/**
 * script.dto.ts
 * 
 * 脚本相关的数据传输对象
 */

import { IsString, IsOptional, IsObject, IsArray, IsEnum, IsNumber, IsBoolean, Min, Max } from 'class-validator';
import type {  Type, Transform  } from 'class-transformer';
import { ScriptStatus, ScriptVisibility } from '../entities/visual-script.entity';

/**
 * 创建脚本DTO
 */
export class CreateScriptDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  projectId?: string;

  @IsOptional()
  @IsEnum(ScriptVisibility)
  visibility?: ScriptVisibility;

  @IsOptional()
  @IsObject()
  graph?: any;

  @IsOptional()
  @IsObject()
  metadata?: any;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  templateCategory?: string;

  @IsOptional()
  @IsBoolean()
  isTemplate?: boolean;
}

/**
 * 更新脚本DTO
 */
export class UpdateScriptDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  projectId?: string;

  @IsOptional()
  @IsEnum(ScriptStatus)
  status?: ScriptStatus;

  @IsOptional()
  @IsEnum(ScriptVisibility)
  visibility?: ScriptVisibility;

  @IsOptional()
  @IsObject()
  graph?: any;

  @IsOptional()
  @IsObject()
  metadata?: any;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  templateCategory?: string;

  @IsOptional()
  @IsBoolean()
  isTemplate?: boolean;
}

/**
 * 脚本查询DTO
 */
export class ScriptQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(ScriptStatus)
  status?: ScriptStatus;

  @IsOptional()
  @IsEnum(ScriptVisibility)
  visibility?: ScriptVisibility;

  @IsOptional()
  @IsString()
  ownerId?: string;

  @IsOptional()
  @IsString()
  projectId?: string;

  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(tag => tag.trim());
    }
    return value;
  })
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  sortBy?: string = 'updatedAt';

  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value === 'true';
    }
    return value;
  })
  includeTemplates?: boolean;

  @IsOptional()
  @IsString()
  templateCategory?: string;
}

/**
 * 脚本执行DTO
 */
export class ExecuteScriptDto {
  @IsOptional()
  @IsString()
  version?: string;

  @IsOptional()
  @IsObject()
  context?: any;

  @IsOptional()
  @IsObject()
  parameters?: any;

  @IsOptional()
  @IsEnum(['client', 'server', 'edge', 'cloud'])
  environment?: string = 'client';

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(10)
  priority?: number = 0;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1000)
  @Max(300000)
  timeoutMs?: number = 30000;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(5)
  maxRetries?: number = 3;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * 脚本分享DTO
 */
export class ShareScriptDto {
  @IsArray()
  @IsString({ each: true })
  userIds: string[];

  @IsOptional()
  @IsEnum(['editor', 'viewer', 'commenter'])
  role?: string = 'viewer';

  @IsOptional()
  @IsString()
  message?: string;

  @IsOptional()
  @IsBoolean()
  sendNotification?: boolean = true;
}

/**
 * 脚本导出DTO
 */
export class ExportScriptDto {
  @IsOptional()
  @IsString()
  version?: string;

  @IsOptional()
  @IsEnum(['json', 'yaml', 'xml'])
  format?: string = 'json';

  @IsOptional()
  @IsBoolean()
  includeMetadata?: boolean = true;

  @IsOptional()
  @IsBoolean()
  includeVersionHistory?: boolean = false;

  @IsOptional()
  @IsBoolean()
  includeComments?: boolean = false;

  @IsOptional()
  @IsBoolean()
  minify?: boolean = false;
}

/**
 * 脚本导入DTO
 */
export class ImportScriptDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  projectId?: string;

  @IsObject()
  data: any;

  @IsOptional()
  @IsEnum(['json', 'yaml', 'xml'])
  format?: string = 'json';

  @IsOptional()
  @IsBoolean()
  overwriteExisting?: boolean = false;

  @IsOptional()
  @IsBoolean()
  preserveIds?: boolean = false;
}

/**
 * 脚本统计DTO
 */
export class ScriptStatsDto {
  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  endDate?: string;

  @IsOptional()
  @IsEnum(['day', 'week', 'month', 'year'])
  groupBy?: string = 'day';

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  metrics?: string[] = ['executions', 'views', 'edits'];

  @IsOptional()
  @IsString()
  ownerId?: string;

  @IsOptional()
  @IsString()
  projectId?: string;
}

/**
 * 脚本搜索DTO
 */
export class SearchScriptDto {
  @IsString()
  query: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  filters?: string[];

  @IsOptional()
  @IsEnum(['relevance', 'date', 'popularity', 'name'])
  sortBy?: string = 'relevance';

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value === 'true';
    }
    return value;
  })
  includeContent?: boolean = false;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value === 'true';
    }
    return value;
  })
  fuzzySearch?: boolean = true;
}
