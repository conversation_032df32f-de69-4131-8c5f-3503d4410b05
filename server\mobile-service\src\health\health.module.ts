/**
 * 健康检查模块
 * 
 * 提供服务健康状态监控功能
 */

import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import type {  TypeOrmModule  } from '@nestjs/typeorm';

import { HealthController } from './health.controller';
import { HealthService } from './health.service';

@Module({
  imports: [
    TerminusModule,
    TypeOrmModule.forFeature([]),
  ],
  controllers: [HealthController],
  providers: [HealthService],
  exports: [HealthService],
})
export class HealthModule {}
