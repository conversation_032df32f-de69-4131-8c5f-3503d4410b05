# DL引擎部署运维指南

## 🚀 快速部署

### 环境要求

#### 硬件要求
- CPU: 8核心以上
- 内存: 32GB以上
- 存储: 500GB SSD
- 网络: 千兆网卡

#### 软件要求
- Node.js 18+
- Docker 20.10+
- Docker Compose 2.0+
- Redis 6.0+
- MySQL 8.0+

### 一键部署脚本

```bash
# 克隆项目
git clone <repository-url>
cd newsystem/server

# 安装依赖
npm run install:all

# 启动基础设施
docker-compose up -d redis mysql

# 启动所有服务
npm run start:all
```

## 🐳 Docker部署

### 构建镜像

```bash
# 构建所有服务镜像
npm run docker:build

# 构建单个服务镜像
cd api-gateway
docker build -t dl-engine/api-gateway .
```

### Docker Compose部署

```yaml
version: '3.8'
services:
  api-gateway:
    image: dl-engine/api-gateway
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
      - DB_HOST=mysql
    depends_on:
      - redis
      - mysql

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

  mysql:
    image: mysql:8
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: dl_engine
    ports:
      - "3306:3306"
```

## ☸️ Kubernetes部署

### 命名空间创建

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine
```

### 服务部署示例

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: dl-engine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: dl-engine/api-gateway:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

## 🔧 配置管理

### 环境变量配置

每个服务的环境变量配置：

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=dl_engine

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 服务配置
PORT=3000
NODE_ENV=production
LOG_LEVEL=info

# JWT配置
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=24h
```

### 配置文件管理

```typescript
// config/database.config.ts
export default {
  type: 'mysql',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  username: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_DATABASE || 'dl_engine',
  synchronize: process.env.NODE_ENV !== 'production',
  logging: process.env.NODE_ENV === 'development'
};
```

## 📊 监控部署

### Prometheus配置

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'dl-engine-services'
    static_configs:
      - targets:
        - 'api-gateway:3000'
        - 'user-service:3002'
        - 'ai-service:3003'
```

### Grafana仪表板

导入预配置的Grafana仪表板：
- 服务健康状态
- API响应时间
- 错误率统计
- 资源使用情况

## 🔄 CI/CD流水线

### GitHub Actions配置

```yaml
name: Deploy DL Engine
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2

    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm test

    - name: Build Docker images
      run: npm run docker:build

    - name: Deploy to production
      run: npm run deploy:prod
```

## 🛠️ 运维操作

### 服务管理

```bash
# 启动所有服务
npm run start:all

# 停止所有服务
npm run stop:all

# 重启服务
npm run restart:service api-gateway

# 查看服务状态
npm run status
```

### 日志管理

```bash
# 查看服务日志
docker logs dl-engine-api-gateway

# 实时日志
docker logs -f dl-engine-api-gateway

# 日志聚合查询
kubectl logs -l app=api-gateway -n dl-engine
```

### 数据备份

```bash
# 数据库备份
mysqldump -h localhost -u root -p dl_engine > backup.sql

# Redis备份
redis-cli --rdb dump.rdb

# 自动备份脚本
./scripts/backup.sh
```

## 🔒 安全配置

### SSL/TLS配置

```nginx
server {
    listen 443 ssl;
    server_name api.dl-engine.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        proxy_pass http://api-gateway:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 防火墙配置

```bash
# 开放必要端口
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3000:3100/tcp

# 限制数据库访问
ufw allow from 10.0.0.0/8 to any port 3306
```
