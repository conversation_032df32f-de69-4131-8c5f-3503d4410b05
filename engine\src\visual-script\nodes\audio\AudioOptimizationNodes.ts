/**
 * 音频优化节点集合
 * 批次1.6：音频优化节点 - 5个节点
 * 提供音频性能优化、流式传输、压缩、内存管理等功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 音频优化配置接口
 */
export interface AudioOptimizationConfig {
  enableCompression: boolean;
  compressionQuality: number;
  enableStreaming: boolean;
  bufferSize: number;
  enableSpatialOptimization: boolean;
  maxConcurrentSources: number;
  enableMemoryPooling: boolean;
  poolSize: number;
}

/**
 * 音频流配置接口
 */
export interface AudioStreamConfig {
  url: string;
  format: string;
  bitrate: number;
  sampleRate: number;
  channels: number;
  bufferDuration: number;
  preloadDuration: number;
}

/**
 * 音频性能数据接口
 */
export interface AudioPerformanceData {
  activeSources: number;
  memoryUsage: number;
  cpuUsage: number;
  latency: number;
  bufferUnderruns: number;
  compressionRatio: number;
  streamingBandwidth: number;
}

/**
 * 音频优化管理器
 */
class AudioOptimizationManager {
  private static instance: AudioOptimizationManager;
  private optimizationConfig: AudioOptimizationConfig;
  private performanceData: AudioPerformanceData;
  private audioStreams: Map<string, any> = new Map();
  private memoryPool: any[] = [];

  private constructor() {
    this.optimizationConfig = {
      enableCompression: true,
      compressionQuality: 0.8,
      enableStreaming: true,
      bufferSize: 4096,
      enableSpatialOptimization: true,
      maxConcurrentSources: 32,
      enableMemoryPooling: true,
      poolSize: 100
    };

    this.performanceData = {
      activeSources: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      latency: 0,
      bufferUnderruns: 0,
      compressionRatio: 1.0,
      streamingBandwidth: 0
    };
  }

  public static getInstance(): AudioOptimizationManager {
    if (!AudioOptimizationManager.instance) {
      AudioOptimizationManager.instance = new AudioOptimizationManager();
    }
    return AudioOptimizationManager.instance;
  }

  setOptimizationConfig(config: Partial<AudioOptimizationConfig>): void {
    Object.assign(this.optimizationConfig, config);
    Debug.log('AudioOptimizationManager', '音频优化配置更新');
  }

  getOptimizationConfig(): AudioOptimizationConfig {
    return { ...this.optimizationConfig };
  }

  updatePerformanceData(data: Partial<AudioPerformanceData>): void {
    Object.assign(this.performanceData, data);
  }

  getPerformanceData(): AudioPerformanceData {
    return { ...this.performanceData };
  }

  createAudioStream(id: string, config: AudioStreamConfig): any {
    const stream = {
      id,
      config,
      buffer: null,
      loaded: false,
      playing: false,
      position: 0
    };

    this.audioStreams.set(id, stream);
    Debug.log('AudioOptimizationManager', `音频流创建: ${id}`);
    return stream;
  }

  getAudioStream(id: string): any {
    return this.audioStreams.get(id);
  }
}

/**
 * 音频优化节点
 */
export class AudioOptimizationNode extends VisualScriptNode {
  public static readonly TYPE = 'AudioOptimization';
  public static readonly NAME = '音频优化';
  public static readonly DESCRIPTION = '配置和控制音频系统优化';

  private optimizationManager: AudioOptimizationManager;

  constructor(nodeType: string = AudioOptimizationNode.TYPE, name: string = AudioOptimizationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.optimizationManager = AudioOptimizationManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('configure', 'trigger', '配置优化');
    this.addInput('enableCompression', 'boolean', '启用压缩');
    this.addInput('compressionQuality', 'number', '压缩质量');
    this.addInput('enableStreaming', 'boolean', '启用流式传输');
    this.addInput('bufferSize', 'number', '缓冲区大小');
    this.addInput('maxConcurrentSources', 'number', '最大并发源');

    // 输出端口
    this.addOutput('config', 'object', '优化配置');
    this.addOutput('memoryReduction', 'number', '内存减少');
    this.addOutput('latencyReduction', 'number', '延迟减少');
    this.addOutput('onConfigured', 'trigger', '配置完成');
    this.addOutput('onOptimized', 'trigger', '优化完成');
    this.addOutput('onError', 'trigger', '错误');
  }

  public execute(inputs?: any): any {
    try {
      const configureTrigger = inputs?.configure;

      if (configureTrigger) {
        return this.configureOptimization(inputs);
      }

      return this.getDefaultOutputs();
    } catch (error) {
      Debug.error('AudioOptimizationNode', '音频优化操作失败', error);
      return { ...this.getDefaultOutputs(), onError: true };
    }
  }

  private configureOptimization(inputs: any): any {
    const enableCompression = inputs?.enableCompression as boolean ?? true;
    const compressionQuality = inputs?.compressionQuality as number || 0.8;
    const enableStreaming = inputs?.enableStreaming as boolean ?? true;
    const bufferSize = inputs?.bufferSize as number || 4096;
    const maxConcurrentSources = inputs?.maxConcurrentSources as number || 32;

    const config: Partial<AudioOptimizationConfig> = {
      enableCompression,
      compressionQuality,
      enableStreaming,
      bufferSize,
      maxConcurrentSources
    };

    this.optimizationManager.setOptimizationConfig(config);

    const memoryReduction = this.calculateMemoryReduction(config);
    const latencyReduction = this.calculateLatencyReduction(config);

    Debug.log('AudioOptimizationNode', '音频优化配置完成');

    return {
      config: this.optimizationManager.getOptimizationConfig(),
      memoryReduction,
      latencyReduction,
      onConfigured: true,
      onOptimized: true,
      onError: false
    };
  }

  private calculateMemoryReduction(config: Partial<AudioOptimizationConfig>): number {
    let reduction = 0;
    if (config.enableCompression) reduction += 30;
    if (config.enableStreaming) reduction += 20;
    if (config.enableMemoryPooling) reduction += 15;
    return Math.min(reduction, 70);
  }

  private calculateLatencyReduction(config: Partial<AudioOptimizationConfig>): number {
    let reduction = 0;
    if (config.bufferSize && config.bufferSize < 2048) reduction += 20;
    if (config.enableSpatialOptimization) reduction += 10;
    return Math.min(reduction, 40);
  }

  private getDefaultOutputs(): any {
    return {
      config: null,
      memoryReduction: 0,
      latencyReduction: 0,
      onConfigured: false,
      onOptimized: false,
      onError: false
    };
  }
}

/**
 * 音频流式传输节点
 */
export class AudioStreamingNode extends VisualScriptNode {
  public static readonly TYPE = 'AudioStreaming';
  public static readonly NAME = '音频流式传输';
  public static readonly DESCRIPTION = '管理音频流式加载和播放';

  private optimizationManager: AudioOptimizationManager;

  constructor(nodeType: string = AudioStreamingNode.TYPE, name: string = AudioStreamingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.optimizationManager = AudioOptimizationManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('createStream', 'trigger', '创建流');
    this.addInput('play', 'trigger', '播放');
    this.addInput('pause', 'trigger', '暂停');
    this.addInput('stop', 'trigger', '停止');
    this.addInput('streamId', 'string', '流ID');
    this.addInput('url', 'string', '音频URL');
    this.addInput('bufferDuration', 'number', '缓冲时长');
    this.addInput('preloadDuration', 'number', '预加载时长');

    // 输出端口
    this.addOutput('stream', 'object', '音频流对象');
    this.addOutput('streamId', 'string', '流ID');
    this.addOutput('bufferLevel', 'number', '缓冲级别');
    this.addOutput('downloadProgress', 'number', '下载进度');
    this.addOutput('bandwidth', 'number', '带宽使用');
    this.addOutput('onStreamCreated', 'trigger', '流创建完成');
    this.addOutput('onPlaying', 'trigger', '开始播放');
    this.addOutput('onPaused', 'trigger', '暂停播放');
    this.addOutput('onStopped', 'trigger', '停止播放');
    this.addOutput('onBuffering', 'trigger', '缓冲中');
    this.addOutput('onError', 'trigger', '错误');
  }

  public execute(inputs?: any): any {
    try {
      const createStreamTrigger = inputs?.createStream;
      const playTrigger = inputs?.play;
      const pauseTrigger = inputs?.pause;
      const stopTrigger = inputs?.stop;

      if (createStreamTrigger) {
        return this.createStream(inputs);
      } else if (playTrigger) {
        return this.playStream(inputs);
      } else if (pauseTrigger) {
        return this.pauseStream(inputs);
      } else if (stopTrigger) {
        return this.stopStream(inputs);
      }

      return this.getDefaultOutputs();
    } catch (error) {
      Debug.error('AudioStreamingNode', '音频流操作失败', error);
      return { ...this.getDefaultOutputs(), onError: true };
    }
  }

  private createStream(inputs: any): any {
    const streamId = inputs?.streamId as string || this.generateId();
    const url = inputs?.url as string;
    const bufferDuration = inputs?.bufferDuration as number || 5.0;
    const preloadDuration = inputs?.preloadDuration as number || 2.0;

    if (!url) {
      throw new Error('未提供音频URL');
    }

    const config: AudioStreamConfig = {
      url,
      format: 'mp3',
      bitrate: 128,
      sampleRate: 44100,
      channels: 2,
      bufferDuration,
      preloadDuration
    };

    const stream = this.optimizationManager.createAudioStream(streamId, config);

    Debug.log('AudioStreamingNode', `音频流创建: ${streamId}`);

    return {
      stream,
      streamId,
      bufferLevel: 0,
      downloadProgress: 0,
      bandwidth: 0,
      onStreamCreated: true,
      onPlaying: false,
      onPaused: false,
      onStopped: false,
      onBuffering: false,
      onError: false
    };
  }

  private playStream(inputs: any): any {
    const streamId = inputs?.streamId as string;

    if (!streamId) {
      throw new Error('未提供流ID');
    }

    const stream = this.optimizationManager.getAudioStream(streamId);
    if (!stream) {
      throw new Error('音频流不存在');
    }

    Debug.log('AudioStreamingNode', `音频流播放: ${streamId}`);

    return {
      stream,
      streamId,
      bufferLevel: 80,
      downloadProgress: 100,
      bandwidth: 128, // kbps
      onStreamCreated: false,
      onPlaying: true,
      onPaused: false,
      onStopped: false,
      onBuffering: false,
      onError: false
    };
  }

  private pauseStream(inputs: any): any {
    const streamId = inputs?.streamId as string;

    if (!streamId) {
      throw new Error('未提供流ID');
    }

    Debug.log('AudioStreamingNode', `音频流暂停: ${streamId}`);

    return {
      stream: null,
      streamId,
      bufferLevel: 80,
      downloadProgress: 100,
      bandwidth: 0,
      onStreamCreated: false,
      onPlaying: false,
      onPaused: true,
      onStopped: false,
      onBuffering: false,
      onError: false
    };
  }

  private stopStream(inputs: any): any {
    const streamId = inputs?.streamId as string;

    if (!streamId) {
      throw new Error('未提供流ID');
    }

    Debug.log('AudioStreamingNode', `音频流停止: ${streamId}`);

    return {
      stream: null,
      streamId,
      bufferLevel: 0,
      downloadProgress: 0,
      bandwidth: 0,
      onStreamCreated: false,
      onPlaying: false,
      onPaused: false,
      onStopped: true,
      onBuffering: false,
      onError: false
    };
  }

  private generateId(): string {
    return 'stream_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      stream: null,
      streamId: '',
      bufferLevel: 0,
      downloadProgress: 0,
      bandwidth: 0,
      onStreamCreated: false,
      onPlaying: false,
      onPaused: false,
      onStopped: false,
      onBuffering: false,
      onError: false
    };
  }
}
