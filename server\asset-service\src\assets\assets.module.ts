/**
 * 资产模块
 */
import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AssetsController } from './assets.controller';
import { CDNController } from './cdn.controller';
import { BatchController } from './batch.controller';
import { AssetsService } from './assets.service';
import { CDNService } from './cdn.service';
import { CacheService } from './cache.service';
import { BatchProcessingService } from './batch-processing.service';
import { Asset } from './entities/asset.entity';
import { AssetVersion } from './entities/asset-version.entity';
import { AssetTag } from './entities/asset-tag.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Asset, AssetVersion, AssetTag]),
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
    ClientsModule.registerAsync([
      {
        name: 'USER_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('USER_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('USER_SERVICE_PORT', 3001),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: 'PROJECT_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('PROJECT_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('PROJECT_SERVICE_PORT', 3002),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [
    AssetsController,
    CDNController,
    BatchController
  ],
  providers: [
    AssetsService,
    CDNService,
    CacheService,
    BatchProcessingService
  ],
  exports: [
    AssetsService,
    CDNService,
    CacheService,
    BatchProcessingService
  ],
})
export class AssetsModule {}
