import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import type {  Document, Types  } from 'mongoose';

export type UIVersionDocument = UIVersion & Document;

export enum VersionType {
  MAJOR = 'major',
  MINOR = 'minor',
  PATCH = 'patch',
}

@Schema({
  timestamps: true,
  collection: 'ui_versions',
})
export class UIVersion {
  @Prop({ type: Types.ObjectId, required: true, index: true })
  resourceId: Types.ObjectId; // 关联的资源ID（模板、组件等）

  @Prop({ required: true })
  resourceType: string; // 资源类型

  @Prop({ required: true })
  version: string;

  @Prop({ 
    type: String, 
    enum: Object.values(VersionType),
    required: true 
  })
  versionType: VersionType;

  @Prop({ maxlength: 500 })
  description?: string;

  @Prop({ type: Object, required: true })
  data: any; // 版本数据

  @Prop({ type: [String], default: [] })
  changes: string[]; // 变更记录

  @Prop({ type: Types.ObjectId, required: true })
  createdBy: Types.ObjectId;

  @Prop({ default: Date.now })
  createdAt: Date;
}

export const UIVersionSchema = SchemaFactory.createForClass(UIVersion);

// 创建索引
UIVersionSchema.index({ resourceId: 1, version: 1 }, { unique: true });
UIVersionSchema.index({ resourceType: 1, createdAt: -1 });
UIVersionSchema.index({ createdBy: 1, createdAt: -1 });
