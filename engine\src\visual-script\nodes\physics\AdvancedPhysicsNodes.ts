/**
 * 高级物理系统节点集合
 * 批次1.5：物理系统增强节点 - 15个节点
 * 包括高级物理节点（8个）和物理优化节点（7个）
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, BufferGeometry, Mesh } from 'three';

/**
 * 流体配置接口
 */
export interface FluidConfig {
  restDensity: number;
  gasConstant: number;
  viscosity: number;
  surfaceTension: number;
  smoothingRadius: number;
  particleRadius: number;
  gravity: Vector3;
  damping: number;
  maxParticles: number;
}

/**
 * 绳索配置接口
 */
export interface RopeConfig {
  length: number;
  segments: number;
  stiffness: number;
  damping: number;
  gravity: Vector3;
  thickness: number;
  mass: number;
}

/**
 * 破坏配置接口
 */
export interface DestructionConfig {
  threshold: number;
  fragmentCount: number;
  explosionForce: number;
  fadeTime: number;
  particleSystem: boolean;
}

/**
 * 物理约束配置接口
 */
export interface PhysicsConstraintConfig {
  type: 'fixed' | 'hinge' | 'slider' | 'ball' | 'cone';
  bodyA: string;
  bodyB: string;
  anchorA: Vector3;
  anchorB: Vector3;
  axis: Vector3;
  limits: { min: number; max: number };
  stiffness: number;
  damping: number;
}

/**
 * 高级物理管理器
 */
class AdvancedPhysicsManager {
  private static instance: AdvancedPhysicsManager;
  private fluidSystems: Map<string, any> = new Map();
  private ropeSystems: Map<string, any> = new Map();
  private constraints: Map<string, any> = new Map();
  private destructionSystems: Map<string, any> = new Map();

  public static getInstance(): AdvancedPhysicsManager {
    if (!AdvancedPhysicsManager.instance) {
      AdvancedPhysicsManager.instance = new AdvancedPhysicsManager();
    }
    return AdvancedPhysicsManager.instance;
  }

  createFluidSystem(id: string, config: FluidConfig): any {
    const fluidSystem = { id, config, particles: [], active: true };
    this.fluidSystems.set(id, fluidSystem);
    Debug.log('AdvancedPhysicsManager', `流体系统创建: ${id}`);
    return fluidSystem;
  }

  createRopeSystem(id: string, config: RopeConfig): any {
    const ropeSystem = { id, config, nodes: [], constraints: [], active: true };
    this.ropeSystems.set(id, ropeSystem);
    Debug.log('AdvancedPhysicsManager', `绳索系统创建: ${id}`);
    return ropeSystem;
  }

  createConstraint(id: string, config: PhysicsConstraintConfig): any {
    const constraint = { id, config, active: true };
    this.constraints.set(id, constraint);
    Debug.log('AdvancedPhysicsManager', `物理约束创建: ${id}`);
    return constraint;
  }

  update(deltaTime: number): void {
    // 更新所有物理系统
    for (const fluidSystem of this.fluidSystems.values()) {
      if (fluidSystem.active) {
        this.updateFluidSystem(fluidSystem, deltaTime);
      }
    }
    
    for (const ropeSystem of this.ropeSystems.values()) {
      if (ropeSystem.active) {
        this.updateRopeSystem(ropeSystem, deltaTime);
      }
    }
  }

  private updateFluidSystem(fluidSystem: any, deltaTime: number): void {
    // 简化的流体模拟更新
  }

  private updateRopeSystem(ropeSystem: any, deltaTime: number): void {
    // 简化的绳索模拟更新
  }
}

/**
 * 软体物理节点
 */
export class SoftBodyPhysicsNode extends VisualScriptNode {
  public static readonly TYPE = 'SoftBodyPhysics';
  public static readonly NAME = '软体物理';
  public static readonly DESCRIPTION = '创建和控制软体物理对象';

  private physicsManager: AdvancedPhysicsManager;

  constructor(nodeType: string = SoftBodyPhysicsNode.TYPE, name: string = SoftBodyPhysicsNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.physicsManager = AdvancedPhysicsManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建软体');
    this.addInput('update', 'trigger', '更新');
    this.addInput('bodyId', 'string', '软体ID');
    this.addInput('geometry', 'object', '几何体');
    this.addInput('mass', 'number', '质量');
    this.addInput('stiffness', 'number', '刚度');
    this.addInput('damping', 'number', '阻尼');

    // 输出端口
    this.addOutput('softBody', 'object', '软体对象');
    this.addOutput('bodyId', 'string', '软体ID');
    this.addOutput('particleCount', 'number', '粒子数量');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onUpdated', 'trigger', '更新完成');
    this.addOutput('onError', 'trigger', '错误');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.update;

      if (createTrigger) {
        return this.createSoftBody(inputs);
      } else if (updateTrigger) {
        return this.updateSoftBody(inputs);
      }

      return this.getDefaultOutputs();
    } catch (error) {
      Debug.error('SoftBodyPhysicsNode', '软体物理操作失败', error);
      return { ...this.getDefaultOutputs(), onError: true };
    }
  }

  private createSoftBody(inputs: any): any {
    const bodyId = inputs?.bodyId as string || this.generateId();
    const geometry = inputs?.geometry as BufferGeometry;
    const mass = inputs?.mass as number || 1.0;
    const stiffness = inputs?.stiffness as number || 0.8;
    const damping = inputs?.damping as number || 0.1;

    if (!geometry) {
      throw new Error('未提供几何体');
    }

    const softBody = {
      id: bodyId,
      geometry,
      mass,
      stiffness,
      damping,
      particles: [],
      constraints: []
    };

    Debug.log('SoftBodyPhysicsNode', `软体物理创建: ${bodyId}`);

    return {
      softBody,
      bodyId,
      particleCount: 0,
      onCreated: true,
      onUpdated: false,
      onError: false
    };
  }

  private updateSoftBody(inputs: any): any {
    const bodyId = inputs?.bodyId as string;

    if (!bodyId) {
      throw new Error('未提供软体ID');
    }

    Debug.log('SoftBodyPhysicsNode', `软体物理更新: ${bodyId}`);

    return {
      softBody: null,
      bodyId,
      particleCount: 0,
      onCreated: false,
      onUpdated: true,
      onError: false
    };
  }

  private generateId(): string {
    return 'softbody_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      softBody: null,
      bodyId: '',
      particleCount: 0,
      onCreated: false,
      onUpdated: false,
      onError: false
    };
  }
}

/**
 * 流体模拟节点
 */
export class FluidSimulationNode extends VisualScriptNode {
  public static readonly TYPE = 'FluidSimulation';
  public static readonly NAME = '流体模拟';
  public static readonly DESCRIPTION = '创建和控制流体模拟系统';

  private physicsManager: AdvancedPhysicsManager;

  constructor(nodeType: string = FluidSimulationNode.TYPE, name: string = FluidSimulationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.physicsManager = AdvancedPhysicsManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建流体');
    this.addInput('addParticle', 'trigger', '添加粒子');
    this.addInput('fluidId', 'string', '流体ID');
    this.addInput('position', 'object', '位置');
    this.addInput('restDensity', 'number', '静止密度');
    this.addInput('viscosity', 'number', '粘度');
    this.addInput('surfaceTension', 'number', '表面张力');

    // 输出端口
    this.addOutput('fluidSystem', 'object', '流体系统');
    this.addOutput('fluidId', 'string', '流体ID');
    this.addOutput('particleCount', 'number', '粒子数量');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onParticleAdded', 'trigger', '粒子添加完成');
    this.addOutput('onError', 'trigger', '错误');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const addParticleTrigger = inputs?.addParticle;

      if (createTrigger) {
        return this.createFluidSystem(inputs);
      } else if (addParticleTrigger) {
        return this.addParticle(inputs);
      }

      return this.getDefaultOutputs();
    } catch (error) {
      Debug.error('FluidSimulationNode', '流体模拟操作失败', error);
      return { ...this.getDefaultOutputs(), onError: true };
    }
  }

  private createFluidSystem(inputs: any): any {
    const fluidId = inputs?.fluidId as string || this.generateId();
    const restDensity = inputs?.restDensity as number || 1000;
    const viscosity = inputs?.viscosity as number || 0.1;
    const surfaceTension = inputs?.surfaceTension as number || 0.1;

    const config: FluidConfig = {
      restDensity,
      gasConstant: 2000,
      viscosity,
      surfaceTension,
      smoothingRadius: 0.1,
      particleRadius: 0.05,
      gravity: new Vector3(0, -9.81, 0),
      damping: 0.1,
      maxParticles: 1000
    };

    const fluidSystem = this.physicsManager.createFluidSystem(fluidId, config);

    Debug.log('FluidSimulationNode', `流体系统创建: ${fluidId}`);

    return {
      fluidSystem,
      fluidId,
      particleCount: 0,
      onCreated: true,
      onParticleAdded: false,
      onError: false
    };
  }

  private addParticle(inputs: any): any {
    const fluidId = inputs?.fluidId as string;
    const position = inputs?.position as Vector3 || new Vector3(0, 0, 0);

    if (!fluidId) {
      throw new Error('未提供流体ID');
    }

    Debug.log('FluidSimulationNode', `流体粒子添加: ${fluidId}`);

    return {
      fluidSystem: null,
      fluidId,
      particleCount: 1,
      onCreated: false,
      onParticleAdded: true,
      onError: false
    };
  }

  private generateId(): string {
    return 'fluid_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      fluidSystem: null,
      fluidId: '',
      particleCount: 0,
      onCreated: false,
      onParticleAdded: false,
      onError: false
    };
  }
}

/**
 * 布料模拟节点
 */
export class ClothSimulationNode extends VisualScriptNode {
  public static readonly TYPE = 'ClothSimulation';
  public static readonly NAME = '布料模拟';
  public static readonly DESCRIPTION = '创建和控制布料物理模拟';

  private physicsManager: AdvancedPhysicsManager;

  constructor(nodeType: string = ClothSimulationNode.TYPE, name: string = ClothSimulationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.physicsManager = AdvancedPhysicsManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建布料');
    this.addInput('pin', 'trigger', '固定顶点');
    this.addInput('clothId', 'string', '布料ID');
    this.addInput('width', 'number', '宽度');
    this.addInput('height', 'number', '高度');
    this.addInput('segments', 'number', '分段数');
    this.addInput('stiffness', 'number', '刚度');
    this.addInput('damping', 'number', '阻尼');
    this.addInput('pinPosition', 'object', '固定位置');

    // 输出端口
    this.addOutput('cloth', 'object', '布料对象');
    this.addOutput('clothId', 'string', '布料ID');
    this.addOutput('vertexCount', 'number', '顶点数量');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onPinned', 'trigger', '固定完成');
    this.addOutput('onError', 'trigger', '错误');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const pinTrigger = inputs?.pin;

      if (createTrigger) {
        return this.createCloth(inputs);
      } else if (pinTrigger) {
        return this.pinVertex(inputs);
      }

      return this.getDefaultOutputs();
    } catch (error) {
      Debug.error('ClothSimulationNode', '布料模拟操作失败', error);
      return { ...this.getDefaultOutputs(), onError: true };
    }
  }

  private createCloth(inputs: any): any {
    const clothId = inputs?.clothId as string || this.generateId();
    const width = inputs?.width as number || 2;
    const height = inputs?.height as number || 2;
    const segments = inputs?.segments as number || 10;
    const stiffness = inputs?.stiffness as number || 0.8;
    const damping = inputs?.damping as number || 0.1;

    const cloth = {
      id: clothId,
      width,
      height,
      segments,
      stiffness,
      damping,
      vertices: [],
      constraints: []
    };

    Debug.log('ClothSimulationNode', `布料创建: ${clothId}`);

    return {
      cloth,
      clothId,
      vertexCount: segments * segments,
      onCreated: true,
      onPinned: false,
      onError: false
    };
  }

  private pinVertex(inputs: any): any {
    const clothId = inputs?.clothId as string;
    const pinPosition = inputs?.pinPosition as Vector3;

    if (!clothId) {
      throw new Error('未提供布料ID');
    }

    Debug.log('ClothSimulationNode', `布料顶点固定: ${clothId}`);

    return {
      cloth: null,
      clothId,
      vertexCount: 0,
      onCreated: false,
      onPinned: true,
      onError: false
    };
  }

  private generateId(): string {
    return 'cloth_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      cloth: null,
      clothId: '',
      vertexCount: 0,
      onCreated: false,
      onPinned: false,
      onError: false
    };
  }
}

/**
 * 绳索模拟节点
 */
export class RopeSimulationNode extends VisualScriptNode {
  public static readonly TYPE = 'RopeSimulation';
  public static readonly NAME = '绳索模拟';
  public static readonly DESCRIPTION = '创建和控制绳索物理模拟';

  private physicsManager: AdvancedPhysicsManager;

  constructor(nodeType: string = RopeSimulationNode.TYPE, name: string = RopeSimulationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.physicsManager = AdvancedPhysicsManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建绳索');
    this.addInput('attach', 'trigger', '附加到对象');
    this.addInput('ropeId', 'string', '绳索ID');
    this.addInput('startPosition', 'object', '起始位置');
    this.addInput('endPosition', 'object', '结束位置');
    this.addInput('segments', 'number', '分段数');
    this.addInput('stiffness', 'number', '刚度');
    this.addInput('damping', 'number', '阻尼');
    this.addInput('attachObject', 'object', '附加对象');

    // 输出端口
    this.addOutput('rope', 'object', '绳索对象');
    this.addOutput('ropeId', 'string', '绳索ID');
    this.addOutput('length', 'number', '绳索长度');
    this.addOutput('tension', 'number', '张力');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onAttached', 'trigger', '附加完成');
    this.addOutput('onError', 'trigger', '错误');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const attachTrigger = inputs?.attach;

      if (createTrigger) {
        return this.createRope(inputs);
      } else if (attachTrigger) {
        return this.attachToObject(inputs);
      }

      return this.getDefaultOutputs();
    } catch (error) {
      Debug.error('RopeSimulationNode', '绳索模拟操作失败', error);
      return { ...this.getDefaultOutputs(), onError: true };
    }
  }

  private createRope(inputs: any): any {
    const ropeId = inputs?.ropeId as string || this.generateId();
    const startPosition = inputs?.startPosition as Vector3 || new Vector3(0, 5, 0);
    const endPosition = inputs?.endPosition as Vector3 || new Vector3(0, 0, 0);
    const segments = inputs?.segments as number || 10;
    const stiffness = inputs?.stiffness as number || 0.9;
    const damping = inputs?.damping as number || 0.1;

    const length = startPosition.distanceTo(endPosition);

    const config: RopeConfig = {
      length,
      segments,
      stiffness,
      damping,
      gravity: new Vector3(0, -9.81, 0),
      thickness: 0.1,
      mass: 1.0
    };

    const rope = this.physicsManager.createRopeSystem(ropeId, config);

    Debug.log('RopeSimulationNode', `绳索创建: ${ropeId}`);

    return {
      rope,
      ropeId,
      length,
      tension: 0,
      onCreated: true,
      onAttached: false,
      onError: false
    };
  }

  private attachToObject(inputs: any): any {
    const ropeId = inputs?.ropeId as string;
    const attachObject = inputs?.attachObject;

    if (!ropeId) {
      throw new Error('未提供绳索ID');
    }

    Debug.log('RopeSimulationNode', `绳索附加: ${ropeId}`);

    return {
      rope: null,
      ropeId,
      length: 0,
      tension: 0,
      onCreated: false,
      onAttached: true,
      onError: false
    };
  }

  private generateId(): string {
    return 'rope_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      rope: null,
      ropeId: '',
      length: 0,
      tension: 0,
      onCreated: false,
      onAttached: false,
      onError: false
    };
  }
}

/**
 * 破坏效果节点
 */
export class DestructionNode extends VisualScriptNode {
  public static readonly TYPE = 'Destruction';
  public static readonly NAME = '破坏效果';
  public static readonly DESCRIPTION = '创建物体破坏和碎片效果';

  constructor(nodeType: string = DestructionNode.TYPE, name: string = DestructionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('destroy', 'trigger', '执行破坏');
    this.addInput('object', 'object', '目标对象');
    this.addInput('impactPoint', 'object', '撞击点');
    this.addInput('force', 'number', '破坏力度');
    this.addInput('fragmentCount', 'number', '碎片数量');
    this.addInput('explosionForce', 'number', '爆炸力');

    // 输出端口
    this.addOutput('fragments', 'array', '碎片数组');
    this.addOutput('fragmentCount', 'number', '碎片数量');
    this.addOutput('onDestroyed', 'trigger', '破坏完成');
    this.addOutput('onFragmentCreated', 'trigger', '碎片创建');
    this.addOutput('onError', 'trigger', '错误');
  }

  public execute(inputs?: any): any {
    try {
      const destroyTrigger = inputs?.destroy;

      if (destroyTrigger) {
        return this.executeDestruction(inputs);
      }

      return this.getDefaultOutputs();
    } catch (error) {
      Debug.error('DestructionNode', '破坏效果操作失败', error);
      return { ...this.getDefaultOutputs(), onError: true };
    }
  }

  private executeDestruction(inputs: any): any {
    const object = inputs?.object;
    const impactPoint = inputs?.impactPoint as Vector3 || new Vector3(0, 0, 0);
    const force = inputs?.force as number || 100;
    const fragmentCount = inputs?.fragmentCount as number || 10;
    const explosionForce = inputs?.explosionForce as number || 50;

    if (!object) {
      throw new Error('未提供目标对象');
    }

    const config: DestructionConfig = {
      threshold: force,
      fragmentCount,
      explosionForce,
      fadeTime: 5.0,
      particleSystem: true
    };

    const fragments = this.createFragments(object, impactPoint, config);

    Debug.log('DestructionNode', `破坏效果执行，生成${fragments.length}个碎片`);

    return {
      fragments,
      fragmentCount: fragments.length,
      onDestroyed: true,
      onFragmentCreated: true,
      onError: false
    };
  }

  private createFragments(object: any, impactPoint: Vector3, config: DestructionConfig): any[] {
    const fragments = [];

    for (let i = 0; i < config.fragmentCount; i++) {
      const fragment = {
        id: `fragment_${i}`,
        position: impactPoint.clone().add(new Vector3(
          (Math.random() - 0.5) * 2,
          (Math.random() - 0.5) * 2,
          (Math.random() - 0.5) * 2
        )),
        velocity: new Vector3(
          (Math.random() - 0.5) * config.explosionForce,
          Math.random() * config.explosionForce,
          (Math.random() - 0.5) * config.explosionForce
        ),
        mass: 0.1,
        lifetime: config.fadeTime
      };

      fragments.push(fragment);
    }

    return fragments;
  }

  private getDefaultOutputs(): any {
    return {
      fragments: [],
      fragmentCount: 0,
      onDestroyed: false,
      onFragmentCreated: false,
      onError: false
    };
  }
}

/**
 * 物理约束节点
 */
export class PhysicsConstraintNode extends VisualScriptNode {
  public static readonly TYPE = 'PhysicsConstraint';
  public static readonly NAME = '物理约束';
  public static readonly DESCRIPTION = '创建和管理物理约束';

  private physicsManager: AdvancedPhysicsManager;

  constructor(nodeType: string = PhysicsConstraintNode.TYPE, name: string = PhysicsConstraintNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.physicsManager = AdvancedPhysicsManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建约束');
    this.addInput('remove', 'trigger', '移除约束');
    this.addInput('constraintId', 'string', '约束ID');
    this.addInput('type', 'string', '约束类型');
    this.addInput('bodyA', 'object', '物体A');
    this.addInput('bodyB', 'object', '物体B');
    this.addInput('anchorA', 'object', '锚点A');
    this.addInput('anchorB', 'object', '锚点B');
    this.addInput('stiffness', 'number', '刚度');
    this.addInput('damping', 'number', '阻尼');

    // 输出端口
    this.addOutput('constraint', 'object', '约束对象');
    this.addOutput('constraintId', 'string', '约束ID');
    this.addOutput('force', 'number', '约束力');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onRemoved', 'trigger', '移除完成');
    this.addOutput('onError', 'trigger', '错误');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const removeTrigger = inputs?.remove;

      if (createTrigger) {
        return this.createConstraint(inputs);
      } else if (removeTrigger) {
        return this.removeConstraint(inputs);
      }

      return this.getDefaultOutputs();
    } catch (error) {
      Debug.error('PhysicsConstraintNode', '物理约束操作失败', error);
      return { ...this.getDefaultOutputs(), onError: true };
    }
  }

  private createConstraint(inputs: any): any {
    const constraintId = inputs?.constraintId as string || this.generateId();
    const type = inputs?.type as string || 'fixed';
    const bodyA = inputs?.bodyA;
    const bodyB = inputs?.bodyB;
    const anchorA = inputs?.anchorA as Vector3 || new Vector3(0, 0, 0);
    const anchorB = inputs?.anchorB as Vector3 || new Vector3(0, 0, 0);
    const stiffness = inputs?.stiffness as number || 1.0;
    const damping = inputs?.damping as number || 0.1;

    if (!bodyA || !bodyB) {
      throw new Error('未提供约束物体');
    }

    const config: PhysicsConstraintConfig = {
      type: type as any,
      bodyA: bodyA.id || 'bodyA',
      bodyB: bodyB.id || 'bodyB',
      anchorA,
      anchorB,
      axis: new Vector3(0, 1, 0),
      limits: { min: -Math.PI, max: Math.PI },
      stiffness,
      damping
    };

    const constraint = this.physicsManager.createConstraint(constraintId, config);

    Debug.log('PhysicsConstraintNode', `物理约束创建: ${constraintId} (${type})`);

    return {
      constraint,
      constraintId,
      force: 0,
      onCreated: true,
      onRemoved: false,
      onError: false
    };
  }

  private removeConstraint(inputs: any): any {
    const constraintId = inputs?.constraintId as string;

    if (!constraintId) {
      throw new Error('未提供约束ID');
    }

    Debug.log('PhysicsConstraintNode', `物理约束移除: ${constraintId}`);

    return {
      constraint: null,
      constraintId,
      force: 0,
      onCreated: false,
      onRemoved: true,
      onError: false
    };
  }

  private generateId(): string {
    return 'constraint_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      constraint: null,
      constraintId: '',
      force: 0,
      onCreated: false,
      onRemoved: false,
      onError: false
    };
  }
}
