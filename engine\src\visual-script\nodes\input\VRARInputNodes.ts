/**
 * VR/AR输入节点集合
 * 提供VR/AR相关的输入功能，包括控制器、头显追踪、手部追踪等
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Quaternion, Matrix4 } from 'three';

/**
 * VR控制器输入节点
 * 处理VR控制器的输入数据
 */
export class VRControllerInputNode extends VisualScriptNode {
  public static readonly TYPE = 'input/vr_controller_input';
  public static readonly NAME = 'VR控制器输入';
  public static readonly DESCRIPTION = '处理VR控制器的输入数据，包括位置、旋转、按钮和摇杆';

  constructor(type: string = VRControllerInputNode.TYPE, name: string = VRControllerInputNode.NAME, id?: string) {
    super(type, name, id);
    
    // 输入端口
    this.addInputPort('enable', '启用', 'boolean', true);
    this.addInputPort('controllerId', '控制器ID', 'string', 'right');
    this.addInputPort('hapticIntensity', '震动强度', 'number', 0);
    this.addInputPort('hapticDuration', '震动时长', 'number', 100);
    
    // 输出端口
    this.addOutputPort('position', '位置', 'vector3');
    this.addOutputPort('rotation', '旋转', 'quaternion');
    this.addOutputPort('connected', '是否连接', 'boolean');
    this.addOutputPort('triggerValue', '扳机值', 'number');
    this.addOutputPort('gripValue', '握持值', 'number');
    this.addOutputPort('thumbstick', '摇杆', 'vector2');
    this.addOutputPort('buttonA', 'A按钮', 'boolean');
    this.addOutputPort('buttonB', 'B按钮', 'boolean');
    this.addOutputPort('buttonX', 'X按钮', 'boolean');
    this.addOutputPort('buttonY', 'Y按钮', 'boolean');
    this.addOutputPort('onTriggerPress', '扳机按下', 'event');
    this.addOutputPort('onGripPress', '握持按下', 'event');
    this.addOutputPort('onButtonPress', '按钮按下', 'event');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const controllerId = inputs?.controllerId as string || 'right';
      const hapticIntensity = inputs?.hapticIntensity as number || 0;
      const hapticDuration = inputs?.hapticDuration as number || 100;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 模拟VR控制器数据
      const position = new Vector3(
        Math.sin(Date.now() * 0.001) * 0.1,
        1.5 + Math.cos(Date.now() * 0.002) * 0.05,
        -0.5
      );
      
      const rotation = new Quaternion().setFromEuler({
        x: Math.sin(Date.now() * 0.001) * 0.1,
        y: Math.cos(Date.now() * 0.0015) * 0.2,
        z: 0
      } as any);

      const connected = true;
      const triggerValue = Math.random() > 0.8 ? Math.random() : 0;
      const gripValue = Math.random() > 0.9 ? Math.random() : 0;
      const thumbstick = {
        x: (Math.random() - 0.5) * 2,
        y: (Math.random() - 0.5) * 2
      };

      const buttonA = Math.random() > 0.95;
      const buttonB = Math.random() > 0.95;
      const buttonX = Math.random() > 0.95;
      const buttonY = Math.random() > 0.95;

      // 触发震动
      if (hapticIntensity > 0) {
        Debug.log('VRControllerInputNode', `触发震动: 强度=${hapticIntensity}, 时长=${hapticDuration}ms`);
      }

      return {
        position,
        rotation,
        connected,
        triggerValue,
        gripValue,
        thumbstick,
        buttonA,
        buttonB,
        buttonX,
        buttonY,
        onTriggerPress: triggerValue > 0.5,
        onGripPress: gripValue > 0.5,
        onButtonPress: buttonA || buttonB || buttonX || buttonY
      };

    } catch (error) {
      Debug.error('VRControllerInputNode', 'VR控制器输入处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      position: new Vector3(0, 0, 0),
      rotation: new Quaternion(),
      connected: false,
      triggerValue: 0,
      gripValue: 0,
      thumbstick: { x: 0, y: 0 },
      buttonA: false,
      buttonB: false,
      buttonX: false,
      buttonY: false,
      onTriggerPress: false,
      onGripPress: false,
      onButtonPress: false
    };
  }
}

/**
 * VR头显追踪节点
 * 获取VR头显的位置和旋转信息
 */
export class VRHeadsetTrackingNode extends VisualScriptNode {
  public static readonly TYPE = 'input/vr_headset_tracking';
  public static readonly NAME = 'VR头显追踪';
  public static readonly DESCRIPTION = '获取VR头显的位置和旋转信息，用于头部追踪';

  constructor(type: string = VRHeadsetTrackingNode.TYPE, name: string = VRHeadsetTrackingNode.NAME, id?: string) {
    super(type, name, id);
    
    // 输入端口
    this.addInputPort('enable', '启用', 'boolean', true);
    this.addInputPort('smoothing', '平滑度', 'number', 0.1);
    
    // 输出端口
    this.addOutputPort('position', '位置', 'vector3');
    this.addOutputPort('rotation', '旋转', 'quaternion');
    this.addOutputPort('viewMatrix', '视图矩阵', 'matrix4');
    this.addOutputPort('projectionMatrix', '投影矩阵', 'matrix4');
    this.addOutputPort('isTracking', '是否追踪', 'boolean');
    this.addOutputPort('trackingQuality', '追踪质量', 'number');
    this.addOutputPort('onTrackingLost', '追踪丢失', 'event');
    this.addOutputPort('onTrackingRecovered', '追踪恢复', 'event');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const smoothing = inputs?.smoothing as number || 0.1;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 模拟VR头显追踪数据
      const position = new Vector3(0, 1.7, 0); // 标准头部高度
      const rotation = new Quaternion().setFromEuler({
        x: Math.sin(Date.now() * 0.0005) * 0.1,
        y: Math.cos(Date.now() * 0.0008) * 0.2,
        z: 0
      } as any);

      const viewMatrix = new Matrix4().makeRotationFromQuaternion(rotation).setPosition(position);
      const projectionMatrix = new Matrix4().makePerspective(-1, 1, 1, -1, 0.1, 1000);
      
      const isTracking = Math.random() > 0.05; // 95%概率正在追踪
      const trackingQuality = isTracking ? 0.8 + Math.random() * 0.2 : 0;

      return {
        position,
        rotation,
        viewMatrix,
        projectionMatrix,
        isTracking,
        trackingQuality,
        onTrackingLost: !isTracking,
        onTrackingRecovered: isTracking
      };

    } catch (error) {
      Debug.error('VRHeadsetTrackingNode', 'VR头显追踪处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      position: new Vector3(0, 0, 0),
      rotation: new Quaternion(),
      viewMatrix: new Matrix4(),
      projectionMatrix: new Matrix4(),
      isTracking: false,
      trackingQuality: 0,
      onTrackingLost: false,
      onTrackingRecovered: false
    };
  }
}

/**
 * AR触摸输入节点
 * 处理AR环境中的触摸输入
 */
export class ARTouchInputNode extends VisualScriptNode {
  public static readonly TYPE = 'input/ar_touch_input';
  public static readonly NAME = 'AR触摸输入';
  public static readonly DESCRIPTION = '处理AR环境中的触摸输入，支持3D空间中的触摸交互';

  constructor(type: string = ARTouchInputNode.TYPE, name: string = ARTouchInputNode.NAME, id?: string) {
    super(type, name, id);
    
    // 输入端口
    this.addInputPort('enable', '启用', 'boolean', true);
    this.addInputPort('raycastDistance', '射线距离', 'number', 10);
    this.addInputPort('touchSensitivity', '触摸敏感度', 'number', 1.0);
    
    // 输出端口
    this.addOutputPort('touchPosition', '触摸位置', 'vector2');
    this.addOutputPort('worldPosition', '世界位置', 'vector3');
    this.addOutputPort('hitObject', '碰撞对象', 'object');
    this.addOutputPort('hitDistance', '碰撞距离', 'number');
    this.addOutputPort('isTouching', '是否触摸', 'boolean');
    this.addOutputPort('touchPressure', '触摸压力', 'number');
    this.addOutputPort('onTouchStart', '触摸开始', 'event');
    this.addOutputPort('onTouchMove', '触摸移动', 'event');
    this.addOutputPort('onTouchEnd', '触摸结束', 'event');
    this.addOutputPort('onObjectHit', '对象碰撞', 'event');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const raycastDistance = inputs?.raycastDistance as number || 10;
      const touchSensitivity = inputs?.touchSensitivity as number || 1.0;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 模拟AR触摸输入数据
      const isTouching = Math.random() > 0.8;
      const touchPosition = {
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight
      };

      const worldPosition = new Vector3(
        (touchPosition.x / window.innerWidth - 0.5) * 10,
        (0.5 - touchPosition.y / window.innerHeight) * 10,
        Math.random() * raycastDistance
      );

      const hitObject = isTouching ? { id: 'ar_object_' + Math.floor(Math.random() * 100) } : null;
      const hitDistance = isTouching ? Math.random() * raycastDistance : 0;
      const touchPressure = isTouching ? 0.5 + Math.random() * 0.5 : 0;

      return {
        touchPosition,
        worldPosition,
        hitObject,
        hitDistance,
        isTouching,
        touchPressure,
        onTouchStart: isTouching && Math.random() > 0.7,
        onTouchMove: isTouching && Math.random() > 0.5,
        onTouchEnd: !isTouching && Math.random() > 0.8,
        onObjectHit: hitObject !== null
      };

    } catch (error) {
      Debug.error('ARTouchInputNode', 'AR触摸输入处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      touchPosition: { x: 0, y: 0 },
      worldPosition: new Vector3(0, 0, 0),
      hitObject: null,
      hitDistance: 0,
      isTouching: false,
      touchPressure: 0,
      onTouchStart: false,
      onTouchMove: false,
      onTouchEnd: false,
      onObjectHit: false
    };
  }
}

/**
 * AR手势输入节点
 * 处理AR环境中的手势识别
 */
export class ARGestureInputNode extends VisualScriptNode {
  public static readonly TYPE = 'input/ar_gesture_input';
  public static readonly NAME = 'AR手势输入';
  public static readonly DESCRIPTION = '处理AR环境中的手势识别，支持空中手势和物体操作手势';

  constructor(type: string = ARGestureInputNode.TYPE, name: string = ARGestureInputNode.NAME, id?: string) {
    super(type, name, id);

    // 输入端口
    this.addInputPort('enable', '启用', 'boolean', true);
    this.addInputPort('gestureTypes', '手势类型', 'array', ['tap', 'pinch', 'swipe', 'rotate']);
    this.addInputPort('minConfidence', '最小置信度', 'number', 0.8);
    this.addInputPort('trackingMode', '追踪模式', 'string', 'hands');

    // 输出端口
    this.addOutputPort('gestureType', '手势类型', 'string');
    this.addOutputPort('confidence', '置信度', 'number');
    this.addOutputPort('handPosition', '手部位置', 'vector3');
    this.addOutputPort('fingerPositions', '手指位置', 'array');
    this.addOutputPort('gestureDirection', '手势方向', 'vector3');
    this.addOutputPort('gestureSpeed', '手势速度', 'number');
    this.addOutputPort('onGestureStart', '手势开始', 'event');
    this.addOutputPort('onGestureEnd', '手势结束', 'event');
    this.addOutputPort('onHandDetected', '手部检测', 'event');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const gestureTypes = inputs?.gestureTypes as string[] || ['tap', 'pinch', 'swipe', 'rotate'];
      const minConfidence = inputs?.minConfidence as number || 0.8;
      const trackingMode = inputs?.trackingMode as string || 'hands';

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 模拟AR手势识别数据
      const isGestureDetected = Math.random() > 0.7;
      const gestureType = isGestureDetected ?
        gestureTypes[Math.floor(Math.random() * gestureTypes.length)] : '';
      const confidence = isGestureDetected ? minConfidence + Math.random() * (1 - minConfidence) : 0;

      const handPosition = new Vector3(
        (Math.random() - 0.5) * 2,
        Math.random() * 2,
        -(Math.random() * 3 + 1)
      );

      // 模拟5个手指的位置
      const fingerPositions = [];
      for (let i = 0; i < 5; i++) {
        fingerPositions.push(new Vector3(
          handPosition.x + (Math.random() - 0.5) * 0.2,
          handPosition.y + (Math.random() - 0.5) * 0.2,
          handPosition.z + (Math.random() - 0.5) * 0.1
        ));
      }

      const gestureDirection = new Vector3(
        Math.random() - 0.5,
        Math.random() - 0.5,
        Math.random() - 0.5
      ).normalize();

      const gestureSpeed = Math.random() * 5;

      return {
        gestureType,
        confidence,
        handPosition,
        fingerPositions,
        gestureDirection,
        gestureSpeed,
        onGestureStart: isGestureDetected && Math.random() > 0.8,
        onGestureEnd: !isGestureDetected && Math.random() > 0.8,
        onHandDetected: Math.random() > 0.3
      };

    } catch (error) {
      Debug.error('ARGestureInputNode', 'AR手势输入处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      gestureType: '',
      confidence: 0,
      handPosition: new Vector3(0, 0, 0),
      fingerPositions: [],
      gestureDirection: new Vector3(0, 0, 0),
      gestureSpeed: 0,
      onGestureStart: false,
      onGestureEnd: false,
      onHandDetected: false
    };
  }
}

/**
 * 空间输入节点
 * 处理3D空间中的输入交互
 */
export class SpatialInputNode extends VisualScriptNode {
  public static readonly TYPE = 'input/spatial_input';
  public static readonly NAME = '空间输入';
  public static readonly DESCRIPTION = '处理3D空间中的输入交互，支持空间定位和6DOF输入';

  constructor(type: string = SpatialInputNode.TYPE, name: string = SpatialInputNode.NAME, id?: string) {
    super(type, name, id);

    // 输入端口
    this.addInputPort('enable', '启用', 'boolean', true);
    this.addInputPort('trackingSpace', '追踪空间', 'string', 'room');
    this.addInputPort('referenceFrame', '参考坐标系', 'string', 'local');

    // 输出端口
    this.addOutputPort('position', '位置', 'vector3');
    this.addOutputPort('rotation', '旋转', 'quaternion');
    this.addOutputPort('velocity', '速度', 'vector3');
    this.addOutputPort('angularVelocity', '角速度', 'vector3');
    this.addOutputPort('acceleration', '加速度', 'vector3');
    this.addOutputPort('trackingState', '追踪状态', 'string');
    this.addOutputPort('boundaryInfo', '边界信息', 'object');
    this.addOutputPort('onBoundaryWarning', '边界警告', 'event');
    this.addOutputPort('onTrackingUpdate', '追踪更新', 'event');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const trackingSpace = inputs?.trackingSpace as string || 'room';
      const referenceFrame = inputs?.referenceFrame as string || 'local';

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 模拟空间输入数据
      const position = new Vector3(
        Math.sin(Date.now() * 0.001) * 2,
        1.5 + Math.cos(Date.now() * 0.0015) * 0.5,
        Math.cos(Date.now() * 0.0008) * 3
      );

      const rotation = new Quaternion().setFromEuler({
        x: Math.sin(Date.now() * 0.0005) * 0.2,
        y: Math.cos(Date.now() * 0.0008) * 0.3,
        z: Math.sin(Date.now() * 0.0012) * 0.1
      } as any);

      const velocity = new Vector3(
        Math.random() - 0.5,
        Math.random() - 0.5,
        Math.random() - 0.5
      ).multiplyScalar(2);

      const angularVelocity = new Vector3(
        Math.random() - 0.5,
        Math.random() - 0.5,
        Math.random() - 0.5
      ).multiplyScalar(Math.PI);

      const acceleration = new Vector3(
        Math.random() - 0.5,
        Math.random() - 0.5,
        Math.random() - 0.5
      ).multiplyScalar(5);

      const trackingStates = ['tracking', 'limited', 'lost'];
      const trackingState = trackingStates[Math.floor(Math.random() * trackingStates.length)];

      const boundaryInfo = {
        center: new Vector3(0, 0, 0),
        size: new Vector3(4, 3, 4),
        type: trackingSpace,
        isVisible: Math.random() > 0.8
      };

      const isNearBoundary = position.length() > 1.5;

      return {
        position,
        rotation,
        velocity,
        angularVelocity,
        acceleration,
        trackingState,
        boundaryInfo,
        onBoundaryWarning: isNearBoundary,
        onTrackingUpdate: true
      };

    } catch (error) {
      Debug.error('SpatialInputNode', '空间输入处理失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      position: new Vector3(0, 0, 0),
      rotation: new Quaternion(),
      velocity: new Vector3(0, 0, 0),
      angularVelocity: new Vector3(0, 0, 0),
      acceleration: new Vector3(0, 0, 0),
      trackingState: 'lost',
      boundaryInfo: null,
      onBoundaryWarning: false,
      onTrackingUpdate: false
    };
  }
}
