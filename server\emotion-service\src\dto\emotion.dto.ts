/**
 * 情感分析相关DTO
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsArray, IsDateString, Min, Max, ValidateNested } from 'class-validator';
import type {  Type  } from 'class-transformer';

/**
 * 情感分析请求DTO
 */
export class EmotionAnalysisDto {
  @ApiProperty({ description: '待分析的文本内容', example: '今天心情很好，阳光明媚' })
  @IsString()
  text: string;

  @ApiPropertyOptional({ description: '用户ID', example: 'user123' })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({ description: '上下文信息', example: { source: 'chat', sessionId: 'session123' } })
  @IsOptional()
  context?: any;

  @ApiPropertyOptional({ description: '数据来源', example: 'chat_message' })
  @IsOptional()
  @IsString()
  source?: string;
}

/**
 * 情感记录查询DTO
 */
export class EmotionQueryDto {
  @ApiPropertyOptional({ description: '用户ID', example: 'user123' })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({ description: '情感类型', example: 'happy' })
  @IsOptional()
  @IsString()
  emotionType?: string;

  @ApiPropertyOptional({ description: '开始时间', example: '2024-01-01T00:00:00Z' })
  @IsOptional()
  @IsDateString()
  startTime?: string;

  @ApiPropertyOptional({ description: '结束时间', example: '2024-12-31T23:59:59Z' })
  @IsOptional()
  @IsDateString()
  endTime?: string;

  @ApiPropertyOptional({ description: '最小强度', example: 0.5, minimum: 0, maximum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  minIntensity?: number;

  @ApiPropertyOptional({ description: '最大强度', example: 1.0, minimum: 0, maximum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  maxIntensity?: number;

  @ApiPropertyOptional({ description: '限制数量', example: 100, minimum: 1, maximum: 1000 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1000)
  limit?: number;

  @ApiPropertyOptional({ description: '偏移量', example: 0, minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  offset?: number;
}

/**
 * 单条情感记录DTO
 */
export class EmotionRecordDto {
  @ApiProperty({ description: '用户ID', example: 'user123' })
  @IsString()
  userId: string;

  @ApiProperty({ description: '情感类型', example: 'happy' })
  @IsString()
  emotionType: string;

  @ApiProperty({ description: '情感强度', example: 0.8, minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  intensity: number;

  @ApiProperty({ description: '置信度', example: 0.9, minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence: number;

  @ApiProperty({ description: '数据来源', example: 'text_analysis' })
  @IsString()
  source: string;

  @ApiPropertyOptional({ description: '上下文信息' })
  @IsOptional()
  context?: any;

  @ApiPropertyOptional({ description: '记录时间', example: '2024-01-01T12:00:00Z' })
  @IsOptional()
  @IsDateString()
  timestamp?: string;
}

/**
 * 批量情感记录DTO
 */
export class BatchEmotionRecordDto {
  @ApiProperty({ 
    description: '情感记录数组',
    type: [EmotionRecordDto],
    example: [
      {
        userId: 'user123',
        emotionType: 'happy',
        intensity: 0.8,
        confidence: 0.9,
        source: 'text_analysis',
        context: { originalText: '今天心情很好' }
      }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EmotionRecordDto)
  records: EmotionRecordDto[];
}

/**
 * 情感统计查询DTO
 */
export class EmotionStatisticsQueryDto {
  @ApiPropertyOptional({ description: '开始时间', example: '2024-01-01T00:00:00Z' })
  @IsOptional()
  @IsDateString()
  startTime?: string;

  @ApiPropertyOptional({ description: '结束时间', example: '2024-12-31T23:59:59Z' })
  @IsOptional()
  @IsDateString()
  endTime?: string;

  @ApiPropertyOptional({ description: '分组方式', example: 'day', enum: ['hour', 'day', 'week', 'month'] })
  @IsOptional()
  @IsString()
  groupBy?: 'hour' | 'day' | 'week' | 'month';
}

/**
 * 数据清理DTO
 */
export class DataCleanupDto {
  @ApiPropertyOptional({ description: '保留天数', example: 30, minimum: 1, maximum: 365 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(365)
  retentionDays?: number;

  @ApiPropertyOptional({ description: '是否强制清理', example: false })
  @IsOptional()
  force?: boolean;
}
