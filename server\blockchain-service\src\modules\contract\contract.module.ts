/**
 * 智能合约模块
 */

import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { ContractService } from './contract.service';
import { ContractController } from './contract.controller';
import { SmartContract } from '../../entities/smart-contract.entity';

@Module({
  imports: [TypeOrmModule.forFeature([SmartContract])],
  controllers: [ContractController],
  providers: [ContractService],
  exports: [ContractService],
})
export class ContractModule {}
