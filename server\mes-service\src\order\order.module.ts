import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { OrderController } from './order.controller';
import { OrderService } from './order.service';
import { ProductionOrder } from './entities/production-order.entity';

/**
 * 生产订单模块
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([ProductionOrder]),
  ],
  controllers: [OrderController],
  providers: [OrderService],
  exports: [OrderService],
})
export class OrderModule {}
