/**
 * 健康检查模块
 */
import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';
import { SpatialFeature } from '../entities/spatial-feature.entity';
import { SpatialLayer } from '../entities/spatial-layer.entity';
import { SpatialProject } from '../entities/spatial-project.entity';

@Module({
  imports: [
    TerminusModule,
    TypeOrmModule.forFeature([SpatialFeature, SpatialLayer, SpatialProject])
  ],
  controllers: [HealthController],
  providers: [HealthService],
  exports: [HealthService]
})
export class HealthModule {}
