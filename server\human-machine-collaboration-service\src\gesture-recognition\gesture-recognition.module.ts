import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { GestureRecognitionService } from './gesture-recognition.service';
import { HandTrackingService } from './hand-tracking.service';
import { GesturePatternService } from './gesture-pattern.service';
import { MotionAnalysisService } from './motion-analysis.service';
import { GesturePattern } from '../database/entities/gesture-pattern.entity';

/**
 * 手势识别模块
 * 提供手势识别、动作捕捉、模式匹配等功能
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([GesturePattern]),
  ],
  providers: [
    GestureRecognitionService,
    HandTrackingService,
    GesturePatternService,
    MotionAnalysisService,
  ],
  exports: [
    GestureRecognitionService,
    HandTrackingService,
    GesturePatternService,
    MotionAnalysisService,
  ],
})
export class GestureRecognitionModule {}
