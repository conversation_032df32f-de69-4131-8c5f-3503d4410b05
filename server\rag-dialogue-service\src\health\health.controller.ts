import { Controller, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import type { 
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
  HttpHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
 } from '@nestjs/terminus';
import { ConfigService } from '@nestjs/config';

@ApiTags('health')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private http: HttpHealthIndicator,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
    private configService: ConfigService,
  ) {}

  @Get()
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  @HealthCheck()
  check() {
    this.logger.log('执行健康检查');
    
    return this.health.check([
      // 数据库健康检查
      () => this.db.pingCheck('database'),
      
      // 内存健康检查 (堆内存不超过 300MB)
      () => this.memory.checkHeap('memory_heap', 300 * 1024 * 1024),
      
      // RSS内存检查 (不超过 300MB)
      () => this.memory.checkRSS('memory_rss', 300 * 1024 * 1024),
      
      // 磁盘健康检查 (可用空间不少于 1GB)
      () => this.disk.checkStorage('storage', {
        path: '/',
        thresholdPercent: 0.9,
      }),
    ]);
  }

  @Get('detailed')
  @ApiOperation({ summary: '详细健康检查' })
  @ApiResponse({ status: 200, description: '详细健康信息' })
  async detailedCheck() {
    this.logger.log('执行详细健康检查');
    
    const basicHealth = await this.check();
    
    // 添加额外的服务信息
    const additionalInfo = {
      service: 'rag-dialogue-service',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      pid: process.pid,
      memory: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
    };

    return {
      ...basicHealth,
      info: additionalInfo,
    };
  }

  @Get('readiness')
  @ApiOperation({ summary: '就绪检查' })
  @ApiResponse({ status: 200, description: '服务就绪' })
  @HealthCheck()
  readiness() {
    this.logger.log('执行就绪检查');
    
    return this.health.check([
      // 检查数据库连接
      () => this.db.pingCheck('database'),
      
      // 检查外部服务连接（如果配置了的话）
      ...(this.configService.get<string>('KNOWLEDGE_BASE_SERVICE_URL') 
        ? [() => this.http.pingCheck(
            'knowledge-base-service',
            `${this.configService.get<string>('KNOWLEDGE_BASE_SERVICE_URL')}/health`
          )]
        : []
      ),
    ]);
  }

  @Get('liveness')
  @ApiOperation({ summary: '存活检查' })
  @ApiResponse({ status: 200, description: '服务存活' })
  liveness() {
    this.logger.log('执行存活检查');
    
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }
}
