/**
 * 用户服务节点集合
 * 批次2.1 - 服务器集成节点
 * 提供用户认证、注册、资料管理等用户服务功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 用户信息接口
 */
export interface UserInfo {
  id: string;
  username: string;
  email: string;
  displayName: string;
  avatar?: string;
  roles: string[];
  permissions: string[];
  preferences: Record<string, any>;
  createdAt: Date;
  lastLoginAt?: Date;
  isActive: boolean;
}

/**
 * 用户会话接口
 */
export interface UserSession {
  sessionId: string;
  userId: string;
  token: string;
  expiresAt: Date;
  deviceInfo?: string;
  ipAddress?: string;
  isActive: boolean;
}

/**
 * 用户认证节点
 * 批次2.1 - 用户服务节点
 */
export class UserAuthenticationNode extends VisualScriptNode {
  public static readonly TYPE = 'UserAuthentication';
  public static readonly NAME = '用户认证';
  public static readonly DESCRIPTION = '处理用户登录认证和身份验证';

  constructor(nodeType: string = UserAuthenticationNode.TYPE, name: string = UserAuthenticationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('username', 'string', '用户名');
    this.addInput('password', 'string', '密码');
    this.addInput('authMethod', 'string', '认证方式');
    this.addInput('rememberMe', 'boolean', '记住我');
    this.addInput('deviceInfo', 'object', '设备信息');

    // 输出端口
    this.addOutput('success', 'boolean', '认证成功');
    this.addOutput('userInfo', 'object', '用户信息');
    this.addOutput('token', 'string', '访问令牌');
    this.addOutput('sessionId', 'string', '会话ID');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('onSuccess', 'trigger', '认证成功事件');
    this.addOutput('onFailure', 'trigger', '认证失败事件');
  }

  public execute(inputs?: any): any {
    try {
      const username = inputs?.username as string;
      const password = inputs?.password as string;
      const authMethod = inputs?.authMethod as string || 'password';
      const rememberMe = inputs?.rememberMe as boolean ?? false;
      const deviceInfo = inputs?.deviceInfo || {};

      if (!username || !password) {
        Debug.warn('UserAuthenticationNode', '用户名或密码为空');
        return this.getFailureOutput('用户名或密码不能为空');
      }

      // 执行用户认证
      const result = this.authenticateUser(username, password, authMethod, rememberMe, deviceInfo);
      
      Debug.log('UserAuthenticationNode', `用户认证${result.success ? '成功' : '失败'}: ${username}`);

      return result;
    } catch (error) {
      Debug.error('UserAuthenticationNode', '用户认证执行失败', error);
      return this.getFailureOutput(error instanceof Error ? error.message : '认证失败');
    }
  }

  private authenticateUser(username: string, password: string, method: string, rememberMe: boolean, deviceInfo: any): any {
    // 简化的认证实现
    // 实际应该调用后端认证服务
    
    // 模拟认证逻辑
    const isValidCredentials = this.validateCredentials(username, password, method);
    
    if (!isValidCredentials) {
      return this.getFailureOutput('用户名或密码错误');
    }

    // 创建用户会话
    const session = this.createUserSession(username, rememberMe, deviceInfo);
    
    // 获取用户信息
    const userInfo = this.getUserInfo(username);

    return {
      success: true,
      userInfo,
      token: session.token,
      sessionId: session.sessionId,
      errorMessage: '',
      onSuccess: true,
      onFailure: false
    };
  }

  private validateCredentials(username: string, password: string, method: string): boolean {
    // 简化的凭据验证
    // 实际应该调用认证服务验证
    switch (method) {
      case 'password':
        return username.length > 0 && password.length >= 6;
      case 'oauth':
        return username.length > 0;
      case 'ldap':
        return username.includes('@') && password.length > 0;
      default:
        return false;
    }
  }

  private createUserSession(username: string, rememberMe: boolean, deviceInfo: any): UserSession {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const token = `token_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`;
    const expiresAt = new Date();
    
    if (rememberMe) {
      expiresAt.setDate(expiresAt.getDate() + 30); // 30天
    } else {
      expiresAt.setHours(expiresAt.getHours() + 8); // 8小时
    }

    return {
      sessionId,
      userId: username,
      token,
      expiresAt,
      deviceInfo: JSON.stringify(deviceInfo),
      isActive: true
    };
  }

  private getUserInfo(username: string): UserInfo {
    // 简化的用户信息获取
    return {
      id: `user_${username}`,
      username,
      email: `${username}@example.com`,
      displayName: username,
      roles: ['user'],
      permissions: ['read'],
      preferences: {},
      createdAt: new Date(),
      isActive: true
    };
  }

  private getFailureOutput(errorMessage: string): any {
    return {
      success: false,
      userInfo: null,
      token: '',
      sessionId: '',
      errorMessage,
      onSuccess: false,
      onFailure: true
    };
  }
}

/**
 * 用户注册节点
 * 批次2.1 - 用户服务节点
 */
export class UserRegistrationNode extends VisualScriptNode {
  public static readonly TYPE = 'UserRegistration';
  public static readonly NAME = '用户注册';
  public static readonly DESCRIPTION = '处理新用户注册和账户创建';

  constructor(nodeType: string = UserRegistrationNode.TYPE, name: string = UserRegistrationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('username', 'string', '用户名');
    this.addInput('email', 'string', '邮箱');
    this.addInput('password', 'string', '密码');
    this.addInput('confirmPassword', 'string', '确认密码');
    this.addInput('displayName', 'string', '显示名称');
    this.addInput('agreementAccepted', 'boolean', '同意协议');
    this.addInput('verificationCode', 'string', '验证码');

    // 输出端口
    this.addOutput('success', 'boolean', '注册成功');
    this.addOutput('userId', 'string', '用户ID');
    this.addOutput('userInfo', 'object', '用户信息');
    this.addOutput('needVerification', 'boolean', '需要验证');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('validationErrors', 'array', '验证错误');
    this.addOutput('onSuccess', 'trigger', '注册成功事件');
    this.addOutput('onFailure', 'trigger', '注册失败事件');
  }

  public execute(inputs?: any): any {
    try {
      const username = inputs?.username as string;
      const email = inputs?.email as string;
      const password = inputs?.password as string;
      const confirmPassword = inputs?.confirmPassword as string;
      const displayName = inputs?.displayName as string || username;
      const agreementAccepted = inputs?.agreementAccepted as boolean ?? false;
      const verificationCode = inputs?.verificationCode as string;

      // 验证输入
      const validationErrors = this.validateRegistrationInput({
        username, email, password, confirmPassword, agreementAccepted
      });

      if (validationErrors.length > 0) {
        return this.getFailureOutput('输入验证失败', validationErrors);
      }

      // 执行用户注册
      const result = this.registerUser({
        username, email, password, displayName, verificationCode
      });
      
      Debug.log('UserRegistrationNode', `用户注册${result.success ? '成功' : '失败'}: ${username}`);

      return result;
    } catch (error) {
      Debug.error('UserRegistrationNode', '用户注册执行失败', error);
      return this.getFailureOutput(error instanceof Error ? error.message : '注册失败', []);
    }
  }

  private validateRegistrationInput(data: any): string[] {
    const errors: string[] = [];

    if (!data.username || data.username.length < 3) {
      errors.push('用户名至少需要3个字符');
    }

    if (!data.email || !this.isValidEmail(data.email)) {
      errors.push('请输入有效的邮箱地址');
    }

    if (!data.password || data.password.length < 6) {
      errors.push('密码至少需要6个字符');
    }

    if (data.password !== data.confirmPassword) {
      errors.push('两次输入的密码不一致');
    }

    if (!data.agreementAccepted) {
      errors.push('请同意用户协议');
    }

    return errors;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private registerUser(data: any): any {
    // 简化的用户注册实现
    // 实际应该调用后端用户服务
    
    // 检查用户名是否已存在
    if (this.isUsernameExists(data.username)) {
      return this.getFailureOutput('用户名已存在', ['用户名已被使用']);
    }

    // 检查邮箱是否已存在
    if (this.isEmailExists(data.email)) {
      return this.getFailureOutput('邮箱已存在', ['邮箱已被注册']);
    }

    // 创建新用户
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const userInfo: UserInfo = {
      id: userId,
      username: data.username,
      email: data.email,
      displayName: data.displayName,
      roles: ['user'],
      permissions: ['read'],
      preferences: {},
      createdAt: new Date(),
      isActive: true
    };

    // 检查是否需要邮箱验证
    const needVerification = !data.verificationCode;

    return {
      success: true,
      userId,
      userInfo,
      needVerification,
      errorMessage: '',
      validationErrors: [],
      onSuccess: true,
      onFailure: false
    };
  }

  private isUsernameExists(username: string): boolean {
    // 简化的用户名检查
    // 实际应该查询数据库
    return username === 'admin' || username === 'test';
  }

  private isEmailExists(email: string): boolean {
    // 简化的邮箱检查
    // 实际应该查询数据库
    return email === '<EMAIL>';
  }

  private getFailureOutput(errorMessage: string, validationErrors: string[]): any {
    return {
      success: false,
      userId: '',
      userInfo: null,
      needVerification: false,
      errorMessage,
      validationErrors,
      onSuccess: false,
      onFailure: true
    };
  }
}

/**
 * 用户资料节点
 * 批次2.1 - 用户服务节点
 */
export class UserProfileNode extends VisualScriptNode {
  public static readonly TYPE = 'UserProfile';
  public static readonly NAME = '用户资料';
  public static readonly DESCRIPTION = '管理用户个人资料信息';

  constructor(nodeType: string = UserProfileNode.TYPE, name: string = UserProfileNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('userId', 'string', '用户ID');
    this.addInput('action', 'string', '操作类型');
    this.addInput('profileData', 'object', '资料数据');
    this.addInput('fieldName', 'string', '字段名称');
    this.addInput('fieldValue', 'any', '字段值');

    // 输出端口
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('userProfile', 'object', '用户资料');
    this.addOutput('updatedFields', 'array', '更新的字段');
    this.addOutput('errorMessage', 'string', '错误信息');
    this.addOutput('onUpdate', 'trigger', '更新事件');
  }

  public execute(inputs?: any): any {
    try {
      const userId = inputs?.userId as string;
      const action = inputs?.action as string || 'get';
      const profileData = inputs?.profileData || {};
      const fieldName = inputs?.fieldName as string;
      const fieldValue = inputs?.fieldValue;

      if (!userId) {
        Debug.warn('UserProfileNode', '用户ID为空');
        return this.getDefaultOutputs();
      }

      // 执行用户资料操作
      const result = this.handleProfileOperation(userId, action, profileData, fieldName, fieldValue);

      Debug.log('UserProfileNode', `用户资料操作完成: ${action} for ${userId}`);

      return result;
    } catch (error) {
      Debug.error('UserProfileNode', '用户资料操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private handleProfileOperation(userId: string, action: string, profileData: any, fieldName: string, fieldValue: any): any {
    switch (action) {
      case 'get':
        return this.getUserProfile(userId);
      case 'update':
        return this.updateUserProfile(userId, profileData);
      case 'setField':
        return this.setProfileField(userId, fieldName, fieldValue);
      case 'getField':
        return this.getProfileField(userId, fieldName);
      default:
        return {
          success: false,
          userProfile: null,
          updatedFields: [],
          errorMessage: `不支持的操作: ${action}`,
          onUpdate: false
        };
    }
  }

  private getUserProfile(userId: string): any {
    // 简化的用户资料获取
    const userProfile = {
      id: userId,
      username: `user_${userId}`,
      email: `${userId}@example.com`,
      displayName: `User ${userId}`,
      avatar: '',
      bio: '',
      location: '',
      website: '',
      birthDate: null,
      gender: '',
      phone: '',
      preferences: {
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        theme: 'light'
      },
      privacy: {
        showEmail: false,
        showPhone: false,
        showLocation: false
      },
      lastUpdated: new Date()
    };

    return {
      success: true,
      userProfile,
      updatedFields: [],
      errorMessage: '',
      onUpdate: false
    };
  }

  private updateUserProfile(userId: string, profileData: any): any {
    // 简化的用户资料更新
    const updatedFields: string[] = [];

    // 验证和更新字段
    const allowedFields = ['displayName', 'bio', 'location', 'website', 'birthDate', 'gender', 'phone', 'avatar'];

    for (const field of allowedFields) {
      if (field in profileData) {
        updatedFields.push(field);
      }
    }

    const userProfile = this.getUserProfile(userId).userProfile;

    // 合并更新数据
    Object.assign(userProfile, profileData);
    userProfile.lastUpdated = new Date();

    return {
      success: true,
      userProfile,
      updatedFields,
      errorMessage: '',
      onUpdate: true
    };
  }

  private setProfileField(userId: string, fieldName: string, fieldValue: any): any {
    if (!fieldName) {
      return {
        success: false,
        userProfile: null,
        updatedFields: [],
        errorMessage: '字段名称不能为空',
        onUpdate: false
      };
    }

    const userProfile = this.getUserProfile(userId).userProfile;
    userProfile[fieldName] = fieldValue;
    userProfile.lastUpdated = new Date();

    return {
      success: true,
      userProfile,
      updatedFields: [fieldName],
      errorMessage: '',
      onUpdate: true
    };
  }

  private getProfileField(userId: string, fieldName: string): any {
    const userProfile = this.getUserProfile(userId).userProfile;
    const fieldValue = userProfile[fieldName];

    return {
      success: true,
      userProfile: { [fieldName]: fieldValue },
      updatedFields: [],
      errorMessage: '',
      onUpdate: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      success: false,
      userProfile: null,
      updatedFields: [],
      errorMessage: '操作失败',
      onUpdate: false
    };
  }
}
