/**
 * 群体协调控制器
 * 
 * 提供群体协调服务的HTTP API接口
 */

import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  HttpStatus, 
  HttpException,
  Logger,
  UseGuards,
  UseInterceptors
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import type {  GroupCoordinationService, CoordinationTaskType, SocialRole  } from '../services/group-coordination.service';
import { 
  CreateCoordinationTaskDto, 
  UpdateTaskStatusDto, 
  CoordinationTaskResponseDto,
  ConflictReportDto,
  ResourceAllocationDto,
  GroupFormationDto,
  RoleAssignmentDto
} from '../dto/coordination.dto';

/**
 * 群体协调控制器
 */
@Controller('coordination')
export class CoordinationController {
  private readonly logger = new Logger(CoordinationController.name);

  constructor(
    private readonly coordinationService: GroupCoordinationService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  /**
   * 创建协调任务
   */
  @Post('tasks')
  async createTask(@Body() createTaskDto: CreateCoordinationTaskDto): Promise<{ taskId: string }> {
    try {
      this.logger.log(`创建协调任务: ${createTaskDto.type}`);
      
      const taskId = await this.coordinationService.createCoordinationTask(
        createTaskDto.type,
        createTaskDto.entityIds,
        createTaskDto.parameters,
        createTaskDto.priority || 1,
        createTaskDto.deadline
      );

      return { taskId };
    } catch (error) {
      this.logger.error('创建协调任务失败:', error);
      throw new HttpException('创建协调任务失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取任务状态
   */
  @Get('tasks/:taskId')
  async getTaskStatus(@Param('taskId') taskId: string): Promise<CoordinationTaskResponseDto> {
    try {
      const task = this.coordinationService.getTaskStatus(taskId);
      
      if (!task) {
        throw new HttpException('任务不存在', HttpStatus.NOT_FOUND);
      }

      return {
        id: task.id,
        type: task.type,
        status: task.status,
        entityIds: task.entityIds,
        parameters: task.parameters,
        priority: task.priority,
        createdAt: new Date(task.createdAt),
        updatedAt: new Date(task.updatedAt),
        deadline: task.deadline ? new Date(task.deadline) : undefined,
        result: task.result
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error('获取任务状态失败:', error);
      throw new HttpException('获取任务状态失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取所有任务列表
   */
  @Get('tasks')
  async getAllTasks(
    @Query('status') status?: string,
    @Query('type') type?: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number
  ): Promise<{ tasks: CoordinationTaskResponseDto[], total: number }> {
    try {
      // 这里应该实现分页和过滤逻辑
      // 暂时返回空数组，实际实现需要在service中添加相应方法
      return {
        tasks: [],
        total: 0
      };
    } catch (error) {
      this.logger.error('获取任务列表失败:', error);
      throw new HttpException('获取任务列表失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 群体形成
   */
  @Post('groups/formation')
  async createGroupFormation(@Body() formationDto: GroupFormationDto): Promise<{ taskId: string }> {
    try {
      this.logger.log(`创建群体形成任务: ${formationDto.strategy}`);
      
      const taskId = await this.coordinationService.createCoordinationTask(
        CoordinationTaskType.GROUP_FORMATION,
        formationDto.entityIds,
        {
          strategy: formationDto.strategy,
          maxDistance: formationDto.maxDistance,
          minGroupSize: formationDto.minGroupSize,
          maxGroupSize: formationDto.maxGroupSize
        },
        formationDto.priority || 2
      );

      return { taskId };
    } catch (error) {
      this.logger.error('创建群体形成任务失败:', error);
      throw new HttpException('创建群体形成任务失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 角色分配
   */
  @Post('roles/assignment')
  async assignRoles(@Body() assignmentDto: RoleAssignmentDto): Promise<{ taskId: string }> {
    try {
      this.logger.log(`创建角色分配任务: ${assignmentDto.groupId}`);
      
      const taskId = await this.coordinationService.createCoordinationTask(
        CoordinationTaskType.ROLE_ASSIGNMENT,
        assignmentDto.entityIds,
        {
          groupId: assignmentDto.groupId,
          requiredRoles: assignmentDto.requiredRoles,
          strategy: assignmentDto.strategy
        },
        assignmentDto.priority || 2
      );

      return { taskId };
    } catch (error) {
      this.logger.error('创建角色分配任务失败:', error);
      throw new HttpException('创建角色分配任务失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 冲突报告
   */
  @Post('conflicts/report')
  async reportConflict(@Body() conflictDto: ConflictReportDto): Promise<{ message: string }> {
    try {
      this.logger.log(`报告冲突: ${conflictDto.type}`);
      
      // 发布冲突事件
      this.eventEmitter.emit('coordination.conflict.reported', {
        id: `conflict_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        type: conflictDto.type,
        participants: conflictDto.participants,
        severity: conflictDto.severity,
        description: conflictDto.description,
        startTime: Date.now(),
        location: conflictDto.location,
        resolutionAttempts: 0
      });

      return { message: '冲突报告已提交' };
    } catch (error) {
      this.logger.error('报告冲突失败:', error);
      throw new HttpException('报告冲突失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 资源分配
   */
  @Post('resources/allocation')
  async allocateResources(@Body() allocationDto: ResourceAllocationDto): Promise<{ taskId: string }> {
    try {
      this.logger.log(`创建资源分配任务: ${allocationDto.resourceType}`);
      
      const taskId = await this.coordinationService.createCoordinationTask(
        CoordinationTaskType.RESOURCE_ALLOCATION,
        allocationDto.entityIds,
        {
          resourceType: allocationDto.resourceType,
          amount: allocationDto.amount,
          strategy: allocationDto.strategy || 'fair_allocation'
        },
        allocationDto.priority || 2
      );

      return { taskId };
    } catch (error) {
      this.logger.error('创建资源分配任务失败:', error);
      throw new HttpException('创建资源分配任务失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取活跃冲突
   */
  @Get('conflicts/active')
  async getActiveConflicts(): Promise<{ conflicts: any[] }> {
    try {
      const conflicts = this.coordinationService.getActiveConflicts();
      return { conflicts };
    } catch (error) {
      this.logger.error('获取活跃冲突失败:', error);
      throw new HttpException('获取活跃冲突失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取可用资源
   */
  @Get('resources/available')
  async getAvailableResources(@Query('type') type?: string): Promise<{ resources: any[] }> {
    try {
      let resources = this.coordinationService.getAvailableResources();
      
      if (type) {
        resources = resources.filter(r => r.type === type);
      }
      
      return { resources };
    } catch (error) {
      this.logger.error('获取可用资源失败:', error);
      throw new HttpException('获取可用资源失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 健康检查
   */
  @Get('health')
  async healthCheck(): Promise<{ status: string, timestamp: Date }> {
    return {
      status: 'ok',
      timestamp: new Date()
    };
  }
}
