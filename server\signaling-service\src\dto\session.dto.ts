import { IsString, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>ber, IsBoolean, IsArray, ValidateNested } from 'class-validator';
import type {  Type  } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PeerCapabilitiesDto {
  @ApiProperty({ description: '是否支持硬件加速' })
  @IsBoolean()
  hardwareAcceleration: boolean;

  @ApiProperty({ description: '是否支持SIMD' })
  @IsBoolean()
  simdSupport: boolean;

  @ApiProperty({ description: '是否支持WebCodecs' })
  @IsBoolean()
  webCodecsSupport: boolean;

  @ApiProperty({ description: '最大比特率' })
  @IsNumber()
  maxBitrate: number;

  @ApiProperty({ description: '首选编解码器列表' })
  @IsArray()
  @IsString({ each: true })
  preferredCodecs: string[];
}

export class CreateSessionDto {
  @ApiProperty({ description: '会话名称' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: '会话描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '最大参与者数量' })
  @IsOptional()
  @IsNumber()
  maxParticipants?: number;

  @ApiPropertyOptional({ description: '会话类型' })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({ description: '会话配置' })
  @IsOptional()
  config?: any;
}

export class JoinSessionDto {
  @ApiProperty({ description: '对等方ID' })
  @IsString()
  peerId: string;

  @ApiProperty({ description: '对等方能力', type: PeerCapabilitiesDto })
  @ValidateNested()
  @Type(() => PeerCapabilitiesDto)
  capabilities: PeerCapabilitiesDto;

  @ApiPropertyOptional({ description: '用户信息' })
  @IsOptional()
  userInfo?: any;
}
