# DL引擎开发者指南

## 🚀 快速开始

### 开发环境搭建

#### 1. 环境要求
- Node.js 18+
- npm 8+
- Git
- Docker (可选)
- VS Code (推荐)

#### 2. 项目克隆和安装

```bash
# 克隆项目
git clone <repository-url>
cd newsystem/server

# 安装依赖
npm install

# 安装所有服务依赖
npm run install:all
```

#### 3. 开发环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 启动基础设施
docker-compose up -d redis mysql

# 启动开发服务器
npm run dev
```

## 📁 项目结构

```
server/
├── shared/                 # 共享组件
│   ├── protocols/         # 通信协议
│   ├── errors/           # 错误处理
│   ├── logging/          # 日志系统
│   └── monitoring/       # 监控配置
├── api-gateway/          # API网关
├── user-service/         # 用户服务
├── ai-service/           # AI服务
├── ...                   # 其他微服务
├── docs/                 # 文档
└── scripts/              # 脚本工具
```

### 微服务标准结构

```
service-name/
├── src/
│   ├── main.ts           # 入口文件
│   ├── app.module.ts     # 根模块
│   ├── controllers/      # 控制器
│   ├── services/         # 业务逻辑
│   ├── entities/         # 数据实体
│   ├── dto/              # 数据传输对象
│   ├── common/           # 公共组件
│   │   ├── filters/      # 异常过滤器
│   │   ├── interceptors/ # 拦截器
│   │   ├── guards/       # 守卫
│   │   └── pipes/        # 管道
│   └── config/           # 配置文件
├── test/                 # 测试文件
├── Dockerfile           # Docker配置
├── docker-compose.yml   # 容器编排
├── package.json         # 项目配置
├── tsconfig.json        # TypeScript配置
├── .eslintrc.js         # ESLint配置
└── README.md            # 项目文档
```

## 🛠️ 开发规范

### 代码规范

#### 1. TypeScript规范
```typescript
// 使用接口定义数据结构
interface UserDto {
  id: number;
  name: string;
  email: string;
  createdAt: Date;
}

// 使用类型注解
async function createUser(userData: CreateUserDto): Promise<User> {
  // 实现逻辑
}

// 使用枚举定义常量
enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}
```

#### 2. NestJS规范
```typescript
// 控制器示例
@Controller('users')
@UseGuards(JwtAuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @ApiOperation({ summary: '获取用户列表' })
  @ApiResponse({ status: 200, description: '成功返回用户列表' })
  async findAll(@Query() query: FindUsersDto): Promise<ApiResponse<User[]>> {
    const users = await this.userService.findAll(query);
    return HttpProtocol.success(users, '获取用户列表成功');
  }

  @Post()
  @ApiOperation({ summary: '创建用户' })
  async create(@Body() createUserDto: CreateUserDto): Promise<ApiResponse<User>> {
    const user = await this.userService.create(createUserDto);
    return HttpProtocol.success(user, '用户创建成功');
  }
}
```

#### 3. 服务层规范
```typescript
@Injectable()
export class UserService {
  private readonly logger = createLogger('UserService');

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async findAll(query: FindUsersDto): Promise<User[]> {
    try {
      this.logger.info('查询用户列表', { query });

      const queryBuilder = this.userRepository.createQueryBuilder('user');

      if (query.name) {
        queryBuilder.andWhere('user.name LIKE :name', { name: `%${query.name}%` });
      }

      const users = await queryBuilder.getMany();

      this.logger.info('用户列表查询成功', { count: users.length });
      return users;

    } catch (error) {
      this.logger.error('用户列表查询失败', error);
      throw new AppError(ErrorCode.DATABASE_ERROR, '查询用户列表失败');
    }
  }
}
```

### API设计规范

#### 1. RESTful API设计
```
GET    /api/users          # 获取用户列表
GET    /api/users/:id      # 获取单个用户
POST   /api/users          # 创建用户
PUT    /api/users/:id      # 更新用户
DELETE /api/users/:id      # 删除用户
```

#### 2. 响应格式标准
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "张三",
    "email": "<EMAIL>"
  },
  "message": "操作成功",
  "timestamp": "2025-07-02T13:47:09.508Z",
  "requestId": "req_1719928029508_abc123"
}
```

#### 3. 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入数据验证失败",
    "details": {
      "email": "邮箱格式不正确"
    }
  },
  "timestamp": "2025-07-02T13:47:09.508Z",
  "requestId": "req_1719928029508_abc123"
}
```

## 🧪 测试指南

### 单元测试

```typescript
// user.service.spec.ts
describe('UserService', () => {
  let service: UserService;
  let repository: Repository<User>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getRepositoryToken(User),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  it('should create a user', async () => {
    const createUserDto = { name: '张三', email: '<EMAIL>' };
    const expectedUser = { id: 1, ...createUserDto };

    jest.spyOn(repository, 'save').mockResolvedValue(expectedUser as User);

    const result = await service.create(createUserDto);
    expect(result).toEqual(expectedUser);
  });
});
```

### 集成测试

```typescript
// user.controller.e2e-spec.ts
describe('UserController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/users (GET)', () => {
    return request(app.getHttpServer())
      .get('/users')
      .expect(200)
      .expect((res) => {
        expect(res.body.success).toBe(true);
        expect(Array.isArray(res.body.data)).toBe(true);
      });
  });
});
```

## 🔧 调试指南

### VS Code调试配置

```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug NestJS",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/src/main.ts",
      "outFiles": ["${workspaceFolder}/dist/**/*.js"],
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal"
    }
  ]
}
```

### 日志调试

```typescript
// 使用统一的日志系统
const logger = createLogger('ServiceName');

logger.debug('调试信息', { userId: 123, action: 'create' });
logger.info('操作信息', { result: 'success' });
logger.warn('警告信息', { warning: 'deprecated API' });
logger.error('错误信息', error, { context: 'additional info' });
```

## 📦 依赖管理

### 添加新依赖

```bash
# 添加生产依赖
npm install package-name

# 添加开发依赖
npm install -D package-name

# 更新依赖
npm update

# 检查过期依赖
npm outdated
```

### 依赖版本管理

```json
// package.json
{
  "dependencies": {
    "@nestjs/core": "^10.0.0",
    "typeorm": "~0.3.0",
    "mysql2": "3.6.0"
  }
}
```

## 🚀 部署流程

### 本地构建测试

```bash
# 构建项目
npm run build

# 运行测试
npm run test

# 代码检查
npm run lint

# 格式化代码
npm run format
```

### Docker构建

```bash
# 构建镜像
docker build -t service-name .

# 运行容器
docker run -p 3000:3000 service-name
```

## 📝 提交规范

### Git提交信息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型说明
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

#### 示例
```
feat(user): 添加用户注册功能

- 实现用户注册API
- 添加邮箱验证
- 完善用户数据验证

Closes #123
```

## 🔍 代码审查

### 审查清单
- [ ] 代码符合项目规范
- [ ] 有适当的错误处理
- [ ] 包含必要的测试
- [ ] API文档已更新
- [ ] 性能考虑合理
- [ ] 安全性检查通过

### 性能优化建议
- 使用数据库索引
- 实现缓存策略
- 避免N+1查询
- 使用连接池
- 合理的分页设计
