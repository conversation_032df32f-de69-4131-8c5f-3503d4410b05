import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { QualityController } from './quality.controller';
import { QualityService } from './quality.service';
import { QualityInspection, QualityControlPlan } from './entities/quality-inspection.entity';

/**
 * 质量管理模块
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([QualityInspection, QualityControlPlan]),
  ],
  controllers: [QualityController],
  providers: [QualityService],
  exports: [QualityService],
})
export class QualityModule {}
