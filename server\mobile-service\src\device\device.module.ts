/**
 * 设备管理模块
 * 
 * 整合移动设备管理相关的功能
 */

import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';

// 控制器
import { DeviceController } from './device.controller';

// 服务
import { DeviceService } from './device.service';

// 实体
import { MobileDevice } from './entities/mobile-device.entity';
import { DeviceSession } from './entities/device-session.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      MobileDevice,
      DeviceSession,
    ]),
  ],
  controllers: [DeviceController],
  providers: [DeviceService],
  exports: [DeviceService],
})
export class DeviceModule {}
