{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "esModuleInterop": true, "resolveJsonModule": true, "paths": {"@/*": ["src/*"], "@shared/*": ["../shared/*"], "@engine/*": ["../../engine/src/*"]}, "strict": true, "strictFunctionTypes": true, "noImplicitReturns": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"], "extends": "../tsconfig.base.json"}