import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import type {  RelationType  } from '../knowledge-graph.service';
import { KnowledgeEntityModel } from './knowledge-entity.entity';

@Entity('knowledge_relations')
@Index(['type'])
@Index(['sourceId', 'targetId'])
@Index(['weight'])
@Index(['confidence'])
export class KnowledgeRelationModel {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: RelationType,
    comment: '关系类型',
  })
  @Index()
  type: RelationType;

  @Column({
    type: 'uuid',
    comment: '源实体ID',
  })
  sourceId: string;

  @Column({
    type: 'uuid',
    comment: '目标实体ID',
  })
  targetId: string;

  @ManyToOne(() => KnowledgeEntityModel)
  @JoinColumn({ name: 'sourceId' })
  sourceEntity: KnowledgeEntityModel;

  @ManyToOne(() => KnowledgeEntityModel)
  @JoinColumn({ name: 'targetId' })
  targetEntity: KnowledgeEntityModel;

  @Column({
    type: 'json',
    nullable: true,
    comment: '关系属性',
  })
  properties: Record<string, any>;

  @Column({
    type: 'decimal',
    precision: 3,
    scale: 2,
    default: 1.0,
    comment: '关系权重',
  })
  weight: number;

  @Column({
    type: 'decimal',
    precision: 3,
    scale: 2,
    default: 1.0,
    comment: '置信度',
  })
  confidence: number;

  @Column({
    type: 'varchar',
    length: 100,
    comment: '数据来源',
  })
  source: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Neo4j关系ID',
  })
  neo4jId: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '描述信息',
  })
  description: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: '元数据',
  })
  metadata: Record<string, any>;

  @Column({
    type: 'boolean',
    default: true,
    comment: '是否激活',
  })
  isActive: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;
}
