/**
 * 群体协调服务数据传输对象
 */

import { IsString, IsArray, IsNumber, IsOptional, IsEnum, IsObject, IsDate, ValidateNested, IsBoolean } from 'class-validator';
import type {  Type, Transform  } from 'class-transformer';
import type {  CoordinationTaskType, SocialRole  } from '../services/group-coordination.service';

/**
 * 位置信息DTO
 */
export class PositionDto {
  @IsNumber()
  x: number;

  @IsNumber()
  y: number;

  @IsNumber()
  z: number;
}

/**
 * 创建协调任务DTO
 */
export class CreateCoordinationTaskDto {
  @IsEnum(CoordinationTaskType)
  type: CoordinationTaskType;

  @IsArray()
  @IsString({ each: true })
  entityIds: string[];

  @IsObject()
  parameters: { [key: string]: any };

  @IsOptional()
  @IsNumber()
  priority?: number;

  @IsOptional()
  @IsNumber()
  deadline?: number;
}

/**
 * 更新任务状态DTO
 */
export class UpdateTaskStatusDto {
  @IsEnum(['pending', 'processing', 'completed', 'failed'])
  status: 'pending' | 'processing' | 'completed' | 'failed';

  @IsOptional()
  @IsObject()
  result?: any;
}

/**
 * 协调任务响应DTO
 */
export class CoordinationTaskResponseDto {
  @IsString()
  id: string;

  @IsEnum(CoordinationTaskType)
  type: CoordinationTaskType;

  @IsEnum(['pending', 'processing', 'completed', 'failed'])
  status: 'pending' | 'processing' | 'completed' | 'failed';

  @IsArray()
  @IsString({ each: true })
  entityIds: string[];

  @IsObject()
  parameters: { [key: string]: any };

  @IsNumber()
  priority: number;

  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @IsDate()
  @Type(() => Date)
  updatedAt: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  deadline?: Date;

  @IsOptional()
  @IsObject()
  result?: any;
}

/**
 * 群体形成DTO
 */
export class GroupFormationDto {
  @IsArray()
  @IsString({ each: true })
  entityIds: string[];

  @IsString()
  strategy: string; // 'proximity_grouping', 'skill_based', 'random'

  @IsOptional()
  @IsNumber()
  maxDistance?: number;

  @IsOptional()
  @IsNumber()
  minGroupSize?: number;

  @IsOptional()
  @IsNumber()
  maxGroupSize?: number;

  @IsOptional()
  @IsNumber()
  priority?: number;
}

/**
 * 角色分配DTO
 */
export class RoleAssignmentDto {
  @IsArray()
  @IsString({ each: true })
  entityIds: string[];

  @IsOptional()
  @IsString()
  groupId?: string;

  @IsArray()
  @IsEnum(SocialRole, { each: true })
  requiredRoles: SocialRole[];

  @IsOptional()
  @IsString()
  strategy?: string; // 'capability_based', 'random', 'rotation'

  @IsOptional()
  @IsNumber()
  priority?: number;
}

/**
 * 冲突报告DTO
 */
export class ConflictReportDto {
  @IsString()
  type: string;

  @IsArray()
  @IsString({ each: true })
  participants: string[];

  @IsNumber()
  severity: number; // 0-1之间

  @IsString()
  description: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => PositionDto)
  location?: PositionDto;
}

/**
 * 资源分配DTO
 */
export class ResourceAllocationDto {
  @IsArray()
  @IsString({ each: true })
  entityIds: string[];

  @IsString()
  resourceType: string;

  @IsNumber()
  amount: number;

  @IsOptional()
  @IsString()
  strategy?: string; // 'fair_allocation', 'priority_based', 'need_based'

  @IsOptional()
  @IsNumber()
  priority?: number;
}

/**
 * 群体信息DTO
 */
export class GroupInfoDto {
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsArray()
  @IsString({ each: true })
  members: string[];

  @IsString()
  leader: string;

  @IsString()
  type: string;

  @IsNumber()
  cohesion: number;

  @IsNumber()
  productivity: number;

  @IsNumber()
  conflictLevel: number;

  @IsString()
  communicationPattern: string;

  @IsString()
  decisionMakingStyle: string;

  @IsArray()
  @IsString({ each: true })
  norms: string[];
}

/**
 * 社交实体详情DTO
 */
export class SocialEntityDetailDto {
  @IsString()
  id: string;

  @ValidateNested()
  @Type(() => PositionDto)
  position: PositionDto;

  @IsOptional()
  @IsObject()
  socialSkills?: {
    leadership?: number;
    conflictResolution?: number;
  };

  @IsOptional()
  @IsNumber()
  trustLevel?: number;
}

/**
 * 冲突信息DTO
 */
export class ConflictInfoDto {
  @IsString()
  id: string;

  @IsString()
  type: string;

  @IsArray()
  @IsString({ each: true })
  participants: string[];

  @IsNumber()
  severity: number;

  @IsString()
  description: string;

  @IsNumber()
  startTime: number;

  @IsOptional()
  @ValidateNested()
  @Type(() => PositionDto)
  location?: PositionDto;

  @IsNumber()
  resolutionAttempts: number;
}

/**
 * 资源信息DTO
 */
export class ResourceInfoDto {
  @IsString()
  id: string;

  @IsString()
  type: string;

  @IsNumber()
  capacity: number;

  @IsNumber()
  currentUsage: number;

  @ValidateNested()
  @Type(() => PositionDto)
  location: PositionDto;

  @IsArray()
  @IsString({ each: true })
  accessRequirements: string[];

  @IsNumber()
  priority: number;
}

/**
 * 协调结果DTO
 */
export class CoordinationResultDto {
  @IsString()
  taskId: string;

  @IsBoolean()
  success: boolean;

  @IsObject()
  result: any;

  @IsArray()
  @IsString({ each: true })
  affectedEntities: string[];

  @IsNumber()
  executionTime: number;

  @IsArray()
  @IsString({ each: true })
  recommendations: string[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateCoordinationTaskDto)
  followUpTasks: CreateCoordinationTaskDto[];
}
