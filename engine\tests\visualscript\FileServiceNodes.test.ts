/**
 * 文件服务节点测试
 * 批次2.1 - 服务器集成节点测试
 */
import {
  FileUploadNode,
  FileDownloadNode,
  FileStorageNode
} from '../../src/visual-script/nodes/file/FileServiceNodes';

import {
  FileCompressionNode,
  FileEncryptionNode,
  FileVersioningNode
} from '../../src/visual-script/nodes/file/FileServiceNodes2';

import {
  FileMetadataNode,
  FileSearchNode,
  FileSyncNode,
  FileAnalyticsNode
} from '../../src/visual-script/nodes/file/FileServiceNodes3';

describe('文件服务节点测试', () => {
  describe('FileUploadNode', () => {
    let node: FileUploadNode;

    beforeEach(() => {
      node = new FileUploadNode();
    });

    it('应该正确创建文件上传节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('FileUpload');
      expect(node.name).toBe('文件上传');
    });

    it('应该有正确的输入端口', () => {
      const inputs = node.getInputs();
      expect(inputs).toHaveLength(5);
      expect(inputs.map(input => input.name)).toEqual([
        'file', 'config', 'destination', 'filename', 'metadata'
      ]);
    });

    it('应该能够上传文件', () => {
      const file = {
        name: 'test.txt',
        size: 1024,
        type: 'text/plain'
      };
      const destination = '/uploads';
      
      const result = node.execute({ file, destination });
      
      expect(result.success).toBe(true);
      expect(result.fileInfo).toBeDefined();
      expect(result.url).toBeDefined();
      expect(result.path).toBeDefined();
      expect(result.size).toBe(file.size);
    });

    it('应该处理空文件', () => {
      const result = node.execute({});
      
      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('文件对象不能为空');
    });
  });

  describe('FileDownloadNode', () => {
    let node: FileDownloadNode;

    beforeEach(() => {
      node = new FileDownloadNode();
    });

    it('应该正确创建文件下载节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('FileDownload');
      expect(node.name).toBe('文件下载');
    });

    it('应该能够下载文件', () => {
      const fileId = 'test-file-123';
      
      const result = node.execute({ fileId });
      
      expect(result.success).toBe(true);
      expect(result.fileData).toBeDefined();
      expect(result.fileInfo).toBeDefined();
      expect(result.downloadUrl).toBeDefined();
    });

    it('应该处理无文件标识', () => {
      const result = node.execute({});
      
      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('必须提供文件ID、路径或URL');
    });
  });

  describe('FileStorageNode', () => {
    let node: FileStorageNode;

    beforeEach(() => {
      node = new FileStorageNode();
    });

    it('应该正确创建文件存储节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('FileStorage');
      expect(node.name).toBe('文件存储');
    });

    it('应该能够存储文件', () => {
      const fileId = 'test-file-123';
      const storageType = 'local';
      const bucket = 'test-bucket';
      
      const result = node.execute({ action: 'store', fileId, storageType, bucket });
      
      expect(result.success).toBe(true);
      expect(result.storageInfo).toBeDefined();
      expect(result.url).toBeDefined();
      expect(result.location).toBeDefined();
    });
  });

  describe('FileCompressionNode', () => {
    let node: FileCompressionNode;

    beforeEach(() => {
      node = new FileCompressionNode();
    });

    it('应该正确创建文件压缩节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('FileCompression');
      expect(node.name).toBe('文件压缩');
    });

    it('应该能够压缩文件', () => {
      const files = [
        { name: 'file1.txt', size: 1024 },
        { name: 'file2.txt', size: 2048 }
      ];
      const archiveName = 'test.zip';
      
      const result = node.execute({ action: 'compress', files, archiveName });
      
      expect(result.success).toBe(true);
      expect(result.archiveInfo).toBeDefined();
      expect(result.compressionRatio).toBeGreaterThan(0);
      expect(result.originalSize).toBeGreaterThan(0);
    });

    it('应该能够解压文件', () => {
      const archiveName = 'test.zip';
      
      const result = node.execute({ action: 'decompress', archiveName });
      
      expect(result.success).toBe(true);
      expect(Array.isArray(result.extractedFiles)).toBe(true);
    });
  });

  describe('FileEncryptionNode', () => {
    let node: FileEncryptionNode;

    beforeEach(() => {
      node = new FileEncryptionNode();
    });

    it('应该正确创建文件加密节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('FileEncryption');
      expect(node.name).toBe('文件加密');
    });

    it('应该能够加密文件', () => {
      const fileId = 'test-file-123';
      const key = 'encryption-key';
      
      const result = node.execute({ action: 'encrypt', fileId, key });
      
      expect(result.success).toBe(true);
      expect(result.encryptedFileId).toBeDefined();
      expect(result.encryptionInfo).toBeDefined();
      expect(result.keyInfo).toBeDefined();
    });

    it('应该能够解密文件', () => {
      const fileId = 'test-file-123_encrypted';
      const key = 'encryption-key';
      
      const result = node.execute({ action: 'decrypt', fileId, key });
      
      expect(result.success).toBe(true);
      expect(result.decryptedFileId).toBeDefined();
    });
  });

  describe('FileVersioningNode', () => {
    let node: FileVersioningNode;

    beforeEach(() => {
      node = new FileVersioningNode();
    });

    it('应该正确创建文件版本控制节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('FileVersioning');
      expect(node.name).toBe('文件版本控制');
    });

    it('应该能够创建版本', () => {
      const fileId = 'test-file-123';
      const comment = '新版本';
      const author = 'test-user';
      
      const result = node.execute({ action: 'create', fileId, comment, author });
      
      expect(result.success).toBe(true);
      expect(result.versionInfo).toBeDefined();
      expect(result.currentVersion).toBeDefined();
    });

    it('应该能够列出版本', () => {
      const fileId = 'test-file-123';
      
      const result = node.execute({ action: 'list', fileId });
      
      expect(result.success).toBe(true);
      expect(Array.isArray(result.versions)).toBe(true);
    });
  });

  describe('FileMetadataNode', () => {
    let node: FileMetadataNode;

    beforeEach(() => {
      node = new FileMetadataNode();
    });

    it('应该正确创建文件元数据节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('FileMetadata');
      expect(node.name).toBe('文件元数据');
    });

    it('应该能够获取元数据', () => {
      const fileId = 'test-file-123';
      
      const result = node.execute({ action: 'get', fileId });
      
      expect(result.success).toBe(true);
      expect(result.metadata).toBeDefined();
      expect(Array.isArray(result.tags)).toBe(true);
      expect(result.properties).toBeDefined();
    });

    it('应该能够设置元数据', () => {
      const fileId = 'test-file-123';
      const metadata = { title: '测试文档', author: '作者' };
      const tags = ['重要', '文档'];
      
      const result = node.execute({ action: 'set', fileId, metadata, tags });
      
      expect(result.success).toBe(true);
      expect(result.metadata).toEqual(expect.objectContaining(metadata));
    });
  });

  describe('FileSearchNode', () => {
    let node: FileSearchNode;

    beforeEach(() => {
      node = new FileSearchNode();
    });

    it('应该正确创建文件搜索节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('FileSearch');
      expect(node.name).toBe('文件搜索');
    });

    it('应该能够搜索文件', () => {
      const query = '测试文档';
      
      const result = node.execute({ query });
      
      expect(result.success).toBe(true);
      expect(Array.isArray(result.results)).toBe(true);
      expect(typeof result.totalCount).toBe('number');
      expect(typeof result.searchTime).toBe('number');
    });
  });

  describe('FileSyncNode', () => {
    let node: FileSyncNode;

    beforeEach(() => {
      node = new FileSyncNode();
    });

    it('应该正确创建文件同步节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('FileSync');
      expect(node.name).toBe('文件同步');
    });

    it('应该能够同步文件', () => {
      const source = '/source/path';
      const destination = '/dest/path';
      
      const result = node.execute({ action: 'sync', source, destination });
      
      expect(result.success).toBe(true);
      expect(result.syncId).toBeDefined();
      expect(Array.isArray(result.syncedFiles)).toBe(true);
      expect(result.statistics).toBeDefined();
    });
  });

  describe('FileAnalyticsNode', () => {
    let node: FileAnalyticsNode;

    beforeEach(() => {
      node = new FileAnalyticsNode();
    });

    it('应该正确创建文件分析节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('FileAnalytics');
      expect(node.name).toBe('文件分析');
    });

    it('应该能够分析文件使用情况', () => {
      const fileIds = ['file1', 'file2', 'file3'];
      const metrics = ['access_count', 'download_count'];
      
      const result = node.execute({ analysisType: 'usage', fileIds, metrics });
      
      expect(result.success).toBe(true);
      expect(result.analytics).toBeDefined();
      expect(Array.isArray(result.insights)).toBe(true);
      expect(Array.isArray(result.recommendations)).toBe(true);
    });

    it('应该能够分析存储情况', () => {
      const result = node.execute({ analysisType: 'storage' });
      
      expect(result.success).toBe(true);
      expect(result.analytics).toBeDefined();
      expect(Array.isArray(result.insights)).toBe(true);
    });
  });
});
