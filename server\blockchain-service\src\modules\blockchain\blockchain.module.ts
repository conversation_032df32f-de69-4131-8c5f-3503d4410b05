/**
 * 区块链模块
 */

import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { BlockchainService } from './blockchain.service';
import { BlockchainController } from './blockchain.controller';

// 新增服务
import { SmartContractService } from './services/smart-contract.service';
import { DigitalAssetService } from './services/digital-asset.service';
import { ConsensusService } from './services/consensus.service';
import { DeFiService } from './services/defi.service';
import { Web3Service } from './services/web3.service';

// 实体
import { SmartContract } from '../../entities/smart-contract.entity';
import { Transaction } from '../../entities/transaction.entity';
import { BlockchainAsset } from '../../entities/blockchain-asset.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([SmartContract, Transaction, BlockchainAsset]),
    BullModule.registerQueue({
      name: 'blockchain',
    }),
    BullModule.registerQueue({
      name: 'consensus',
    }),
    BullModule.registerQueue({
      name: 'defi',
    }),
  ],
  controllers: [BlockchainController],
  providers: [
    BlockchainService,
    SmartContractService,
    DigitalAssetService,
    ConsensusService,
    DeFiService,
    Web3Service,
  ],
  exports: [
    BlockchainService,
    SmartContractService,
    DigitalAssetService,
    ConsensusService,
    DeFiService,
    Web3Service,
  ],
})
export class BlockchainModule {}
