/**
 * 资源管理节点验证脚本
 * 验证批次1.3节点的基本功能
 */

// 导入资源加载节点
import {
  LoadAssetNode,
  UnloadAssetNode,
  PreloadAssetNode,
  AsyncLoadAssetNode,
  LoadAssetBundleNode,
  AssetDependencyNode,
  AssetCacheNode,
  AssetCompressionNode,
  AssetEncryptionNode,
  AssetValidationNode,
  AssetMetadataNode,
  AssetVersionNode
} from '../nodes/resources/ResourceManagementNodes';

// 导入资源优化节点
import { AssetOptimizationNode } from '../nodes/resources/ResourceManagementNodes';
import {
  TextureCompressionNode,
  MeshOptimizationNode,
  AudioCompressionNode,
  AssetBatchingNode,
  AssetStreamingNode,
  AssetMemoryManagementNode,
  AssetGarbageCollectionNode,
  AssetPerformanceMonitorNode,
  AssetUsageAnalyticsNode
} from '../nodes/resources/ResourceOptimizationNodes';

/**
 * 验证结果接口
 */
interface ValidationResult {
  nodeName: string;
  success: boolean;
  error?: string;
  details?: any;
}

/**
 * 节点验证器
 */
class NodeValidator {
  private results: ValidationResult[] = [];

  /**
   * 验证单个节点
   */
  async validateNode(nodeClass: any, nodeName: string, testInputs: any): Promise<ValidationResult> {
    try {
      console.log(`验证节点: ${nodeName}`);
      
      // 创建节点实例
      const node = new nodeClass();
      
      // 验证节点基本属性
      if (!node.TYPE || !node.NAME || !node.DESCRIPTION) {
        throw new Error('节点缺少必要的静态属性');
      }
      
      // 执行节点
      const result = await node.execute(testInputs);
      
      // 验证输出
      if (!result || typeof result !== 'object') {
        throw new Error('节点输出格式无效');
      }
      
      console.log(`✓ ${nodeName} 验证成功`);
      
      return {
        nodeName,
        success: true,
        details: {
          type: node.TYPE,
          name: node.NAME,
          description: node.DESCRIPTION,
          outputKeys: Object.keys(result)
        }
      };
      
    } catch (error) {
      console.error(`✗ ${nodeName} 验证失败:`, error.message);
      
      return {
        nodeName,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 验证所有资源加载节点
   */
  async validateResourceLoadingNodes(): Promise<void> {
    console.log('\n=== 验证资源加载节点 ===');
    
    // LoadAssetNode
    this.results.push(await this.validateNode(LoadAssetNode, 'LoadAssetNode', {
      execute: true,
      resourceId: 'test_texture',
      url: 'https://example.com/texture.jpg',
      type: 'texture',
      priority: 50
    }));

    // UnloadAssetNode
    this.results.push(await this.validateNode(UnloadAssetNode, 'UnloadAssetNode', {
      execute: true,
      resourceId: 'test_texture',
      force: false
    }));

    // PreloadAssetNode
    this.results.push(await this.validateNode(PreloadAssetNode, 'PreloadAssetNode', {
      execute: true,
      urls: ['texture1.jpg', 'texture2.jpg'],
      types: ['texture', 'texture'],
      priority: 30
    }));

    // AsyncLoadAssetNode
    this.results.push(await this.validateNode(AsyncLoadAssetNode, 'AsyncLoadAssetNode', {
      execute: true,
      resourceId: 'async_texture',
      url: 'https://example.com/texture.jpg',
      type: 'texture'
    }));

    // LoadAssetBundleNode
    this.results.push(await this.validateNode(LoadAssetBundleNode, 'LoadAssetBundleNode', {
      execute: true,
      bundleId: 'test_bundle',
      manifest: {
        assets: [
          { id: 'tex1', name: 'texture1', type: 'texture', url: 'tex1.jpg' }
        ]
      }
    }));

    // AssetDependencyNode
    this.results.push(await this.validateNode(AssetDependencyNode, 'AssetDependencyNode', {
      execute: true,
      resourceId: 'main_model',
      dependencies: ['texture1', 'texture2'],
      autoLoad: true
    }));

    // AssetCacheNode
    this.results.push(await this.validateNode(AssetCacheNode, 'AssetCacheNode', {
      execute: true,
      operation: 'info'
    }));

    // AssetCompressionNode
    this.results.push(await this.validateNode(AssetCompressionNode, 'AssetCompressionNode', {
      execute: true,
      operation: 'compress',
      data: { test: 'data' },
      compressionLevel: 6
    }));

    // AssetEncryptionNode
    this.results.push(await this.validateNode(AssetEncryptionNode, 'AssetEncryptionNode', {
      execute: true,
      operation: 'encrypt',
      data: { sensitive: 'data' },
      key: 'test_key'
    }));

    // AssetValidationNode
    this.results.push(await this.validateNode(AssetValidationNode, 'AssetValidationNode', {
      execute: true,
      resourceId: 'test_resource',
      data: { type: 'texture', width: 1024 },
      schema: { type: 'object', required: ['type'] }
    }));

    // AssetMetadataNode
    this.results.push(await this.validateNode(AssetMetadataNode, 'AssetMetadataNode', {
      execute: true,
      operation: 'get',
      resourceId: 'test_resource'
    }));

    // AssetVersionNode
    this.results.push(await this.validateNode(AssetVersionNode, 'AssetVersionNode', {
      execute: true,
      operation: 'get',
      resourceId: 'test_resource'
    }));
  }

  /**
   * 验证所有资源优化节点
   */
  async validateResourceOptimizationNodes(): Promise<void> {
    console.log('\n=== 验证资源优化节点 ===');
    
    // AssetOptimizationNode
    this.results.push(await this.validateNode(AssetOptimizationNode, 'AssetOptimizationNode', {
      execute: true,
      resourceId: 'test_texture',
      optimizationType: 'auto',
      quality: 0.8
    }));

    // TextureCompressionNode
    this.results.push(await this.validateNode(TextureCompressionNode, 'TextureCompressionNode', {
      execute: true,
      texture: { width: 1024, height: 1024, channels: 4 },
      format: 'DXT5',
      quality: 0.8
    }));

    // MeshOptimizationNode
    this.results.push(await this.validateNode(MeshOptimizationNode, 'MeshOptimizationNode', {
      execute: true,
      mesh: { vertexCount: 10000 },
      optimizationType: 'simplify',
      targetVertexCount: 5000
    }));

    // AudioCompressionNode
    this.results.push(await this.validateNode(AudioCompressionNode, 'AudioCompressionNode', {
      execute: true,
      audio: { duration: 60, sampleRate: 44100, channels: 2 },
      format: 'mp3',
      bitrate: 128
    }));

    // AssetBatchingNode
    this.results.push(await this.validateNode(AssetBatchingNode, 'AssetBatchingNode', {
      execute: true,
      resources: [{ id: 'res1', size: 1024 }],
      operation: 'optimize',
      batchSize: 1
    }));

    // AssetStreamingNode
    this.results.push(await this.validateNode(AssetStreamingNode, 'AssetStreamingNode', {
      execute: true,
      resourceUrl: 'https://example.com/large_file.bin',
      chunkSize: 64 * 1024,
      autoStart: false
    }));

    // AssetMemoryManagementNode
    this.results.push(await this.validateNode(AssetMemoryManagementNode, 'AssetMemoryManagementNode', {
      execute: true,
      operation: 'status'
    }));

    // AssetGarbageCollectionNode
    this.results.push(await this.validateNode(AssetGarbageCollectionNode, 'AssetGarbageCollectionNode', {
      execute: true,
      mode: 'auto',
      threshold: 0.8
    }));

    // AssetPerformanceMonitorNode
    this.results.push(await this.validateNode(AssetPerformanceMonitorNode, 'AssetPerformanceMonitorNode', {
      execute: true,
      operation: 'get',
      resourceId: 'test_resource'
    }));

    // AssetUsageAnalyticsNode
    this.results.push(await this.validateNode(AssetUsageAnalyticsNode, 'AssetUsageAnalyticsNode', {
      execute: true,
      analysisType: 'usage',
      timeRange: '24h'
    }));
  }

  /**
   * 生成验证报告
   */
  generateReport(): void {
    console.log('\n=== 验证报告 ===');
    
    const totalNodes = this.results.length;
    const successfulNodes = this.results.filter(r => r.success).length;
    const failedNodes = this.results.filter(r => !r.success).length;
    
    console.log(`总节点数: ${totalNodes}`);
    console.log(`成功验证: ${successfulNodes}`);
    console.log(`验证失败: ${failedNodes}`);
    console.log(`成功率: ${((successfulNodes / totalNodes) * 100).toFixed(1)}%`);
    
    if (failedNodes > 0) {
      console.log('\n失败的节点:');
      this.results.filter(r => !r.success).forEach(result => {
        console.log(`- ${result.nodeName}: ${result.error}`);
      });
    }
    
    console.log('\n成功验证的节点:');
    this.results.filter(r => r.success).forEach(result => {
      console.log(`✓ ${result.nodeName} (${result.details?.type})`);
    });
  }

  /**
   * 运行所有验证
   */
  async runAllValidations(): Promise<void> {
    console.log('开始验证DL引擎批次1.3资源管理节点...\n');
    
    await this.validateResourceLoadingNodes();
    await this.validateResourceOptimizationNodes();
    
    this.generateReport();
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const validator = new NodeValidator();
  await validator.runAllValidations();
}

// 如果直接运行此文件，则执行验证
if (require.main === module) {
  main().catch(console.error);
}

export { NodeValidator };
