import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';
import { Interval } from '@nestjs/schedule';
import { v4 as uuidv4 } from 'uuid';
import type { 
  CheatDetection,
  CheatType,
  SeverityLevel,
  ActionType,
  PlayerBehaviorPattern,
  PlayerAction,
  BehaviorStatistics,
  BehaviorAnomaly,
  AntiCheatConfig,
  ValidationResult,
  NetworkAnalysis,
  CheatEvidence
 } from './anti-cheat.interface';

@Injectable()
export class AntiCheatService {
  private readonly logger = new Logger(AntiCheatService.name);
  private readonly playerPatterns = new Map<string, PlayerBehaviorPattern>();
  private readonly detections = new Map<string, CheatDetection[]>();
  private readonly networkAnalysis = new Map<string, NetworkAnalysis>();
  private readonly bannedPlayers = new Set<string>();
  
  private readonly config: AntiCheatConfig = {
    enabled: true,
    strictMode: false,
    detectionThresholds: {
      speedHack: {
        maxSpeed: 20, // 单位/秒
        accelerationLimit: 50 // 单位/秒²
      },
      teleport: {
        maxDistance: 100, // 单位
        timeWindow: 1000 // 毫秒
      },
      aimBot: {
        maxAccuracy: 0.95, // 95%命中率
        minReactionTime: 100 // 毫秒
      },
      packetManipulation: {
        maxPacketLoss: 0.1, // 10%
        maxLatencyVariance: 500 // 毫秒
      }
    },
    actions: {
      [CheatType.SPEED_HACK]: {
        severity: SeverityLevel.HIGH,
        action: ActionType.RESET_POSITION,
        threshold: 3
      },
      [CheatType.TELEPORT]: {
        severity: SeverityLevel.CRITICAL,
        action: ActionType.KICK,
        threshold: 1
      },
      [CheatType.WALL_HACK]: {
        severity: SeverityLevel.HIGH,
        action: ActionType.BAN_TEMPORARY,
        threshold: 2
      },
      [CheatType.AIM_BOT]: {
        severity: SeverityLevel.CRITICAL,
        action: ActionType.BAN_TEMPORARY,
        threshold: 1
      },
      [CheatType.RESOURCE_HACK]: {
        severity: SeverityLevel.MEDIUM,
        action: ActionType.WARN,
        threshold: 5
      },
      [CheatType.PACKET_MANIPULATION]: {
        severity: SeverityLevel.HIGH,
        action: ActionType.KICK,
        threshold: 3
      },
      [CheatType.TIME_MANIPULATION]: {
        severity: SeverityLevel.CRITICAL,
        action: ActionType.BAN_PERMANENT,
        threshold: 1
      },
      [CheatType.INVALID_ACTION]: {
        severity: SeverityLevel.MEDIUM,
        action: ActionType.LOG,
        threshold: 10
      },
      [CheatType.SUSPICIOUS_BEHAVIOR]: {
        severity: SeverityLevel.LOW,
        action: ActionType.LOG,
        threshold: 20
      }
    },
    monitoring: {
      logAllDetections: true,
      alertAdmins: true,
      saveEvidence: true,
      banDuration: {
        temporary: 24, // 24小时
        escalation: [1, 6, 24, 168, 720] // 1小时到30天
      }
    }
  };

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
  ) {
    this.loadConfig();
  }

  /**
   * 初始化玩家行为模式跟踪
   */
  initializePlayerTracking(playerId: string, roomId: string): void {
    if (!this.config.enabled) return;

    const pattern: PlayerBehaviorPattern = {
      playerId,
      sessionStart: new Date(),
      actions: [],
      statistics: {
        totalActions: 0,
        actionsPerMinute: 0,
        averageLatency: 0,
        movementSpeed: {
          average: 0,
          maximum: 0,
          variance: 0
        },
        accuracy: {
          hitRate: 0,
          headShotRate: 0,
          reactionTime: 0
        },
        patterns: {
          repeatingSequences: 0,
          unusualTimings: 0,
          impossibleActions: 0
        }
      },
      anomalies: [],
      riskScore: 0
    };

    this.playerPatterns.set(playerId, pattern);
    this.detections.set(playerId, []);

    this.logger.log(`开始跟踪玩家 ${playerId} 的行为模式`);
  }

  /**
   * 验证玩家动作
   */
  validatePlayerAction(
    playerId: string,
    roomId: string,
    action: PlayerAction
  ): ValidationResult {
    if (!this.config.enabled) {
      return { valid: true, confidence: 1.0 };
    }

    const pattern = this.playerPatterns.get(playerId);
    if (!pattern) {
      this.initializePlayerTracking(playerId, roomId);
      return { valid: true, confidence: 0.5 };
    }

    // 记录动作
    pattern.actions.push(action);
    pattern.statistics.totalActions++;

    // 执行各种检测
    const validations = [
      this.validateMovement(playerId, action),
      this.validateTiming(playerId, action),
      this.validatePhysics(playerId, action),
      this.validateNetworkConsistency(playerId, action)
    ];

    // 综合验证结果
    const invalidValidations = validations.filter(v => !v.valid);
    if (invalidValidations.length > 0) {
      const confidence = 1.0 - (invalidValidations.length / validations.length);
      return {
        valid: false,
        reason: invalidValidations.map(v => v.reason).join('; '),
        confidence,
        metadata: {
          failedValidations: invalidValidations.length,
          totalValidations: validations.length
        }
      };
    }

    return { valid: true, confidence: 1.0 };
  }

  /**
   * 检测作弊行为
   */
  detectCheat(
    playerId: string,
    roomId: string,
    cheatType: CheatType,
    evidence: CheatEvidence,
    confidence: number
  ): CheatDetection | null {
    if (!this.config.enabled) return null;

    const detection: CheatDetection = {
      id: uuidv4(),
      playerId,
      roomId,
      type: cheatType,
      severity: this.config.actions[cheatType].severity,
      confidence,
      evidence,
      timestamp: new Date(),
      resolved: false,
      metadata: {}
    };

    // 记录检测结果
    const playerDetections = this.detections.get(playerId) || [];
    playerDetections.push(detection);
    this.detections.set(playerId, playerDetections);

    // 更新风险评分
    this.updateRiskScore(playerId, detection);

    // 检查是否需要采取行动
    const actionRequired = this.shouldTakeAction(playerId, cheatType);
    if (actionRequired) {
      detection.action = this.config.actions[cheatType].action;
      this.executeAction(playerId, roomId, detection);
    }

    // 触发检测事件
    this.eventEmitter.emit('cheat.detected', detection);

    this.logger.warn(
      `检测到作弊行为: ${cheatType} - 玩家: ${playerId}, 置信度: ${confidence}`
    );

    return detection;
  }

  /**
   * 检查玩家是否被封禁
   */
  isPlayerBanned(playerId: string): boolean {
    return this.bannedPlayers.has(playerId);
  }

  /**
   * 获取玩家风险评分
   */
  getPlayerRiskScore(playerId: string): number {
    const pattern = this.playerPatterns.get(playerId);
    return pattern?.riskScore || 0;
  }

  /**
   * 获取玩家检测历史
   */
  getPlayerDetections(playerId: string): CheatDetection[] {
    return this.detections.get(playerId) || [];
  }

  /**
   * 清理玩家数据
   */
  cleanupPlayerData(playerId: string): void {
    this.playerPatterns.delete(playerId);
    this.detections.delete(playerId);
    this.networkAnalysis.delete(playerId);
    
    this.logger.log(`已清理玩家 ${playerId} 的反作弊数据`);
  }

  /**
   * 定期分析和清理
   */
  @Interval(60000) // 每分钟执行一次
  performPeriodicAnalysis() {
    if (!this.config.enabled) return;

    for (const [playerId, pattern] of this.playerPatterns.entries()) {
      this.analyzePlayerBehavior(playerId, pattern);
      this.updateStatistics(playerId, pattern);
    }

    // 清理过期数据
    this.cleanupExpiredData();
  }

  // 私有方法

  private loadConfig(): void {
    // 从配置服务加载反作弊配置
    this.config.enabled = this.configService.get<boolean>('ANTI_CHEAT_ENABLED', true);
    this.config.strictMode = this.configService.get<boolean>('ANTI_CHEAT_STRICT_MODE', false);
  }

  private validateMovement(playerId: string, action: PlayerAction): ValidationResult {
    if (!action.position) {
      return { valid: true, confidence: 1.0 };
    }

    const pattern = this.playerPatterns.get(playerId);
    if (!pattern || pattern.actions.length < 2) {
      return { valid: true, confidence: 0.5 };
    }

    const lastAction = pattern.actions[pattern.actions.length - 2];
    if (!lastAction.position) {
      return { valid: true, confidence: 0.5 };
    }

    // 计算移动距离和速度
    const distance = this.calculateDistance(lastAction.position, action.position);
    const timeDiff = (action.timestamp.getTime() - lastAction.timestamp.getTime()) / 1000;
    const speed = distance / timeDiff;

    // 检查速度是否超过限制
    if (speed > this.config.detectionThresholds.speedHack.maxSpeed) {
      this.detectCheat(
        playerId,
        '', // roomId需要从上下文获取
        CheatType.SPEED_HACK,
        {
          type: 'movement_validation',
          description: `移动速度过快: ${speed.toFixed(2)} 单位/秒`,
          data: { speed, distance, timeDiff, threshold: this.config.detectionThresholds.speedHack.maxSpeed },
          context: { playerState: action, gameState: {}, networkInfo: {} }
        },
        0.8
      );

      return {
        valid: false,
        reason: `移动速度过快: ${speed.toFixed(2)} > ${this.config.detectionThresholds.speedHack.maxSpeed}`,
        confidence: 0.8
      };
    }

    // 检查传送
    if (distance > this.config.detectionThresholds.teleport.maxDistance && 
        timeDiff < this.config.detectionThresholds.teleport.timeWindow / 1000) {
      this.detectCheat(
        playerId,
        '',
        CheatType.TELEPORT,
        {
          type: 'teleport_detection',
          description: `疑似传送: 距离 ${distance.toFixed(2)}, 时间 ${timeDiff.toFixed(3)}s`,
          data: { distance, timeDiff, threshold: this.config.detectionThresholds.teleport },
          context: { playerState: action, gameState: {}, networkInfo: {} }
        },
        0.9
      );

      return {
        valid: false,
        reason: `疑似传送行为`,
        confidence: 0.9
      };
    }

    return { valid: true, confidence: 1.0 };
  }

  private validateTiming(playerId: string, action: PlayerAction): ValidationResult {
    const pattern = this.playerPatterns.get(playerId);
    if (!pattern || pattern.actions.length < 5) {
      return { valid: true, confidence: 0.5 };
    }

    // 检查动作时间间隔的规律性
    const recentActions = pattern.actions.slice(-5);
    const intervals = [];
    
    for (let i = 1; i < recentActions.length; i++) {
      const interval = recentActions[i].timestamp.getTime() - recentActions[i-1].timestamp.getTime();
      intervals.push(interval);
    }

    // 检查是否有过于规律的时间间隔（可能是机器人）
    const variance = this.calculateVariance(intervals);
    if (variance < 10 && intervals.every(i => i < 100)) { // 方差小于10ms且间隔都小于100ms
      return {
        valid: false,
        reason: '动作时间间隔过于规律，疑似机器人行为',
        confidence: 0.7
      };
    }

    return { valid: true, confidence: 1.0 };
  }

  private validatePhysics(playerId: string, action: PlayerAction): ValidationResult {
    // 简化的物理验证
    // 实际实现应该检查重力、碰撞、加速度等物理规律
    return { valid: true, confidence: 1.0 };
  }

  private validateNetworkConsistency(playerId: string, action: PlayerAction): ValidationResult {
    const analysis = this.networkAnalysis.get(playerId);
    if (!analysis) {
      return { valid: true, confidence: 0.5 };
    }

    // 检查网络异常
    if (analysis.suspiciousActivity.duplicatePackets > 10 ||
        analysis.suspiciousActivity.malformedPackets > 5) {
      return {
        valid: false,
        reason: '网络数据包异常',
        confidence: 0.6
      };
    }

    return { valid: true, confidence: 1.0 };
  }

  private shouldTakeAction(playerId: string, cheatType: CheatType): boolean {
    const detections = this.detections.get(playerId) || [];
    const typeDetections = detections.filter(d => d.type === cheatType && !d.resolved);
    
    return typeDetections.length >= this.config.actions[cheatType].threshold;
  }

  private executeAction(playerId: string, roomId: string, detection: CheatDetection): void {
    const action = detection.action;
    if (!action) return;

    switch (action) {
      case ActionType.LOG:
        this.logger.warn(`记录作弊行为: ${detection.type} - ${playerId}`);
        break;
      case ActionType.WARN:
        this.eventEmitter.emit('player.warn', { playerId, roomId, detection });
        break;
      case ActionType.KICK:
        this.eventEmitter.emit('player.kick', { playerId, roomId, detection });
        break;
      case ActionType.BAN_TEMPORARY:
        this.bannedPlayers.add(playerId);
        this.eventEmitter.emit('player.ban', { 
          playerId, 
          roomId, 
          detection, 
          duration: this.config.monitoring.banDuration.temporary 
        });
        break;
      case ActionType.BAN_PERMANENT:
        this.bannedPlayers.add(playerId);
        this.eventEmitter.emit('player.ban', { 
          playerId, 
          roomId, 
          detection, 
          permanent: true 
        });
        break;
      case ActionType.RESET_POSITION:
        this.eventEmitter.emit('player.reset_position', { playerId, roomId, detection });
        break;
    }

    detection.resolved = true;
    this.logger.log(`执行反作弊动作: ${action} - 玩家: ${playerId}`);
  }

  private updateRiskScore(playerId: string, detection: CheatDetection): void {
    const pattern = this.playerPatterns.get(playerId);
    if (!pattern) return;

    // 根据检测类型和严重程度更新风险评分
    let scoreIncrease = 0;
    switch (detection.severity) {
      case SeverityLevel.LOW: scoreIncrease = 5; break;
      case SeverityLevel.MEDIUM: scoreIncrease = 15; break;
      case SeverityLevel.HIGH: scoreIncrease = 30; break;
      case SeverityLevel.CRITICAL: scoreIncrease = 50; break;
    }

    pattern.riskScore = Math.min(100, pattern.riskScore + scoreIncrease * detection.confidence);
  }

  private analyzePlayerBehavior(playerId: string, pattern: PlayerBehaviorPattern): void {
    // 分析玩家行为模式，检测异常
    if (pattern.actions.length < 10) return;

    // 检测重复序列
    this.detectRepeatingSequences(pattern);
    
    // 检测异常时间模式
    this.detectUnusualTimings(pattern);
    
    // 检测不可能的动作
    this.detectImpossibleActions(pattern);
  }

  private detectRepeatingSequences(pattern: PlayerBehaviorPattern): void {
    // 简化的重复序列检测
    const recentActions = pattern.actions.slice(-20);
    // 实现重复序列检测算法
  }

  private detectUnusualTimings(pattern: PlayerBehaviorPattern): void {
    // 检测异常的时间模式
    const recentActions = pattern.actions.slice(-10);
    // 实现时间模式分析
  }

  private detectImpossibleActions(pattern: PlayerBehaviorPattern): void {
    // 检测物理上不可能的动作
    // 实现物理验证逻辑
  }

  private updateStatistics(playerId: string, pattern: PlayerBehaviorPattern): void {
    const sessionDuration = (Date.now() - pattern.sessionStart.getTime()) / 60000; // 分钟
    pattern.statistics.actionsPerMinute = pattern.statistics.totalActions / sessionDuration;
    
    // 更新其他统计信息
    if (pattern.actions.length > 0) {
      const latencies = pattern.actions.map(a => a.latency).filter(l => l > 0);
      if (latencies.length > 0) {
        pattern.statistics.averageLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
      }
    }
  }

  private cleanupExpiredData(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时

    for (const [playerId, pattern] of this.playerPatterns.entries()) {
      if (now - pattern.sessionStart.getTime() > maxAge) {
        this.cleanupPlayerData(playerId);
      }
    }
  }

  private calculateDistance(pos1: any, pos2: any): number {
    const dx = pos2.x - pos1.x;
    const dy = pos2.y - pos1.y;
    const dz = pos2.z - pos1.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }

  private calculateVariance(numbers: number[]): number {
    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    const squaredDiffs = numbers.map(n => Math.pow(n - mean, 2));
    return squaredDiffs.reduce((a, b) => a + b, 0) / numbers.length;
  }
}
