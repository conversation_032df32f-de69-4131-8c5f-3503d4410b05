/**
 * 用户服务节点集合 - 第三部分
 * 批次2.1 - 服务器集成节点
 * 提供用户会话、偏好、活动、分析、通知、组管理、同步等功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { UserInfo, UserSession } from './UserServiceNodes';

/**
 * 用户会话节点
 * 批次2.1 - 用户服务节点
 */
export class UserSessionNode extends VisualScriptNode {
  public static readonly TYPE = 'UserSession';
  public static readonly NAME = '用户会话';
  public static readonly DESCRIPTION = '管理用户会话和登录状态';

  constructor(nodeType: string = UserSessionNode.TYPE, name: string = UserSessionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('sessionId', 'string', '会话ID');
    this.addInput('userId', 'string', '用户ID');
    this.addInput('token', 'string', '访问令牌');
    this.addInput('action', 'string', '操作类型');
    this.addInput('deviceInfo', 'object', '设备信息');

    // 输出端口
    this.addOutput('isValid', 'boolean', '会话有效');
    this.addOutput('sessionInfo', 'object', '会话信息');
    this.addOutput('userInfo', 'object', '用户信息');
    this.addOutput('expiresAt', 'string', '过期时间');
    this.addOutput('remainingTime', 'number', '剩余时间');
    this.addOutput('onExpired', 'trigger', '会话过期事件');
    this.addOutput('onRefreshed', 'trigger', '会话刷新事件');
  }

  public execute(inputs?: any): any {
    try {
      const sessionId = inputs?.sessionId as string;
      const userId = inputs?.userId as string;
      const token = inputs?.token as string;
      const action = inputs?.action as string || 'validate';
      const deviceInfo = inputs?.deviceInfo || {};

      // 执行会话操作
      const result = this.handleSessionOperation(sessionId, userId, token, action, deviceInfo);
      
      Debug.log('UserSessionNode', `用户会话操作完成: ${action}`);

      return result;
    } catch (error) {
      Debug.error('UserSessionNode', '用户会话操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private handleSessionOperation(sessionId: string, userId: string, token: string, action: string, deviceInfo: any): any {
    switch (action) {
      case 'validate':
        return this.validateSession(sessionId || token);
      case 'create':
        return this.createSession(userId, deviceInfo);
      case 'refresh':
        return this.refreshSession(sessionId || token);
      case 'destroy':
        return this.destroySession(sessionId || token);
      case 'list':
        return this.getUserSessions(userId);
      default:
        return this.getDefaultOutputs();
    }
  }

  private validateSession(sessionIdOrToken: string): any {
    if (!sessionIdOrToken) {
      return this.getInvalidSessionOutput('会话ID或令牌为空');
    }

    // 简化的会话验证
    const sessionInfo = this.getSessionInfo(sessionIdOrToken);
    
    if (!sessionInfo) {
      return this.getInvalidSessionOutput('会话不存在');
    }

    const now = new Date();
    const expiresAt = new Date(sessionInfo.expiresAt);
    const isExpired = now > expiresAt;
    
    if (isExpired) {
      return this.getInvalidSessionOutput('会话已过期');
    }

    const remainingTime = Math.max(0, expiresAt.getTime() - now.getTime());

    return {
      isValid: true,
      sessionInfo,
      userInfo: this.getUserInfoFromSession(sessionInfo),
      expiresAt: expiresAt.toISOString(),
      remainingTime: Math.floor(remainingTime / 1000),
      onExpired: false,
      onRefreshed: false
    };
  }

  private createSession(userId: string, deviceInfo: any): any {
    if (!userId) {
      return this.getInvalidSessionOutput('用户ID为空');
    }

    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const token = `token_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`;
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 8); // 8小时

    const sessionInfo: UserSession = {
      sessionId,
      userId,
      token,
      expiresAt,
      deviceInfo: JSON.stringify(deviceInfo),
      isActive: true
    };

    return {
      isValid: true,
      sessionInfo,
      userInfo: this.getUserInfoFromUserId(userId),
      expiresAt: expiresAt.toISOString(),
      remainingTime: 8 * 3600, // 8小时
      onExpired: false,
      onRefreshed: false
    };
  }

  private refreshSession(sessionIdOrToken: string): any {
    const sessionInfo = this.getSessionInfo(sessionIdOrToken);
    
    if (!sessionInfo) {
      return this.getInvalidSessionOutput('会话不存在');
    }

    // 延长会话时间
    const newExpiresAt = new Date();
    newExpiresAt.setHours(newExpiresAt.getHours() + 8);
    
    sessionInfo.expiresAt = newExpiresAt;

    return {
      isValid: true,
      sessionInfo,
      userInfo: this.getUserInfoFromSession(sessionInfo),
      expiresAt: newExpiresAt.toISOString(),
      remainingTime: 8 * 3600,
      onExpired: false,
      onRefreshed: true
    };
  }

  private destroySession(sessionIdOrToken: string): any {
    const sessionInfo = this.getSessionInfo(sessionIdOrToken);
    
    if (sessionInfo) {
      sessionInfo.isActive = false;
    }

    return {
      isValid: false,
      sessionInfo: null,
      userInfo: null,
      expiresAt: '',
      remainingTime: 0,
      onExpired: true,
      onRefreshed: false
    };
  }

  private getUserSessions(userId: string): any {
    if (!userId) {
      return this.getDefaultOutputs();
    }

    // 简化的用户会话列表
    const sessions = [
      this.createSession(userId, { device: 'web' }).sessionInfo,
      this.createSession(userId, { device: 'mobile' }).sessionInfo
    ];

    return {
      isValid: true,
      sessionInfo: { sessions },
      userInfo: this.getUserInfoFromUserId(userId),
      expiresAt: '',
      remainingTime: 0,
      onExpired: false,
      onRefreshed: false
    };
  }

  private getSessionInfo(sessionIdOrToken: string): UserSession | null {
    // 简化的会话信息获取
    // 实际应该从数据库或缓存获取
    if (sessionIdOrToken.startsWith('session_') || sessionIdOrToken.startsWith('token_')) {
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 4); // 模拟4小时后过期

      return {
        sessionId: sessionIdOrToken.startsWith('session_') ? sessionIdOrToken : `session_${Date.now()}`,
        userId: 'user_123',
        token: sessionIdOrToken.startsWith('token_') ? sessionIdOrToken : `token_${Date.now()}`,
        expiresAt,
        deviceInfo: '{"device":"web"}',
        isActive: true
      };
    }
    
    return null;
  }

  private getUserInfoFromSession(session: UserSession): UserInfo {
    return this.getUserInfoFromUserId(session.userId);
  }

  private getUserInfoFromUserId(userId: string): UserInfo {
    return {
      id: userId,
      username: `user_${userId}`,
      email: `${userId}@example.com`,
      displayName: `User ${userId}`,
      roles: ['user'],
      permissions: ['read', 'write'],
      preferences: {},
      createdAt: new Date(),
      isActive: true
    };
  }

  private getInvalidSessionOutput(errorMessage: string): any {
    return {
      isValid: false,
      sessionInfo: null,
      userInfo: null,
      expiresAt: '',
      remainingTime: 0,
      onExpired: true,
      onRefreshed: false,
      errorMessage
    };
  }

  private getDefaultOutputs(): any {
    return {
      isValid: false,
      sessionInfo: null,
      userInfo: null,
      expiresAt: '',
      remainingTime: 0,
      onExpired: false,
      onRefreshed: false
    };
  }
}

/**
 * 用户偏好节点
 * 批次2.1 - 用户服务节点
 */
export class UserPreferencesNode extends VisualScriptNode {
  public static readonly TYPE = 'UserPreferences';
  public static readonly NAME = '用户偏好';
  public static readonly DESCRIPTION = '管理用户个人偏好设置';

  constructor(nodeType: string = UserPreferencesNode.TYPE, name: string = UserPreferencesNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('userId', 'string', '用户ID');
    this.addInput('action', 'string', '操作类型');
    this.addInput('preferences', 'object', '偏好设置');
    this.addInput('key', 'string', '偏好键');
    this.addInput('value', 'any', '偏好值');
    this.addInput('category', 'string', '偏好分类');

    // 输出端口
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('userPreferences', 'object', '用户偏好');
    this.addOutput('preferenceValue', 'any', '偏好值');
    this.addOutput('categories', 'array', '偏好分类');
    this.addOutput('onChanged', 'trigger', '偏好变更事件');
  }

  public execute(inputs?: any): any {
    try {
      const userId = inputs?.userId as string;
      const action = inputs?.action as string || 'get';
      const preferences = inputs?.preferences || {};
      const key = inputs?.key as string;
      const value = inputs?.value;
      const category = inputs?.category as string;

      if (!userId) {
        Debug.warn('UserPreferencesNode', '用户ID为空');
        return this.getDefaultOutputs();
      }

      // 执行偏好操作
      const result = this.handlePreferencesOperation(userId, action, preferences, key, value, category);
      
      Debug.log('UserPreferencesNode', `用户偏好操作完成: ${action} for ${userId}`);

      return result;
    } catch (error) {
      Debug.error('UserPreferencesNode', '用户偏好操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private handlePreferencesOperation(userId: string, action: string, preferences: any, key: string, value: any, category: string): any {
    switch (action) {
      case 'get':
        return this.getUserPreferences(userId, category);
      case 'set':
        return this.setUserPreferences(userId, preferences);
      case 'getValue':
        return this.getPreferenceValue(userId, key);
      case 'setValue':
        return this.setPreferenceValue(userId, key, value);
      case 'reset':
        return this.resetPreferences(userId, category);
      default:
        return this.getDefaultOutputs();
    }
  }

  private getUserPreferences(userId: string, category?: string): any {
    // 简化的用户偏好获取
    const allPreferences = {
      ui: {
        theme: 'light',
        language: 'zh-CN',
        fontSize: 14,
        sidebarCollapsed: false
      },
      notifications: {
        email: true,
        push: true,
        sound: false,
        frequency: 'daily'
      },
      privacy: {
        showProfile: true,
        showActivity: false,
        allowMessages: true
      },
      system: {
        autoSave: true,
        backupFrequency: 'weekly',
        timezone: 'Asia/Shanghai'
      }
    };

    const userPreferences = category ? { [category]: allPreferences[category] } : allPreferences;
    const categories = Object.keys(allPreferences);

    return {
      success: true,
      userPreferences,
      preferenceValue: null,
      categories,
      onChanged: false
    };
  }

  private setUserPreferences(userId: string, preferences: any): any {
    // 简化的用户偏好设置
    const currentPreferences = this.getUserPreferences(userId).userPreferences;
    const updatedPreferences = { ...currentPreferences, ...preferences };

    return {
      success: true,
      userPreferences: updatedPreferences,
      preferenceValue: null,
      categories: Object.keys(updatedPreferences),
      onChanged: true
    };
  }

  private getPreferenceValue(userId: string, key: string): any {
    if (!key) {
      return this.getDefaultOutputs();
    }

    const preferences = this.getUserPreferences(userId).userPreferences;
    const value = this.getNestedValue(preferences, key);

    return {
      success: true,
      userPreferences: preferences,
      preferenceValue: value,
      categories: Object.keys(preferences),
      onChanged: false
    };
  }

  private setPreferenceValue(userId: string, key: string, value: any): any {
    if (!key) {
      return this.getDefaultOutputs();
    }

    const preferences = this.getUserPreferences(userId).userPreferences;
    this.setNestedValue(preferences, key, value);

    return {
      success: true,
      userPreferences: preferences,
      preferenceValue: value,
      categories: Object.keys(preferences),
      onChanged: true
    };
  }

  private resetPreferences(userId: string, category?: string): any {
    const defaultPreferences = this.getUserPreferences('default').userPreferences;
    
    if (category) {
      const currentPreferences = this.getUserPreferences(userId).userPreferences;
      currentPreferences[category] = defaultPreferences[category];
      
      return {
        success: true,
        userPreferences: currentPreferences,
        preferenceValue: null,
        categories: Object.keys(currentPreferences),
        onChanged: true
      };
    } else {
      return {
        success: true,
        userPreferences: defaultPreferences,
        preferenceValue: null,
        categories: Object.keys(defaultPreferences),
        onChanged: true
      };
    }
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current && current[key], obj);
  }

  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {};
      return current[key];
    }, obj);
    
    if (lastKey) {
      target[lastKey] = value;
    }
  }

  private getDefaultOutputs(): any {
    return {
      success: false,
      userPreferences: {},
      preferenceValue: null,
      categories: [],
      onChanged: false
    };
  }
}
