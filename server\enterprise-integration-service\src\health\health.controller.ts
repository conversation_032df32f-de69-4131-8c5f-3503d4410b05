import { Controller, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import type { 
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
 } from '@nestjs/terminus';
import { HealthService } from './health.service';

/**
 * 健康检查控制器
 * 提供服务健康状态监控接口
 */
@ApiTags('health')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(
    private readonly healthCheckService: HealthCheckService,
    private readonly typeOrmHealthIndicator: TypeOrmHealthIndicator,
    private readonly memoryHealthIndicator: MemoryHealthIndicator,
    private readonly diskHealthIndicator: DiskHealthIndicator,
    private readonly healthService: HealthService,
  ) {}

  /**
   * 基础健康检查
   */
  @Get()
  @ApiOperation({
    summary: '基础健康检查',
    description: '检查服务基本运行状态',
  })
  @ApiResponse({
    status: 200,
    description: '服务健康',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        info: { type: 'object' },
        error: { type: 'object' },
        details: { type: 'object' },
      },
    },
  })
  @HealthCheck()
  async check() {
    return this.healthCheckService.check([
      // 数据库健康检查
      () => this.typeOrmHealthIndicator.pingCheck('database'),
      
      // 内存使用检查
      () =>
        this.memoryHealthIndicator.checkHeap('memory_heap', 150 * 1024 * 1024),
      () =>
        this.memoryHealthIndicator.checkRSS('memory_rss', 150 * 1024 * 1024),
      
      // 磁盘空间检查
      () =>
        this.diskHealthIndicator.checkStorage('storage', {
          path: '/',
          thresholdPercent: 0.9,
        }),
    ]);
  }

  /**
   * 详细健康检查
   */
  @Get('detailed')
  @ApiOperation({
    summary: '详细健康检查',
    description: '检查服务详细状态，包括外部依赖和业务指标',
  })
  @ApiResponse({
    status: 200,
    description: '详细健康状态',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string' },
        timestamp: { type: 'string' },
        uptime: { type: 'number' },
        version: { type: 'string' },
        environment: { type: 'string' },
        database: { type: 'object' },
        redis: { type: 'object' },
        enterpriseSystems: { type: 'object' },
        integrationFlows: { type: 'object' },
        performance: { type: 'object' },
      },
    },
  })
  async detailedCheck() {
    try {
      const healthStatus = await this.healthService.getDetailedHealthStatus();
      return {
        status: 'ok',
        ...healthStatus,
      };
    } catch (error) {
      this.logger.error('详细健康检查失败', error);
      return {
        status: 'error',
        message: (error as Error).message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * 就绪检查
   */
  @Get('ready')
  @ApiOperation({
    summary: '就绪检查',
    description: '检查服务是否准备好接收请求',
  })
  @ApiResponse({
    status: 200,
    description: '服务就绪',
  })
  @ApiResponse({
    status: 503,
    description: '服务未就绪',
  })
  async readiness() {
    return this.healthCheckService.check([
      () => this.typeOrmHealthIndicator.pingCheck('database'),
      () => this.healthService.checkRedisConnection(),
      () => this.healthService.checkCriticalSystems(),
    ]);
  }

  /**
   * 存活检查
   */
  @Get('live')
  @ApiOperation({
    summary: '存活检查',
    description: '检查服务是否存活',
  })
  @ApiResponse({
    status: 200,
    description: '服务存活',
  })
  async liveness() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }

  /**
   * 性能指标
   */
  @Get('metrics')
  @ApiOperation({
    summary: '性能指标',
    description: '获取服务性能指标',
  })
  @ApiResponse({
    status: 200,
    description: '性能指标',
    schema: {
      type: 'object',
      properties: {
        cpu: { type: 'object' },
        memory: { type: 'object' },
        eventLoop: { type: 'object' },
        gc: { type: 'object' },
        requests: { type: 'object' },
        database: { type: 'object' },
        integrations: { type: 'object' },
      },
    },
  })
  async metrics() {
    try {
      const metrics = await this.healthService.getPerformanceMetrics();
      return {
        status: 'ok',
        metrics,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('获取性能指标失败', error);
      return {
        status: 'error',
        message: (error as Error).message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * 系统信息
   */
  @Get('info')
  @ApiOperation({
    summary: '系统信息',
    description: '获取系统基本信息',
  })
  @ApiResponse({
    status: 200,
    description: '系统信息',
  })
  async info() {
    return {
      service: 'enterprise-integration-service',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
    };
  }
}
