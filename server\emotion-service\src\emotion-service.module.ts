/**
 * 情感分析服务主模块
 */

import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';

// 控制器
import { EmotionController } from './emotion/emotion.controller';
import { HealthController } from './health/health.controller';

// 服务
import { EmotionAnalysisService } from './emotion/emotion-analysis.service';
import { EmotionDataService } from './emotion-data/emotion-data.service';
import { HealthService } from './health/health.service';

// 实体
import { EmotionRecord } from './entities/emotion-record.entity';
import { EmotionPattern } from './entities/emotion-pattern.entity';
import { UserEmotionProfile } from './entities/user-emotion-profile.entity';

/**
 * 情感分析服务主模块
 */
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
      expandVariables: true,
      validationOptions: {
        allowUnknown: false,
        abortEarly: true,
      },
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get<string>('DB_HOST', 'localhost'),
        port: configService.get<number>('DB_PORT', 3306),
        username: configService.get<string>('DB_USERNAME', 'root'),
        password: configService.get<string>('DB_PASSWORD'),
        database: configService.get<string>('DB_DATABASE', 'emotion_service'),
        entities: [EmotionRecord, EmotionPattern, UserEmotionProfile],
        synchronize: configService.get<boolean>('DB_SYNCHRONIZE', false),
        logging: configService.get<boolean>('DB_LOGGING', false),
        charset: 'utf8mb4',
        timezone: '+08:00',
        extra: {
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
        },
      }),
      inject: [ConfigService],
    }),

    // 实体模块
    TypeOrmModule.forFeature([
      EmotionRecord,
      EmotionPattern,
      UserEmotionProfile,
    ]),

    // 事件发射器模块
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 20,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // 定时任务模块
    ScheduleModule.forRoot(),
  ],

  controllers: [
    EmotionController,
    HealthController,
  ],

  providers: [
    EmotionAnalysisService,
    EmotionDataService,
    HealthService,
  ],

  exports: [
    EmotionAnalysisService,
    EmotionDataService,
    HealthService,
  ],
})
export class EmotionServiceModule {}
