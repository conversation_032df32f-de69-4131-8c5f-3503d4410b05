import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { InjectRedis } from '@nestjs-modules/ioredis';
import type {  Model, Types  } from 'mongoose';
import Redis from 'ioredis';
import type {  UITheme, UIThemeDocument, ThemeType, ThemeStatus  } from './schemas/ui-theme.schema';
import { CreateUIThemeDto, UpdateUIThemeDto, QueryUIThemeDto } from './dto/ui-theme.dto';

@Injectable()
export class UIThemeService {
  constructor(
    @InjectModel(UITheme.name) private themeModel: Model<UIThemeDocument>,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  /**
   * 创建UI主题
   */
  async create(createThemeDto: CreateUIThemeDto, userId: Types.ObjectId): Promise<UITheme> {
    try {
      // 检查主题名称是否已存在
      const existingTheme = await this.themeModel.findOne({
        name: createThemeDto.name,
        organizationId: createThemeDto.organizationId,
      });

      if (existingTheme) {
        throw new BadRequestException('主题名称已存在');
      }

      // 创建主题
      const theme = new this.themeModel({
        ...createThemeDto,
        createdBy: userId,
        updatedBy: userId,
      });

      const savedTheme = await theme.save();

      // 清除相关缓存
      await this.clearThemeCache(createThemeDto.type);

      return savedTheme;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('创建主题失败');
    }
  }

  /**
   * 查询UI主题列表
   */
  async findAll(queryDto: QueryUIThemeDto, userId: Types.ObjectId): Promise<{
    themes: UITheme[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { page = 1, limit = 20, search, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = queryDto;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const query: any = {
      isActive: true,
    };

    // 添加过滤条件
    if (filters.type) query.type = filters.type;
    if (filters.status) query.status = filters.status;
    if (filters.organizationId) query.organizationId = filters.organizationId;
    if (filters.tags) query.tags = { $in: filters.tags.split(',') };
    if (typeof filters.isPublic === 'boolean') query.isPublic = filters.isPublic;

    // 添加搜索条件
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $regex: search, $options: 'i' } },
      ];
    }

    // 添加权限过滤
    query.$or = [
      { isPublic: true },
      { createdBy: userId },
      // 这里可以添加组织权限检查
    ];

    // 排序
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // 执行查询
    const [themes, total] = await Promise.all([
      this.themeModel
        .find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('createdBy', 'username email')
        .populate('updatedBy', 'username email')
        .exec(),
      this.themeModel.countDocuments(query),
    ]);

    return {
      themes,
      total,
      page,
      limit,
    };
  }

  /**
   * 获取单个UI主题
   */
  async findOne(id: string, userId: Types.ObjectId): Promise<UITheme> {
    // 尝试从缓存获取
    const cacheKey = `ui_theme:${id}`;
    const cached = await this.redis.get(cacheKey);
    if (cached) {
      const theme = JSON.parse(cached);
      if (this.canAccessTheme(theme, userId)) {
        return theme;
      }
    }

    // 从数据库获取
    const theme = await this.themeModel
      .findById(id)
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email')
      .exec();

    if (!theme) {
      throw new NotFoundException('主题不存在');
    }

    // 检查访问权限
    if (!this.canAccessTheme(theme, userId)) {
      throw new ForbiddenException('没有访问权限');
    }

    // 增加浏览次数
    await this.incrementViews(theme);

    // 缓存主题
    await this.redis.setex(cacheKey, 1800, JSON.stringify(theme));

    return theme;
  }

  /**
   * 更新UI主题
   */
  async update(id: string, updateThemeDto: UpdateUIThemeDto, userId: Types.ObjectId): Promise<UITheme> {
    const theme = await this.themeModel.findById(id);

    if (!theme) {
      throw new NotFoundException('主题不存在');
    }

    // 检查编辑权限
    if (!this.canEditTheme(theme, userId)) {
      throw new ForbiddenException('没有编辑权限');
    }

    // 更新主题
    Object.assign(theme, updateThemeDto);
    theme.updatedBy = userId;
    theme.updatedAt = new Date();

    const updatedTheme = await theme.save();

    // 清除缓存
    await this.redis.del(`ui_theme:${id}`);
    await this.clearThemeCache(theme.type);

    return updatedTheme;
  }

  /**
   * 删除UI主题
   */
  async remove(id: string, userId: Types.ObjectId): Promise<void> {
    const theme = await this.themeModel.findById(id);

    if (!theme) {
      throw new NotFoundException('主题不存在');
    }

    // 检查删除权限
    if (!this.canEditTheme(theme, userId)) {
      throw new ForbiddenException('没有删除权限');
    }

    // 软删除
    await this.softDeleteTheme(theme, userId);

    // 清除缓存
    await this.redis.del(`ui_theme:${id}`);
    await this.clearThemeCache(theme.type);
  }

  /**
   * 发布主题
   */
  async publish(id: string, userId: Types.ObjectId): Promise<UITheme> {
    const theme = await this.themeModel.findById(id);

    if (!theme) {
      throw new NotFoundException('主题不存在');
    }

    if (!this.canEditTheme(theme, userId)) {
      throw new ForbiddenException('没有发布权限');
    }

    if (theme.status === ThemeStatus.PUBLISHED) {
      throw new BadRequestException('主题已经发布');
    }

    theme.status = ThemeStatus.PUBLISHED;
    theme.updatedBy = userId;
    theme.updatedAt = new Date();

    const publishedTheme = await theme.save();

    // 清除缓存
    await this.redis.del(`ui_theme:${id}`);

    return publishedTheme;
  }

  /**
   * 下载主题
   */
  async download(id: string, userId: Types.ObjectId): Promise<UITheme> {
    const theme = await this.findOne(id, userId);
    
    // 增加下载次数
    await this.incrementDownloads(theme);

    return theme;
  }

  /**
   * 检查主题访问权限
   */
  private canAccessTheme(theme: UITheme, userId: Types.ObjectId): boolean {
    if (theme.isPublic) {
      return true;
    }

    return theme.createdBy.equals(userId);
  }

  /**
   * 检查主题编辑权限
   */
  private canEditTheme(theme: UITheme, userId: Types.ObjectId): boolean {
    return theme.createdBy.equals(userId);
  }

  /**
   * 增加浏览次数
   */
  private async incrementViews(theme: any): Promise<void> {
    theme.statistics = theme.statistics || {};
    theme.statistics.views = (theme.statistics.views || 0) + 1;
    await theme.save();
  }

  /**
   * 增加下载次数
   */
  private async incrementDownloads(theme: any): Promise<void> {
    theme.statistics = theme.statistics || {};
    theme.statistics.downloads = (theme.statistics.downloads || 0) + 1;
    await theme.save();
  }

  /**
   * 软删除主题
   */
  private async softDeleteTheme(theme: any, userId: Types.ObjectId): Promise<void> {
    theme.deletedAt = new Date();
    theme.deletedBy = userId;
    theme.isActive = false;
    await theme.save();
  }

  /**
   * 生成主题变体
   */
  async generateThemeVariants(id: string, userId: Types.ObjectId, options: {
    colorSchemes?: string[];
    sizes?: string[];
    modes?: string[];
  }): Promise<UITheme[]> {
    const baseTheme = await this.findOne(id, userId);
    const variants: UITheme[] = [];

    const { colorSchemes = ['light', 'dark'], sizes = ['small', 'medium', 'large'], modes = ['default'] } = options;

    for (const colorScheme of colorSchemes) {
      for (const size of sizes) {
        for (const mode of modes) {
          if (colorScheme === 'light' && size === 'medium' && mode === 'default') {
            continue; // 跳过基础主题
          }

          const variantName = `${baseTheme.name}-${colorScheme}-${size}-${mode}`;
          const variantTheme = await this.createThemeVariant(baseTheme, {
            name: variantName,
            colorScheme,
            size,
            mode
          }, userId);

          variants.push(variantTheme);
        }
      }
    }

    return variants;
  }

  /**
   * 应用主题到项目
   */
  async applyThemeToProject(themeId: string, projectId: string, userId: Types.ObjectId): Promise<any> {
    const theme = await this.findOne(themeId, userId);

    // 生成主题CSS
    const css = await this.generateThemeCSS(theme);

    // 生成主题配置
    const config = await this.generateThemeConfig(theme);

    // 这里应该调用项目服务来应用主题
    // 暂时返回主题数据
    return {
      themeId: (theme as any)._id,
      projectId,
      css,
      config,
      appliedAt: new Date(),
      appliedBy: userId
    };
  }

  /**
   * 预览主题效果
   */
  async previewTheme(id: string, userId: Types.ObjectId, previewOptions?: {
    components?: string[];
    viewport?: { width: number; height: number };
    device?: string;
  }): Promise<any> {
    const theme = await this.findOne(id, userId);

    // 生成预览数据
    const previewData = {
      theme: theme,
      css: await this.generateThemeCSS(theme),
      components: previewOptions?.components || ['button', 'input', 'card', 'modal'],
      viewport: previewOptions?.viewport || { width: 1200, height: 800 },
      device: previewOptions?.device || 'desktop',
      previewUrl: `/api/v1/ui-theme/${id}/preview`,
      timestamp: new Date()
    };

    // 缓存预览数据
    const cacheKey = `theme_preview:${id}:${userId}`;
    await this.redis.setex(cacheKey, 300, JSON.stringify(previewData)); // 5分钟缓存

    return previewData;
  }

  /**
   * 导出主题
   */
  async exportTheme(id: string, userId: Types.ObjectId, format: 'json' | 'css' | 'scss' | 'less' = 'json'): Promise<any> {
    const theme = await this.findOne(id, userId);

    switch (format) {
      case 'css':
        return {
          format: 'css',
          content: await this.generateThemeCSS(theme),
          filename: `${theme.name}.css`
        };
      case 'scss':
        return {
          format: 'scss',
          content: await this.generateThemeSCSS(theme),
          filename: `${theme.name}.scss`
        };
      case 'less':
        return {
          format: 'less',
          content: await this.generateThemeLESS(theme),
          filename: `${theme.name}.less`
        };
      default:
        return {
          format: 'json',
          content: theme,
          filename: `${theme.name}.json`
        };
    }
  }

  /**
   * 导入主题
   */
  async importTheme(themeData: any, userId: Types.ObjectId, options?: {
    overwrite?: boolean;
    namePrefix?: string;
  }): Promise<UITheme> {
    const { overwrite = false, namePrefix = 'Imported' } = options || {};

    // 验证主题数据
    await this.validateThemeData(themeData);

    // 检查名称冲突
    let themeName = themeData.name;
    if (!overwrite) {
      const existingTheme = await this.themeModel.findOne({ name: themeName });
      if (existingTheme) {
        themeName = `${namePrefix} ${themeName}`;
      }
    }

    // 创建导入的主题
    const importedTheme = new this.themeModel({
      ...themeData,
      _id: undefined,
      name: themeName,
      createdBy: userId,
      updatedBy: userId,
      isPublic: false,
      status: ThemeStatus.DRAFT
    });

    return importedTheme.save();
  }

  /**
   * 获取主题使用统计
   */
  async getThemeUsageStats(id: string, userId: Types.ObjectId): Promise<any> {
    const theme = await this.findOne(id, userId);

    // 这里应该从项目服务获取使用统计
    // 暂时返回模拟数据
    return {
      themeId: (theme as any)._id,
      themeName: theme.name,
      totalProjects: 0,
      activeProjects: 0,
      totalViews: theme.statistics?.views || 0,
      totalDownloads: theme.statistics?.downloads || 0,
      usageByMonth: [],
      popularComponents: [],
      lastUsed: null
    };
  }

  // 私有辅助方法

  /**
   * 创建主题变体
   */
  private async createThemeVariant(baseTheme: any, variant: any, userId: Types.ObjectId): Promise<UITheme> {
    const variantData = {
      ...baseTheme.toObject(),
      _id: undefined,
      name: variant.name,
      parentThemeId: baseTheme._id,
      variant: {
        colorScheme: variant.colorScheme,
        size: variant.size,
        mode: variant.mode
      },
      createdBy: userId,
      updatedBy: userId,
      isPublic: false,
      status: ThemeStatus.DRAFT
    };

    // 应用变体修改
    if (variant.colorScheme === 'dark') {
      variantData.colors = this.generateDarkColorScheme(variantData.colors);
    }

    if (variant.size !== 'medium') {
      variantData.typography = this.adjustTypographySize(variantData.typography, variant.size);
      variantData.spacing = this.adjustSpacing(variantData.spacing, variant.size);
    }

    const variantTheme = new this.themeModel(variantData);
    return variantTheme.save();
  }

  /**
   * 生成主题CSS
   */
  private async generateThemeCSS(theme: any): Promise<string> {
    const css = [];

    // 生成CSS变量
    css.push(':root {');

    // 颜色变量
    if (theme.colors) {
      Object.entries(theme.colors).forEach(([key, value]) => {
        css.push(`  --color-${key}: ${value};`);
      });
    }

    // 字体变量
    if (theme.typography) {
      Object.entries(theme.typography).forEach(([key, value]) => {
        if (typeof value === 'string' || typeof value === 'number') {
          css.push(`  --font-${key}: ${value};`);
        }
      });
    }

    // 间距变量
    if (theme.spacing) {
      Object.entries(theme.spacing).forEach(([key, value]) => {
        css.push(`  --spacing-${key}: ${value};`);
      });
    }

    css.push('}');

    // 生成组件样式
    if (theme.components) {
      Object.entries(theme.components).forEach(([componentName, styles]: [string, any]) => {
        css.push(`\n.${componentName} {`);
        Object.entries(styles).forEach(([property, value]) => {
          css.push(`  ${property}: ${value};`);
        });
        css.push('}');
      });
    }

    return css.join('\n');
  }

  /**
   * 生成主题SCSS
   */
  private async generateThemeSCSS(theme: any): Promise<string> {
    const scss = [];

    // 生成SCSS变量
    if (theme.colors) {
      scss.push('// Colors');
      Object.entries(theme.colors).forEach(([key, value]) => {
        scss.push(`$color-${key}: ${value};`);
      });
      scss.push('');
    }

    if (theme.typography) {
      scss.push('// Typography');
      Object.entries(theme.typography).forEach(([key, value]) => {
        if (typeof value === 'string' || typeof value === 'number') {
          scss.push(`$font-${key}: ${value};`);
        }
      });
      scss.push('');
    }

    // 生成mixins和组件样式
    if (theme.components) {
      scss.push('// Components');
      Object.entries(theme.components).forEach(([componentName, styles]: [string, any]) => {
        scss.push(`@mixin ${componentName}-theme {`);
        Object.entries(styles).forEach(([property, value]) => {
          scss.push(`  ${property}: ${value};`);
        });
        scss.push('}');
        scss.push('');
        scss.push(`.${componentName} {`);
        scss.push(`  @include ${componentName}-theme;`);
        scss.push('}');
        scss.push('');
      });
    }

    return scss.join('\n');
  }

  /**
   * 生成主题LESS
   */
  private async generateThemeLESS(theme: any): Promise<string> {
    const less = [];

    // 生成LESS变量
    if (theme.colors) {
      less.push('// Colors');
      Object.entries(theme.colors).forEach(([key, value]) => {
        less.push(`@color-${key}: ${value};`);
      });
      less.push('');
    }

    return less.join('\n');
  }

  /**
   * 生成主题配置
   */
  private async generateThemeConfig(theme: any): Promise<any> {
    return {
      name: theme.name,
      version: theme.version || '1.0.0',
      colors: theme.colors,
      typography: theme.typography,
      spacing: theme.spacing,
      components: theme.components,
      breakpoints: theme.breakpoints,
      animations: theme.animations
    };
  }

  /**
   * 生成暗色主题
   */
  private generateDarkColorScheme(colors: any): any {
    const darkColors = { ...colors };

    // 简单的暗色转换逻辑
    if (darkColors.primary) {
      darkColors.background = '#1a1a1a';
      darkColors.surface = '#2d2d2d';
      darkColors.text = '#ffffff';
      darkColors.textSecondary = '#cccccc';
    }

    return darkColors;
  }

  /**
   * 调整字体大小
   */
  private adjustTypographySize(typography: any, size: string): any {
    const adjusted = { ...typography };
    const multiplier = size === 'small' ? 0.875 : size === 'large' ? 1.125 : 1;

    Object.keys(adjusted).forEach(key => {
      if (key.includes('Size') && typeof adjusted[key] === 'number') {
        adjusted[key] = Math.round(adjusted[key] * multiplier);
      }
    });

    return adjusted;
  }

  /**
   * 调整间距
   */
  private adjustSpacing(spacing: any, size: string): any {
    const adjusted = { ...spacing };
    const multiplier = size === 'small' ? 0.75 : size === 'large' ? 1.25 : 1;

    Object.keys(adjusted).forEach(key => {
      if (typeof adjusted[key] === 'number') {
        adjusted[key] = Math.round(adjusted[key] * multiplier);
      }
    });

    return adjusted;
  }

  /**
   * 验证主题数据
   */
  private async validateThemeData(themeData: any): Promise<void> {
    const errors = [];

    if (!themeData.name) errors.push('主题名称不能为空');
    if (!themeData.colors) errors.push('颜色配置不能为空');
    if (!themeData.typography) errors.push('字体配置不能为空');

    if (errors.length > 0) {
      throw new BadRequestException(`主题数据验证失败: ${errors.join(', ')}`);
    }
  }

  /**
   * 清除主题缓存
   */
  private async clearThemeCache(type: ThemeType): Promise<void> {
    const pattern = `ui_theme*:${type}:*`;
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}
