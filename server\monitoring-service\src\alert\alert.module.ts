import { Modu<PERSON> } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { AlertService } from './alert.service';
import { AlertController } from './alert.controller';
import { AlertEntity } from './entities/alert.entity';
import { AlertRuleEntity } from './entities/alert-rule.entity';
import { AlertRuleService } from './alert-rule.service';
import { AlertEvaluatorService } from './alert-evaluator.service';
import { NotificationModule } from '../notification/notification.module';
import { ServiceMetricsEntity } from '../monitoring/entities/service-metrics.entity';
import { SystemMetricsEntity } from '../monitoring/entities/system-metrics.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([AlertEntity, AlertRuleEntity, ServiceMetricsEntity, SystemMetricsEntity]),
    NotificationModule,
  ],
  controllers: [AlertController],
  providers: [
    AlertService,
    AlertRuleService,
    AlertEvaluatorService,
  ],
  exports: [
    AlertService,
    AlertRuleService,
    AlertEvaluatorService,
  ],
})
export class AlertModule {}
