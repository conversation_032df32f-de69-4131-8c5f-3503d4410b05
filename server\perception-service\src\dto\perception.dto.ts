/**
 * 感知数据处理DTO
 */

import {
  IsString,
  IsNumber,
  IsBoolean,
  IsOptional,
  IsArray,
  IsObject,
  IsEnum,
  Min,
  Max,
  ValidateNested,
} from 'class-validator';
import type {  Type  } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PerceptionModality } from '../types/perception.types';

/**
 * 感知数据处理DTO
 */
export class ProcessPerceptionDataDto {
  @ApiProperty({ description: '数据ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: '实体ID' })
  @IsString()
  entityId: string;

  @ApiProperty({ description: '会话ID' })
  @IsString()
  sessionId: string;

  @ApiProperty({ description: '感知模态', enum: PerceptionModality })
  @IsEnum(PerceptionModality)
  modality: PerceptionModality;

  @ApiProperty({ description: '时间戳' })
  @IsNumber()
  timestamp: number;

  @ApiProperty({ description: '置信度', minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence: number;

  @ApiProperty({ description: '感知数据' })
  @IsObject()
  data: any;

  @ApiProperty({ description: '元数据', default: {} })
  @IsObject()
  metadata: any;

  @ApiPropertyOptional({ description: '是否已处理', default: false })
  @IsOptional()
  @IsBoolean()
  processed?: boolean;

  @ApiPropertyOptional({ description: '异常列表', default: [] })
  @IsOptional()
  @IsArray()
  anomalies?: any[];
}

/**
 * 批量处理感知数据DTO
 */
export class BatchProcessPerceptionDataDto {
  @ApiProperty({ description: '感知数据列表', type: [ProcessPerceptionDataDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProcessPerceptionDataDto)
  data: ProcessPerceptionDataDto[];
}

/**
 * 更新配置DTO
 */
export class UpdateConfigDto {
  @ApiPropertyOptional({ description: '启用实时处理' })
  @IsOptional()
  @IsBoolean()
  enableRealTimeProcessing?: boolean;

  @ApiPropertyOptional({ description: '批次大小', minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  batchSize?: number;

  @ApiPropertyOptional({ description: '融合阈值', minimum: 0, maximum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  fusionThreshold?: number;

  @ApiPropertyOptional({ description: '异常阈值', minimum: 0, maximum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  anomalyThreshold?: number;

  @ApiPropertyOptional({ description: '数据保留期间(毫秒)', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  retentionPeriod?: number;

  @ApiPropertyOptional({ description: '质量阈值', minimum: 0, maximum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  qualityThreshold?: number;

  @ApiPropertyOptional({ description: '启用预测' })
  @IsOptional()
  @IsBoolean()
  enablePrediction?: boolean;

  @ApiPropertyOptional({ description: '预测时间范围(毫秒)', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  predictionHorizon?: number;
}

/**
 * 感知数据响应DTO
 */
export class PerceptionDataResponseDto {
  @ApiProperty({ description: '操作是否成功' })
  success: boolean;

  @ApiProperty({ description: '响应消息' })
  message: string;

  @ApiProperty({ description: '响应数据' })
  data: any;
}

/**
 * 融合感知数据响应DTO
 */
export class FusedPerceptionDataResponseDto {
  @ApiProperty({ description: '操作是否成功' })
  success: boolean;

  @ApiProperty({ description: '响应消息' })
  message: string;

  @ApiProperty({ description: '融合数据' })
  data: any;
}

/**
 * 统计信息响应DTO
 */
export class StatisticsResponseDto {
  @ApiProperty({ description: '操作是否成功' })
  success: boolean;

  @ApiProperty({ description: '响应消息' })
  message: string;

  @ApiProperty({ description: '统计数据' })
  data: {
    totalPerceptions: number;
    modalityBreakdown: { [key: string]: number };
    averageConfidence: number;
    anomalyRate: number;
    processingLatency: number;
    fusionRate: number;
    dataQuality: number;
  };
}
