/**
 * 渲染系统节点集合
 * 提供材质系统、光照控制、相机管理等渲染相关功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { 
  Material, 
  MeshBasicMaterial, 
  MeshStandardMaterial, 
  MeshPhysicalMaterial,
  Texture, 
  Color, 
  Vector3, 
  Camera, 
  PerspectiveCamera, 
  OrthographicCamera,
  Light,
  DirectionalLight,
  PointLight,
  SpotLight,
  AmbientLight,
  Scene,
  WebGLRenderer
} from 'three';

/**
 * 材质类型枚举
 */
export enum MaterialType {
  BASIC = 'basic',
  STANDARD = 'standard',
  PHYSICAL = 'physical',
  LAMBERT = 'lambert',
  PHONG = 'phong',
  TOON = 'toon',
  SHADER = 'shader',
  CUSTOM = 'custom'
}

/**
 * 光照类型枚举
 */
export enum LightType {
  DIRECTIONAL = 'directional',
  POINT = 'point',
  SPOT = 'spot',
  AMBIENT = 'ambient',
  HEMISPHERE = 'hemisphere',
  AREA = 'area'
}

/**
 * 相机类型枚举
 */
export enum CameraType {
  PERSPECTIVE = 'perspective',
  ORTHOGRAPHIC = 'orthographic'
}

/**
 * 渲染质量枚举
 */
export enum RenderQuality {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  ULTRA = 'ultra'
}

/**
 * 材质配置接口
 */
export interface MaterialConfig {
  type: MaterialType;
  color: Color;
  opacity: number;
  transparent: boolean;
  wireframe: boolean;
  metalness?: number;
  roughness?: number;
  emissive?: Color;
  emissiveIntensity?: number;
  normalScale?: number;
  envMapIntensity?: number;
  clearcoat?: number;
  clearcoatRoughness?: number;
  transmission?: number;
  thickness?: number;
  ior?: number;
}

/**
 * 光照配置接口
 */
export interface LightConfig {
  type: LightType;
  color: Color;
  intensity: number;
  position?: Vector3;
  target?: Vector3;
  distance?: number;
  decay?: number;
  angle?: number;
  penumbra?: number;
  castShadow?: boolean;
  shadowMapSize?: number;
  shadowBias?: number;
  shadowRadius?: number;
}

/**
 * 相机配置接口
 */
export interface CameraConfig {
  type: CameraType;
  position: Vector3;
  target: Vector3;
  fov?: number;
  aspect?: number;
  near?: number;
  far?: number;
  left?: number;
  right?: number;
  top?: number;
  bottom?: number;
  zoom?: number;
}

/**
 * 渲染配置接口
 */
export interface RenderConfig {
  quality: RenderQuality;
  antialias: boolean;
  shadows: boolean;
  shadowType: 'basic' | 'pcf' | 'pcfsoft' | 'vsm';
  toneMapping: 'none' | 'linear' | 'reinhard' | 'cineon' | 'aces';
  toneMappingExposure: number;
  outputEncoding: 'linear' | 'srgb';
  physicallyCorrectLights: boolean;
  gammaFactor: number;
}

/**
 * 渲染管理器
 */
class RenderingManager {
  private materials: Map<string, Material> = new Map();
  private lights: Map<string, Light> = new Map();
  private cameras: Map<string, Camera> = new Map();
  private textures: Map<string, Texture> = new Map();
  private renderer: WebGLRenderer | null = null;
  private scene: Scene | null = null;
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 设置渲染器
   */
  setRenderer(renderer: WebGLRenderer): void {
    this.renderer = renderer;
  }

  /**
   * 设置场景
   */
  setScene(scene: Scene): void {
    this.scene = scene;
  }

  /**
   * 创建材质
   */
  createMaterial(id: string, config: MaterialConfig): Material {
    let material: Material;

    switch (config.type) {
      case MaterialType.BASIC:
        material = new MeshBasicMaterial({
          color: config.color,
          opacity: config.opacity,
          transparent: config.transparent,
          wireframe: config.wireframe
        });
        break;

      case MaterialType.STANDARD:
        material = new MeshStandardMaterial({
          color: config.color,
          opacity: config.opacity,
          transparent: config.transparent,
          wireframe: config.wireframe,
          metalness: config.metalness || 0,
          roughness: config.roughness || 1,
          emissive: config.emissive || new Color(0x000000),
          emissiveIntensity: config.emissiveIntensity || 1,
          envMapIntensity: config.envMapIntensity || 1
        });
        break;

      case MaterialType.PHYSICAL:
        material = new MeshPhysicalMaterial({
          color: config.color,
          opacity: config.opacity,
          transparent: config.transparent,
          wireframe: config.wireframe,
          metalness: config.metalness || 0,
          roughness: config.roughness || 1,
          emissive: config.emissive || new Color(0x000000),
          emissiveIntensity: config.emissiveIntensity || 1,
          clearcoat: config.clearcoat || 0,
          clearcoatRoughness: config.clearcoatRoughness || 0,
          transmission: config.transmission || 0,
          thickness: config.thickness || 0,
          ior: config.ior || 1.5
        });
        break;

      default:
        material = new MeshStandardMaterial({
          color: config.color,
          opacity: config.opacity,
          transparent: config.transparent,
          wireframe: config.wireframe
        });
    }

    this.materials.set(id, material);
    this.emit('materialCreated', { id, material });

    Debug.log('RenderingManager', `材质创建: ${id} (${config.type})`);
    return material;
  }

  /**
   * 创建光源
   */
  createLight(id: string, config: LightConfig): Light {
    let light: Light;

    switch (config.type) {
      case LightType.DIRECTIONAL:
        light = new DirectionalLight(config.color, config.intensity);
        if (config.position) {
          light.position.copy(config.position);
        }
        if (config.target && light instanceof DirectionalLight) {
          light.target.position.copy(config.target);
        }
        break;

      case LightType.POINT:
        light = new PointLight(config.color, config.intensity, config.distance, config.decay);
        if (config.position) {
          light.position.copy(config.position);
        }
        break;

      case LightType.SPOT:
        light = new SpotLight(config.color, config.intensity, config.distance, config.angle, config.penumbra, config.decay);
        if (config.position) {
          light.position.copy(config.position);
        }
        if (config.target && light instanceof SpotLight) {
          light.target.position.copy(config.target);
        }
        break;

      case LightType.AMBIENT:
        light = new AmbientLight(config.color, config.intensity);
        break;

      default:
        light = new DirectionalLight(config.color, config.intensity);
    }

    // 设置阴影
    if (config.castShadow && light instanceof DirectionalLight || light instanceof PointLight || light instanceof SpotLight) {
      light.castShadow = true;
      if (config.shadowMapSize) {
        light.shadow.mapSize.setScalar(config.shadowMapSize);
      }
      if (config.shadowBias !== undefined) {
        light.shadow.bias = config.shadowBias;
      }
      if (config.shadowRadius !== undefined) {
        light.shadow.radius = config.shadowRadius;
      }
    }

    this.lights.set(id, light);
    
    // 添加到场景
    if (this.scene) {
      this.scene.add(light);
      if (light instanceof DirectionalLight || light instanceof SpotLight) {
        this.scene.add(light.target);
      }
    }

    this.emit('lightCreated', { id, light });

    Debug.log('RenderingManager', `光源创建: ${id} (${config.type})`);
    return light;
  }

  /**
   * 创建相机
   */
  createCamera(id: string, config: CameraConfig): Camera {
    let camera: Camera;

    switch (config.type) {
      case CameraType.PERSPECTIVE:
        camera = new PerspectiveCamera(
          config.fov || 75,
          config.aspect || 1,
          config.near || 0.1,
          config.far || 1000
        );
        break;

      case CameraType.ORTHOGRAPHIC:
        camera = new OrthographicCamera(
          config.left || -1,
          config.right || 1,
          config.top || 1,
          config.bottom || -1,
          config.near || 0.1,
          config.far || 1000
        );
        break;

      default:
        camera = new PerspectiveCamera(75, 1, 0.1, 1000);
    }

    camera.position.copy(config.position);
    camera.lookAt(config.target);

    if (config.zoom && camera instanceof OrthographicCamera) {
      camera.zoom = config.zoom;
      camera.updateProjectionMatrix();
    }

    this.cameras.set(id, camera);
    this.emit('cameraCreated', { id, camera });

    Debug.log('RenderingManager', `相机创建: ${id} (${config.type})`);
    return camera;
  }

  /**
   * 配置渲染器
   */
  configureRenderer(config: RenderConfig): void {
    if (!this.renderer) {
      Debug.warn('RenderingManager', '渲染器未设置');
      return;
    }

    // 设置抗锯齿
    // 注意：抗锯齿需要在创建渲染器时设置，这里只是记录配置

    // 设置阴影
    this.renderer.shadowMap.enabled = config.shadows;
    // 注意：这里使用数字常量，实际应该导入THREE的常量
    switch (config.shadowType) {
      case 'basic':
        this.renderer.shadowMap.type = 0; // THREE.BasicShadowMap
        break;
      case 'pcf':
        this.renderer.shadowMap.type = 1; // THREE.PCFShadowMap
        break;
      case 'pcfsoft':
        this.renderer.shadowMap.type = 2; // THREE.PCFSoftShadowMap
        break;
      case 'vsm':
        this.renderer.shadowMap.type = 3; // THREE.VSMShadowMap
        break;
    }

    // 设置色调映射
    switch (config.toneMapping) {
      case 'none':
        this.renderer.toneMapping = 0; // NoToneMapping
        break;
      case 'linear':
        this.renderer.toneMapping = 1; // LinearToneMapping
        break;
      case 'reinhard':
        this.renderer.toneMapping = 2; // ReinhardToneMapping
        break;
      case 'cineon':
        this.renderer.toneMapping = 3; // CineonToneMapping
        break;
      case 'aces':
        this.renderer.toneMapping = 4; // ACESFilmicToneMapping
        break;
    }

    this.renderer.toneMappingExposure = config.toneMappingExposure;

    // 设置输出编码
    switch (config.outputEncoding) {
      case 'linear':
        this.renderer.outputEncoding = 3000; // LinearEncoding
        break;
      case 'srgb':
        this.renderer.outputEncoding = 3001; // sRGBEncoding
        break;
    }

    // 设置物理正确光照
    (this.renderer as any).physicallyCorrectLights = config.physicallyCorrectLights;

    this.emit('rendererConfigured', { config });

    Debug.log('RenderingManager', `渲染器配置完成: ${config.quality}`);
  }

  /**
   * 获取材质
   */
  getMaterial(id: string): Material | undefined {
    return this.materials.get(id);
  }

  /**
   * 获取光源
   */
  getLight(id: string): Light | undefined {
    return this.lights.get(id);
  }

  /**
   * 获取相机
   */
  getCamera(id: string): Camera | undefined {
    return this.cameras.get(id);
  }

  /**
   * 更新材质属性
   */
  updateMaterial(id: string, properties: Partial<MaterialConfig>): boolean {
    const material = this.materials.get(id);
    if (!material) {
      return false;
    }

    // 更新通用属性
    if (properties.color) {
      (material as any).color = properties.color;
    }
    if (properties.opacity !== undefined) {
      material.opacity = properties.opacity;
    }
    if (properties.transparent !== undefined) {
      material.transparent = properties.transparent;
    }
    if (properties.wireframe !== undefined) {
      (material as any).wireframe = properties.wireframe;
    }

    // 更新PBR属性
    if (material instanceof MeshStandardMaterial || material instanceof MeshPhysicalMaterial) {
      if (properties.metalness !== undefined) {
        material.metalness = properties.metalness;
      }
      if (properties.roughness !== undefined) {
        material.roughness = properties.roughness;
      }
      if (properties.emissive) {
        material.emissive = properties.emissive;
      }
      if (properties.emissiveIntensity !== undefined) {
        material.emissiveIntensity = properties.emissiveIntensity;
      }
    }

    material.needsUpdate = true;
    this.emit('materialUpdated', { id, material, properties });

    Debug.log('RenderingManager', `材质更新: ${id}`);
    return true;
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('RenderingManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    // 清理材质
    for (const material of this.materials.values()) {
      material.dispose();
    }
    this.materials.clear();

    // 清理光源
    for (const light of this.lights.values()) {
      if (this.scene) {
        this.scene.remove(light);
      }
    }
    this.lights.clear();

    // 清理相机
    this.cameras.clear();

    // 清理纹理
    for (const texture of this.textures.values()) {
      texture.dispose();
    }
    this.textures.clear();

    this.eventListeners.clear();
  }
}

/**
 * 材质系统节点
 */
export class MaterialSystemNode extends VisualScriptNode {
  public static readonly TYPE = 'MaterialSystem';
  public static readonly NAME = '材质系统';
  public static readonly DESCRIPTION = '创建和管理材质';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = MaterialSystemNode.TYPE, name: string = MaterialSystemNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建材质');
    this.addInput('update', 'trigger', '更新材质');
    this.addInput('materialId', 'string', '材质ID');
    this.addInput('materialType', 'string', '材质类型');
    this.addInput('color', 'object', '颜色');
    this.addInput('opacity', 'number', '透明度');
    this.addInput('transparent', 'boolean', '透明');
    this.addInput('wireframe', 'boolean', '线框模式');
    this.addInput('metalness', 'number', '金属度');
    this.addInput('roughness', 'number', '粗糙度');
    this.addInput('emissive', 'object', '自发光颜色');
    this.addInput('emissiveIntensity', 'number', '自发光强度');

    // 输出端口
    this.addOutput('material', 'object', '材质对象');
    this.addOutput('materialId', 'string', '材质ID');
    this.addOutput('materialType', 'string', '材质类型');
    this.addOutput('onCreated', 'trigger', '材质创建完成');
    this.addOutput('onUpdated', 'trigger', '材质更新完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.update;

      if (createTrigger) {
        return this.createMaterial(inputs);
      } else if (updateTrigger) {
        return this.updateMaterial(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('MaterialSystemNode', '材质操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createMaterial(inputs: any): any {
    const materialId = inputs?.materialId as string || this.generateMaterialId();
    const materialType = inputs?.materialType as string || 'standard';
    const color = inputs?.color as Color || new Color(0xffffff);
    const opacity = inputs?.opacity as number ?? 1.0;
    const transparent = inputs?.transparent as boolean ?? false;
    const wireframe = inputs?.wireframe as boolean ?? false;
    const metalness = inputs?.metalness as number ?? 0.0;
    const roughness = inputs?.roughness as number ?? 1.0;
    const emissive = inputs?.emissive as Color || new Color(0x000000);
    const emissiveIntensity = inputs?.emissiveIntensity as number ?? 1.0;

    const config: MaterialConfig = {
      type: materialType as MaterialType,
      color,
      opacity,
      transparent,
      wireframe,
      metalness,
      roughness,
      emissive,
      emissiveIntensity
    };

    const material = MaterialSystemNode.renderingManager.createMaterial(materialId, config);

    Debug.log('MaterialSystemNode', `材质创建成功: ${materialId} (${materialType})`);

    return {
      material,
      materialId,
      materialType,
      onCreated: true,
      onUpdated: false,
      onError: false
    };
  }

  private updateMaterial(inputs: any): any {
    const materialId = inputs?.materialId as string;

    if (!materialId) {
      throw new Error('未提供材质ID');
    }

    const properties: Partial<MaterialConfig> = {};

    if (inputs?.color) properties.color = inputs.color;
    if (inputs?.opacity !== undefined) properties.opacity = inputs.opacity;
    if (inputs?.transparent !== undefined) properties.transparent = inputs.transparent;
    if (inputs?.wireframe !== undefined) properties.wireframe = inputs.wireframe;
    if (inputs?.metalness !== undefined) properties.metalness = inputs.metalness;
    if (inputs?.roughness !== undefined) properties.roughness = inputs.roughness;
    if (inputs?.emissive) properties.emissive = inputs.emissive;
    if (inputs?.emissiveIntensity !== undefined) properties.emissiveIntensity = inputs.emissiveIntensity;

    const success = MaterialSystemNode.renderingManager.updateMaterial(materialId, properties);

    if (!success) {
      throw new Error('材质更新失败');
    }

    const material = MaterialSystemNode.renderingManager.getMaterial(materialId);

    Debug.log('MaterialSystemNode', `材质更新成功: ${materialId}`);

    return {
      material,
      materialId,
      materialType: (material as any)?.type || 'unknown',
      onCreated: false,
      onUpdated: true,
      onError: false
    };
  }

  private generateMaterialId(): string {
    return 'mat_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      material: null,
      materialId: '',
      materialType: '',
      onCreated: false,
      onUpdated: false,
      onError: false
    };
  }
}

/**
 * 光照控制节点
 */
export class LightControlNode extends VisualScriptNode {
  public static readonly TYPE = 'LightControl';
  public static readonly NAME = '光照控制';
  public static readonly DESCRIPTION = '创建和管理光源';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = LightControlNode.TYPE, name: string = LightControlNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建光源');
    this.addInput('update', 'trigger', '更新光源');
    this.addInput('lightId', 'string', '光源ID');
    this.addInput('lightType', 'string', '光源类型');
    this.addInput('color', 'object', '光源颜色');
    this.addInput('intensity', 'number', '光源强度');
    this.addInput('position', 'object', '光源位置');
    this.addInput('target', 'object', '目标位置');
    this.addInput('distance', 'number', '光照距离');
    this.addInput('angle', 'number', '光照角度');
    this.addInput('penumbra', 'number', '半影');
    this.addInput('castShadow', 'boolean', '投射阴影');

    // 输出端口
    this.addOutput('light', 'object', '光源对象');
    this.addOutput('lightId', 'string', '光源ID');
    this.addOutput('lightType', 'string', '光源类型');
    this.addOutput('onCreated', 'trigger', '光源创建完成');
    this.addOutput('onUpdated', 'trigger', '光源更新完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.update;

      if (createTrigger) {
        return this.createLight(inputs);
      } else if (updateTrigger) {
        return this.updateLight(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('LightControlNode', '光照操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createLight(inputs: any): any {
    const lightId = inputs?.lightId as string || this.generateLightId();
    const lightType = inputs?.lightType as string || 'directional';
    const color = inputs?.color as Color || new Color(0xffffff);
    const intensity = inputs?.intensity as number ?? 1.0;
    const position = inputs?.position as Vector3 || new Vector3(0, 10, 0);
    const target = inputs?.target as Vector3 || new Vector3(0, 0, 0);
    const distance = inputs?.distance as number ?? 0;
    const angle = inputs?.angle as number ?? Math.PI / 3;
    const penumbra = inputs?.penumbra as number ?? 0;
    const castShadow = inputs?.castShadow as boolean ?? false;

    const config: LightConfig = {
      type: lightType as LightType,
      color,
      intensity,
      position,
      target,
      distance,
      angle,
      penumbra,
      castShadow,
      shadowMapSize: 1024,
      shadowBias: -0.0001,
      shadowRadius: 1
    };

    const light = LightControlNode.renderingManager.createLight(lightId, config);

    Debug.log('LightControlNode', `光源创建成功: ${lightId} (${lightType})`);

    return {
      light,
      lightId,
      lightType,
      onCreated: true,
      onUpdated: false,
      onError: false
    };
  }

  private updateLight(inputs: any): any {
    const lightId = inputs?.lightId as string;

    if (!lightId) {
      throw new Error('未提供光源ID');
    }

    const light = LightControlNode.renderingManager.getLight(lightId);

    if (!light) {
      throw new Error('光源不存在');
    }

    // 更新光源属性
    if (inputs?.color) {
      light.color = inputs.color;
    }
    if (inputs?.intensity !== undefined) {
      light.intensity = inputs.intensity;
    }
    if (inputs?.position) {
      light.position.copy(inputs.position);
    }
    if (inputs?.target && (light instanceof DirectionalLight || light instanceof SpotLight)) {
      light.target.position.copy(inputs.target);
    }
    if (inputs?.distance !== undefined && (light instanceof PointLight || light instanceof SpotLight)) {
      (light as PointLight | SpotLight).distance = inputs.distance;
    }
    if (inputs?.angle !== undefined && light instanceof SpotLight) {
      light.angle = inputs.angle;
    }
    if (inputs?.penumbra !== undefined && light instanceof SpotLight) {
      light.penumbra = inputs.penumbra;
    }
    if (inputs?.castShadow !== undefined) {
      light.castShadow = inputs.castShadow;
    }

    Debug.log('LightControlNode', `光源更新成功: ${lightId}`);

    return {
      light,
      lightId,
      lightType: light.type,
      onCreated: false,
      onUpdated: true,
      onError: false
    };
  }

  private generateLightId(): string {
    return 'light_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      light: null,
      lightId: '',
      lightType: '',
      onCreated: false,
      onUpdated: false,
      onError: false
    };
  }
}

/**
 * 相机管理节点
 */
export class CameraManagerNode extends VisualScriptNode {
  public static readonly TYPE = 'CameraManager';
  public static readonly NAME = '相机管理';
  public static readonly DESCRIPTION = '创建和管理相机';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = CameraManagerNode.TYPE, name: string = CameraManagerNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建相机');
    this.addInput('update', 'trigger', '更新相机');
    this.addInput('lookAt', 'trigger', '看向目标');
    this.addInput('cameraId', 'string', '相机ID');
    this.addInput('cameraType', 'string', '相机类型');
    this.addInput('position', 'object', '相机位置');
    this.addInput('target', 'object', '目标位置');
    this.addInput('fov', 'number', '视野角度');
    this.addInput('aspect', 'number', '宽高比');
    this.addInput('near', 'number', '近裁剪面');
    this.addInput('far', 'number', '远裁剪面');
    this.addInput('zoom', 'number', '缩放');

    // 输出端口
    this.addOutput('camera', 'object', '相机对象');
    this.addOutput('cameraId', 'string', '相机ID');
    this.addOutput('cameraType', 'string', '相机类型');
    this.addOutput('position', 'object', '相机位置');
    this.addOutput('target', 'object', '目标位置');
    this.addOutput('onCreated', 'trigger', '相机创建完成');
    this.addOutput('onUpdated', 'trigger', '相机更新完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.update;
      const lookAtTrigger = inputs?.lookAt;

      if (createTrigger) {
        return this.createCamera(inputs);
      } else if (updateTrigger) {
        return this.updateCamera(inputs);
      } else if (lookAtTrigger) {
        return this.lookAtTarget(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('CameraManagerNode', '相机操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createCamera(inputs: any): any {
    const cameraId = inputs?.cameraId as string || this.generateCameraId();
    const cameraType = inputs?.cameraType as string || 'perspective';
    const position = inputs?.position as Vector3 || new Vector3(0, 0, 5);
    const target = inputs?.target as Vector3 || new Vector3(0, 0, 0);
    const fov = inputs?.fov as number ?? 75;
    const aspect = inputs?.aspect as number ?? 1;
    const near = inputs?.near as number ?? 0.1;
    const far = inputs?.far as number ?? 1000;
    const zoom = inputs?.zoom as number ?? 1;

    const config: CameraConfig = {
      type: cameraType as CameraType,
      position,
      target,
      fov,
      aspect,
      near,
      far,
      zoom
    };

    const camera = CameraManagerNode.renderingManager.createCamera(cameraId, config);

    Debug.log('CameraManagerNode', `相机创建成功: ${cameraId} (${cameraType})`);

    return {
      camera,
      cameraId,
      cameraType,
      position: camera.position,
      target,
      onCreated: true,
      onUpdated: false,
      onError: false
    };
  }

  private updateCamera(inputs: any): any {
    const cameraId = inputs?.cameraId as string;

    if (!cameraId) {
      throw new Error('未提供相机ID');
    }

    const camera = CameraManagerNode.renderingManager.getCamera(cameraId);

    if (!camera) {
      throw new Error('相机不存在');
    }

    // 更新相机属性
    if (inputs?.position) {
      camera.position.copy(inputs.position);
    }

    if (camera instanceof PerspectiveCamera) {
      if (inputs?.fov !== undefined) {
        camera.fov = inputs.fov;
        camera.updateProjectionMatrix();
      }
      if (inputs?.aspect !== undefined) {
        camera.aspect = inputs.aspect;
        camera.updateProjectionMatrix();
      }
    }

    if (camera instanceof OrthographicCamera) {
      if (inputs?.zoom !== undefined) {
        camera.zoom = inputs.zoom;
        camera.updateProjectionMatrix();
      }
    }

    if (inputs?.near !== undefined) {
      (camera as any).near = inputs.near;
      if (camera instanceof PerspectiveCamera || camera instanceof OrthographicCamera) {
        camera.updateProjectionMatrix();
      }
    }
    if (inputs?.far !== undefined) {
      (camera as any).far = inputs.far;
      if (camera instanceof PerspectiveCamera || camera instanceof OrthographicCamera) {
        camera.updateProjectionMatrix();
      }
    }

    Debug.log('CameraManagerNode', `相机更新成功: ${cameraId}`);

    return {
      camera,
      cameraId,
      cameraType: camera.type,
      position: camera.position,
      target: inputs?.target || new Vector3(0, 0, 0),
      onCreated: false,
      onUpdated: true,
      onError: false
    };
  }

  private lookAtTarget(inputs: any): any {
    const cameraId = inputs?.cameraId as string;
    const target = inputs?.target as Vector3;

    if (!cameraId) {
      throw new Error('未提供相机ID');
    }

    if (!target) {
      throw new Error('未提供目标位置');
    }

    const camera = CameraManagerNode.renderingManager.getCamera(cameraId);

    if (!camera) {
      throw new Error('相机不存在');
    }

    camera.lookAt(target);

    Debug.log('CameraManagerNode', `相机朝向更新: ${cameraId}`);

    return {
      camera,
      cameraId,
      cameraType: camera.type,
      position: camera.position,
      target,
      onCreated: false,
      onUpdated: true,
      onError: false
    };
  }

  private generateCameraId(): string {
    return 'cam_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      camera: null,
      cameraId: '',
      cameraType: '',
      position: null,
      target: null,
      onCreated: false,
      onUpdated: false,
      onError: false
    };
  }
}

/**
 * 渲染配置节点
 */
export class RenderConfigNode extends VisualScriptNode {
  public static readonly TYPE = 'RenderConfig';
  public static readonly NAME = '渲染配置';
  public static readonly DESCRIPTION = '配置渲染器设置';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = RenderConfigNode.TYPE, name: string = RenderConfigNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('configure', 'trigger', '应用配置');
    this.addInput('quality', 'string', '渲染质量');
    this.addInput('antialias', 'boolean', '抗锯齿');
    this.addInput('shadows', 'boolean', '阴影');
    this.addInput('shadowType', 'string', '阴影类型');
    this.addInput('toneMapping', 'string', '色调映射');
    this.addInput('toneMappingExposure', 'number', '曝光度');
    this.addInput('outputEncoding', 'string', '输出编码');
    this.addInput('physicallyCorrectLights', 'boolean', '物理正确光照');

    // 输出端口
    this.addOutput('config', 'object', '渲染配置');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('onConfigured', 'trigger', '配置完成');
    this.addOutput('onError', 'trigger', '配置失败');
  }

  public execute(inputs?: any): any {
    try {
      const configureTrigger = inputs?.configure;
      if (!configureTrigger) {
        return this.getDefaultOutputs();
      }

      const quality = inputs?.quality as string || 'medium';
      const antialias = inputs?.antialias as boolean ?? true;
      const shadows = inputs?.shadows as boolean ?? true;
      const shadowType = inputs?.shadowType as string || 'pcf';
      const toneMapping = inputs?.toneMapping as string || 'aces';
      const toneMappingExposure = inputs?.toneMappingExposure as number ?? 1.0;
      const outputEncoding = inputs?.outputEncoding as string || 'srgb';
      const physicallyCorrectLights = inputs?.physicallyCorrectLights as boolean ?? true;

      const config: RenderConfig = {
        quality: quality as RenderQuality,
        antialias,
        shadows,
        shadowType: shadowType as any,
        toneMapping: toneMapping as any,
        toneMappingExposure,
        outputEncoding: outputEncoding as any,
        physicallyCorrectLights,
        gammaFactor: 2.2
      };

      RenderConfigNode.renderingManager.configureRenderer(config);

      Debug.log('RenderConfigNode', `渲染配置应用成功: ${quality}`);

      return {
        config,
        quality,
        onConfigured: true,
        onError: false
      };

    } catch (error) {
      Debug.error('RenderConfigNode', '渲染配置失败', error);
      return {
        config: null,
        quality: '',
        onConfigured: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      config: null,
      quality: '',
      onConfigured: false,
      onError: false
    };
  }
}

// ==================== 批次1.1：材质管理节点 ====================

/**
 * 创建材质节点
 */
export class CreateMaterialNode extends VisualScriptNode {
  public static readonly TYPE = 'CreateMaterial';
  public static readonly NAME = '创建材质';
  public static readonly DESCRIPTION = '创建新的材质对象';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = CreateMaterialNode.TYPE, name: string = CreateMaterialNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建');
    this.addInput('materialId', 'string', '材质ID');
    this.addInput('materialType', 'string', '材质类型');
    this.addInput('name', 'string', '材质名称');

    // 输出端口
    this.addOutput('material', 'object', '材质对象');
    this.addOutput('materialId', 'string', '材质ID');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onError', 'trigger', '创建失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      if (!createTrigger) {
        return this.getDefaultOutputs();
      }

      const materialId = inputs?.materialId as string || this.generateMaterialId();
      const materialType = inputs?.materialType as string || 'standard';
      const name = inputs?.name as string || `Material_${materialId}`;

      const config: MaterialConfig = {
        type: materialType as MaterialType,
        color: new Color(0xffffff),
        opacity: 1.0,
        transparent: false,
        wireframe: false,
        metalness: 0.0,
        roughness: 1.0,
        emissive: new Color(0x000000),
        emissiveIntensity: 1.0
      };

      const material = CreateMaterialNode.renderingManager.createMaterial(materialId, config);
      (material as any).name = name;

      Debug.log('CreateMaterialNode', `材质创建成功: ${materialId}`);

      return {
        material,
        materialId,
        onCreated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('CreateMaterialNode', '材质创建失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private generateMaterialId(): string {
    return 'mat_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      material: null,
      materialId: '',
      onCreated: false,
      onError: false
    };
  }
}

/**
 * 设置材质属性节点
 */
export class SetMaterialPropertyNode extends VisualScriptNode {
  public static readonly TYPE = 'SetMaterialProperty';
  public static readonly NAME = '设置材质属性';
  public static readonly DESCRIPTION = '设置材质的各种属性';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = SetMaterialPropertyNode.TYPE, name: string = SetMaterialPropertyNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('set', 'trigger', '设置');
    this.addInput('materialId', 'string', '材质ID');
    this.addInput('property', 'string', '属性名称');
    this.addInput('value', 'any', '属性值');
    this.addInput('color', 'object', '颜色');
    this.addInput('opacity', 'number', '透明度');
    this.addInput('metalness', 'number', '金属度');
    this.addInput('roughness', 'number', '粗糙度');

    // 输出端口
    this.addOutput('material', 'object', '材质对象');
    this.addOutput('success', 'boolean', '设置成功');
    this.addOutput('onSet', 'trigger', '设置完成');
    this.addOutput('onError', 'trigger', '设置失败');
  }

  public execute(inputs?: any): any {
    try {
      const setTrigger = inputs?.set;
      if (!setTrigger) {
        return this.getDefaultOutputs();
      }

      const materialId = inputs?.materialId as string;
      if (!materialId) {
        throw new Error('未提供材质ID');
      }

      const material = SetMaterialPropertyNode.renderingManager.getMaterial(materialId);
      if (!material) {
        throw new Error('材质不存在');
      }

      // 设置属性
      const property = inputs?.property as string;
      const value = inputs?.value;

      if (property && value !== undefined) {
        this.setMaterialProperty(material, property, value);
      } else {
        // 批量设置属性
        if (inputs?.color) this.setMaterialProperty(material, 'color', inputs.color);
        if (inputs?.opacity !== undefined) this.setMaterialProperty(material, 'opacity', inputs.opacity);
        if (inputs?.metalness !== undefined) this.setMaterialProperty(material, 'metalness', inputs.metalness);
        if (inputs?.roughness !== undefined) this.setMaterialProperty(material, 'roughness', inputs.roughness);
      }

      material.needsUpdate = true;

      Debug.log('SetMaterialPropertyNode', `材质属性设置成功: ${materialId}`);

      return {
        material,
        success: true,
        onSet: true,
        onError: false
      };

    } catch (error) {
      Debug.error('SetMaterialPropertyNode', '材质属性设置失败', error);
      return {
        material: null,
        success: false,
        onSet: false,
        onError: true
      };
    }
  }

  private setMaterialProperty(material: Material, property: string, value: any): void {
    switch (property) {
      case 'color':
        if (value instanceof Color) {
          (material as any).color = value;
        }
        break;
      case 'opacity':
        material.opacity = Math.max(0, Math.min(1, value));
        break;
      case 'transparent':
        material.transparent = Boolean(value);
        break;
      case 'wireframe':
        (material as any).wireframe = Boolean(value);
        break;
      case 'metalness':
        if (material instanceof MeshStandardMaterial || material instanceof MeshPhysicalMaterial) {
          material.metalness = Math.max(0, Math.min(1, value));
        }
        break;
      case 'roughness':
        if (material instanceof MeshStandardMaterial || material instanceof MeshPhysicalMaterial) {
          material.roughness = Math.max(0, Math.min(1, value));
        }
        break;
      case 'emissive':
        if (material instanceof MeshStandardMaterial || material instanceof MeshPhysicalMaterial) {
          material.emissive = value instanceof Color ? value : new Color(value);
        }
        break;
      case 'emissiveIntensity':
        if (material instanceof MeshStandardMaterial || material instanceof MeshPhysicalMaterial) {
          material.emissiveIntensity = Math.max(0, value);
        }
        break;
      default:
        Debug.warn('SetMaterialPropertyNode', `未知的材质属性: ${property}`);
    }
  }

  private getDefaultOutputs(): any {
    return {
      material: null,
      success: false,
      onSet: false,
      onError: false
    };
  }
}

/**
 * 获取材质属性节点
 */
export class GetMaterialPropertyNode extends VisualScriptNode {
  public static readonly TYPE = 'GetMaterialProperty';
  public static readonly NAME = '获取材质属性';
  public static readonly DESCRIPTION = '获取材质的各种属性值';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = GetMaterialPropertyNode.TYPE, name: string = GetMaterialPropertyNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('get', 'trigger', '获取');
    this.addInput('materialId', 'string', '材质ID');
    this.addInput('property', 'string', '属性名称');

    // 输出端口
    this.addOutput('value', 'any', '属性值');
    this.addOutput('color', 'object', '颜色');
    this.addOutput('opacity', 'number', '透明度');
    this.addOutput('metalness', 'number', '金属度');
    this.addOutput('roughness', 'number', '粗糙度');
    this.addOutput('onGet', 'trigger', '获取完成');
    this.addOutput('onError', 'trigger', '获取失败');
  }

  public execute(inputs?: any): any {
    try {
      const getTrigger = inputs?.get;
      if (!getTrigger) {
        return this.getDefaultOutputs();
      }

      const materialId = inputs?.materialId as string;
      if (!materialId) {
        throw new Error('未提供材质ID');
      }

      const material = GetMaterialPropertyNode.renderingManager.getMaterial(materialId);
      if (!material) {
        throw new Error('材质不存在');
      }

      const property = inputs?.property as string;
      let value: any = null;

      if (property) {
        value = this.getMaterialProperty(material, property);
      }

      Debug.log('GetMaterialPropertyNode', `材质属性获取成功: ${materialId}.${property}`);

      return {
        value,
        color: (material as any).color || null,
        opacity: material.opacity,
        metalness: (material as any).metalness || 0,
        roughness: (material as any).roughness || 1,
        onGet: true,
        onError: false
      };

    } catch (error) {
      Debug.error('GetMaterialPropertyNode', '材质属性获取失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getMaterialProperty(material: Material, property: string): any {
    switch (property) {
      case 'color':
        return (material as any).color;
      case 'opacity':
        return material.opacity;
      case 'transparent':
        return material.transparent;
      case 'wireframe':
        return (material as any).wireframe;
      case 'metalness':
        return (material as any).metalness || 0;
      case 'roughness':
        return (material as any).roughness || 1;
      case 'emissive':
        return (material as any).emissive;
      case 'emissiveIntensity':
        return (material as any).emissiveIntensity || 1;
      case 'type':
        return material.type;
      case 'name':
        return (material as any).name;
      default:
        Debug.warn('GetMaterialPropertyNode', `未知的材质属性: ${property}`);
        return null;
    }
  }

  private getDefaultOutputs(): any {
    return {
      value: null,
      color: null,
      opacity: 1,
      metalness: 0,
      roughness: 1,
      onGet: false,
      onError: false
    };
  }
}

/**
 * 材质混合节点
 */
export class MaterialBlendNode extends VisualScriptNode {
  public static readonly TYPE = 'MaterialBlend';
  public static readonly NAME = '材质混合';
  public static readonly DESCRIPTION = '混合两个材质创建新材质';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = MaterialBlendNode.TYPE, name: string = MaterialBlendNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('blend', 'trigger', '混合');
    this.addInput('materialA', 'string', '材质A ID');
    this.addInput('materialB', 'string', '材质B ID');
    this.addInput('blendFactor', 'number', '混合因子');
    this.addInput('blendMode', 'string', '混合模式');
    this.addInput('outputId', 'string', '输出材质ID');

    // 输出端口
    this.addOutput('blendedMaterial', 'object', '混合材质');
    this.addOutput('materialId', 'string', '材质ID');
    this.addOutput('onBlended', 'trigger', '混合完成');
    this.addOutput('onError', 'trigger', '混合失败');
  }

  public execute(inputs?: any): any {
    try {
      const blendTrigger = inputs?.blend;
      if (!blendTrigger) {
        return this.getDefaultOutputs();
      }

      const materialAId = inputs?.materialA as string;
      const materialBId = inputs?.materialB as string;
      const blendFactor = Math.max(0, Math.min(1, inputs?.blendFactor as number || 0.5));
      const blendMode = inputs?.blendMode as string || 'linear';
      const outputId = inputs?.outputId as string || this.generateMaterialId();

      if (!materialAId || !materialBId) {
        throw new Error('需要提供两个材质ID');
      }

      const materialA = MaterialBlendNode.renderingManager.getMaterial(materialAId);
      const materialB = MaterialBlendNode.renderingManager.getMaterial(materialBId);

      if (!materialA || !materialB) {
        throw new Error('材质不存在');
      }

      // 创建混合材质
      const blendedMaterial = this.blendMaterials(materialA, materialB, blendFactor, blendMode, outputId);

      Debug.log('MaterialBlendNode', `材质混合成功: ${materialAId} + ${materialBId} = ${outputId}`);

      return {
        blendedMaterial,
        materialId: outputId,
        onBlended: true,
        onError: false
      };

    } catch (error) {
      Debug.error('MaterialBlendNode', '材质混合失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private blendMaterials(materialA: Material, materialB: Material, factor: number, mode: string, outputId: string): Material {
    // 创建新的标准材质作为混合结果
    const colorA = (materialA as any).color || new Color(0xffffff);
    const colorB = (materialB as any).color || new Color(0xffffff);

    let blendedColor: Color;

    switch (mode) {
      case 'linear':
        blendedColor = colorA.clone().lerp(colorB, factor);
        break;
      case 'multiply':
        blendedColor = new Color(
          colorA.r * colorB.r,
          colorA.g * colorB.g,
          colorA.b * colorB.b
        );
        break;
      case 'screen':
        blendedColor = new Color(
          1 - (1 - colorA.r) * (1 - colorB.r),
          1 - (1 - colorA.g) * (1 - colorB.g),
          1 - (1 - colorA.b) * (1 - colorB.b)
        );
        break;
      case 'overlay':
        blendedColor = new Color(
          colorA.r < 0.5 ? 2 * colorA.r * colorB.r : 1 - 2 * (1 - colorA.r) * (1 - colorB.r),
          colorA.g < 0.5 ? 2 * colorA.g * colorB.g : 1 - 2 * (1 - colorA.g) * (1 - colorB.g),
          colorA.b < 0.5 ? 2 * colorA.b * colorB.b : 1 - 2 * (1 - colorA.b) * (1 - colorB.b)
        );
        break;
      default:
        blendedColor = colorA.clone().lerp(colorB, factor);
    }

    const config: MaterialConfig = {
      type: MaterialType.STANDARD,
      color: blendedColor,
      opacity: this.lerp(materialA.opacity, materialB.opacity, factor),
      transparent: materialA.transparent || materialB.transparent,
      wireframe: factor > 0.5 ? (materialB as any).wireframe : (materialA as any).wireframe,
      metalness: this.lerp((materialA as any).metalness || 0, (materialB as any).metalness || 0, factor),
      roughness: this.lerp((materialA as any).roughness || 1, (materialB as any).roughness || 1, factor),
      emissive: ((materialA as any).emissive || new Color(0)).clone().lerp((materialB as any).emissive || new Color(0), factor),
      emissiveIntensity: this.lerp((materialA as any).emissiveIntensity || 1, (materialB as any).emissiveIntensity || 1, factor)
    };

    return MaterialBlendNode.renderingManager.createMaterial(outputId, config);
  }

  private lerp(a: number, b: number, t: number): number {
    return a + (b - a) * t;
  }

  private generateMaterialId(): string {
    return 'blended_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      blendedMaterial: null,
      materialId: '',
      onBlended: false,
      onError: false
    };
  }
}

/**
 * 材质动画节点
 */
export class MaterialAnimationNode extends VisualScriptNode {
  public static readonly TYPE = 'MaterialAnimation';
  public static readonly NAME = '材质动画';
  public static readonly DESCRIPTION = '为材质属性创建动画效果';

  private static renderingManager: RenderingManager = new RenderingManager();
  private animationId: number | null = null;
  private startTime: number = 0;

  constructor(nodeType: string = MaterialAnimationNode.TYPE, name: string = MaterialAnimationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('start', 'trigger', '开始动画');
    this.addInput('stop', 'trigger', '停止动画');
    this.addInput('materialId', 'string', '材质ID');
    this.addInput('property', 'string', '动画属性');
    this.addInput('fromValue', 'any', '起始值');
    this.addInput('toValue', 'any', '结束值');
    this.addInput('duration', 'number', '持续时间(秒)');
    this.addInput('easing', 'string', '缓动函数');
    this.addInput('loop', 'boolean', '循环播放');

    // 输出端口
    this.addOutput('currentValue', 'any', '当前值');
    this.addOutput('progress', 'number', '进度');
    this.addOutput('isPlaying', 'boolean', '正在播放');
    this.addOutput('onStarted', 'trigger', '动画开始');
    this.addOutput('onCompleted', 'trigger', '动画完成');
    this.addOutput('onStopped', 'trigger', '动画停止');
    this.addOutput('onError', 'trigger', '动画错误');
  }

  public execute(inputs?: any): any {
    try {
      const startTrigger = inputs?.start;
      const stopTrigger = inputs?.stop;

      if (startTrigger) {
        return this.startAnimation(inputs);
      } else if (stopTrigger) {
        return this.stopAnimation();
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('MaterialAnimationNode', '材质动画操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private startAnimation(inputs: any): any {
    const materialId = inputs?.materialId as string;
    const property = inputs?.property as string;
    const fromValue = inputs?.fromValue;
    const toValue = inputs?.toValue;
    const duration = Math.max(0.1, inputs?.duration as number || 1.0);
    const easing = inputs?.easing as string || 'linear';
    const loop = inputs?.loop as boolean || false;

    if (!materialId || !property || fromValue === undefined || toValue === undefined) {
      throw new Error('缺少必要的动画参数');
    }

    const material = MaterialAnimationNode.renderingManager.getMaterial(materialId);
    if (!material) {
      throw new Error('材质不存在');
    }

    // 停止现有动画
    if (this.animationId !== null) {
      cancelAnimationFrame(this.animationId);
    }

    this.startTime = performance.now();

    const animate = () => {
      const currentTime = performance.now();
      const elapsed = (currentTime - this.startTime) / 1000; // 转换为秒
      let progress = Math.min(elapsed / duration, 1);

      // 应用缓动函数
      progress = this.applyEasing(progress, easing);

      // 计算当前值
      const currentValue = this.interpolateValue(fromValue, toValue, progress);

      // 设置材质属性
      this.setMaterialProperty(material, property, currentValue);

      if (elapsed >= duration) {
        if (loop) {
          this.startTime = currentTime;
          this.animationId = requestAnimationFrame(animate);
        } else {
          this.animationId = null;
          return {
            currentValue,
            progress: 1,
            isPlaying: false,
            onCompleted: true,
            onStarted: false,
            onStopped: false,
            onError: false
          };
        }
      } else {
        this.animationId = requestAnimationFrame(animate);
      }
    };

    this.animationId = requestAnimationFrame(animate);

    Debug.log('MaterialAnimationNode', `材质动画开始: ${materialId}.${property}`);

    return {
      currentValue: fromValue,
      progress: 0,
      isPlaying: true,
      onStarted: true,
      onCompleted: false,
      onStopped: false,
      onError: false
    };
  }

  private stopAnimation(): any {
    if (this.animationId !== null) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;

      Debug.log('MaterialAnimationNode', '材质动画停止');

      return {
        currentValue: null,
        progress: 0,
        isPlaying: false,
        onStarted: false,
        onCompleted: false,
        onStopped: true,
        onError: false
      };
    }

    return this.getDefaultOutputs();
  }

  private applyEasing(t: number, easing: string): number {
    switch (easing) {
      case 'linear':
        return t;
      case 'easeIn':
        return t * t;
      case 'easeOut':
        return 1 - (1 - t) * (1 - t);
      case 'easeInOut':
        return t < 0.5 ? 2 * t * t : 1 - 2 * (1 - t) * (1 - t);
      case 'bounce':
        if (t < 1 / 2.75) {
          return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
          return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        } else if (t < 2.5 / 2.75) {
          return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        } else {
          return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
      default:
        return t;
    }
  }

  private interpolateValue(from: any, to: any, t: number): any {
    if (typeof from === 'number' && typeof to === 'number') {
      return from + (to - from) * t;
    } else if (from instanceof Color && to instanceof Color) {
      return from.clone().lerp(to, t);
    } else if (from instanceof Vector3 && to instanceof Vector3) {
      return from.clone().lerp(to, t);
    }
    return t < 0.5 ? from : to;
  }

  private setMaterialProperty(material: Material, property: string, value: any): void {
    switch (property) {
      case 'color':
        (material as any).color = value;
        break;
      case 'opacity':
        material.opacity = value;
        break;
      case 'metalness':
        (material as any).metalness = value;
        break;
      case 'roughness':
        (material as any).roughness = value;
        break;
      case 'emissiveIntensity':
        (material as any).emissiveIntensity = value;
        break;
    }
    material.needsUpdate = true;
  }

  private getDefaultOutputs(): any {
    return {
      currentValue: null,
      progress: 0,
      isPlaying: false,
      onStarted: false,
      onCompleted: false,
      onStopped: false,
      onError: false
    };
  }
}

/**
 * 材质优化节点
 */
export class MaterialOptimizationNode extends VisualScriptNode {
  public static readonly TYPE = 'MaterialOptimization';
  public static readonly NAME = '材质优化';
  public static readonly DESCRIPTION = '优化材质性能和内存使用';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = MaterialOptimizationNode.TYPE, name: string = MaterialOptimizationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('optimize', 'trigger', '优化');
    this.addInput('materialId', 'string', '材质ID');
    this.addInput('optimizationLevel', 'string', '优化级别');
    this.addInput('targetPlatform', 'string', '目标平台');
    this.addInput('qualityLevel', 'string', '质量级别');

    // 输出端口
    this.addOutput('optimizedMaterial', 'object', '优化后材质');
    this.addOutput('optimizationReport', 'object', '优化报告');
    this.addOutput('memoryReduction', 'number', '内存减少量');
    this.addOutput('performanceGain', 'number', '性能提升');
    this.addOutput('onOptimized', 'trigger', '优化完成');
    this.addOutput('onError', 'trigger', '优化失败');
  }

  public execute(inputs?: any): any {
    try {
      const optimizeTrigger = inputs?.optimize;
      if (!optimizeTrigger) {
        return this.getDefaultOutputs();
      }

      const materialId = inputs?.materialId as string;
      if (!materialId) {
        throw new Error('未提供材质ID');
      }

      const material = MaterialOptimizationNode.renderingManager.getMaterial(materialId);
      if (!material) {
        throw new Error('材质不存在');
      }

      const optimizationLevel = inputs?.optimizationLevel as string || 'medium';
      const targetPlatform = inputs?.targetPlatform as string || 'desktop';
      const qualityLevel = inputs?.qualityLevel as string || 'high';

      const optimizationResult = this.optimizeMaterial(material, optimizationLevel, targetPlatform, qualityLevel);

      Debug.log('MaterialOptimizationNode', `材质优化完成: ${materialId}`);

      return {
        optimizedMaterial: optimizationResult.material,
        optimizationReport: optimizationResult.report,
        memoryReduction: optimizationResult.memoryReduction,
        performanceGain: optimizationResult.performanceGain,
        onOptimized: true,
        onError: false
      };

    } catch (error) {
      Debug.error('MaterialOptimizationNode', '材质优化失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private optimizeMaterial(material: Material, level: string, platform: string, quality: string): any {
    const report = {
      originalComplexity: this.calculateMaterialComplexity(material),
      optimizations: [] as string[],
      warnings: [] as string[]
    };

    let memoryReduction = 0;
    let performanceGain = 0;

    // 根据优化级别应用不同优化策略
    switch (level) {
      case 'low':
        // 基础优化
        if (material.transparent && material.opacity === 1.0) {
          material.transparent = false;
          report.optimizations.push('移除不必要的透明度');
          performanceGain += 5;
        }
        break;

      case 'medium':
        // 中等优化
        this.applyMediumOptimizations(material, report);
        memoryReduction += 15;
        performanceGain += 10;
        break;

      case 'high':
        // 高级优化
        this.applyHighOptimizations(material, report);
        memoryReduction += 30;
        performanceGain += 20;
        break;

      case 'aggressive':
        // 激进优化
        this.applyAggressiveOptimizations(material, report);
        memoryReduction += 50;
        performanceGain += 35;
        break;
    }

    // 平台特定优化
    this.applyPlatformOptimizations(material, platform, report);

    // 质量级别调整
    this.applyQualityOptimizations(material, quality, report);

    material.needsUpdate = true;

    return {
      material,
      report,
      memoryReduction,
      performanceGain
    };
  }

  private calculateMaterialComplexity(material: Material): number {
    let complexity = 1;

    if (material.transparent) complexity += 1;
    if ((material as any).normalMap) complexity += 1;
    if ((material as any).envMap) complexity += 1;
    if ((material as any).emissiveMap) complexity += 1;

    return complexity;
  }

  private applyMediumOptimizations(material: Material, report: any): void {
    // 优化材质属性
    if ((material as any).roughness !== undefined) {
      (material as any).roughness = Math.round((material as any).roughness * 100) / 100;
      report.optimizations.push('精度优化：粗糙度');
    }

    if ((material as any).metalness !== undefined) {
      (material as any).metalness = Math.round((material as any).metalness * 100) / 100;
      report.optimizations.push('精度优化：金属度');
    }
  }

  private applyHighOptimizations(material: Material, report: any): void {
    this.applyMediumOptimizations(material, report);

    // 更激进的优化
    if (material.opacity < 0.01) {
      material.opacity = 0;
      material.transparent = true;
      report.optimizations.push('优化：极低透明度处理');
    }
  }

  private applyAggressiveOptimizations(material: Material, report: any): void {
    this.applyHighOptimizations(material, report);

    // 最激进的优化
    report.optimizations.push('激进优化：简化材质复杂度');
    report.warnings.push('警告：激进优化可能影响视觉质量');
  }

  private applyPlatformOptimizations(material: Material, platform: string, report: any): void {
    switch (platform) {
      case 'mobile':
        // 移动端优化
        if ((material as any).envMapIntensity > 0.5) {
          (material as any).envMapIntensity = 0.5;
          report.optimizations.push('移动端优化：降低环境贴图强度');
        }
        break;
      case 'web':
        // Web端优化
        report.optimizations.push('Web端优化：启用压缩');
        break;
      case 'desktop':
        // 桌面端可以保持高质量
        break;
    }
  }

  private applyQualityOptimizations(material: Material, quality: string, report: any): void {
    switch (quality) {
      case 'low':
        // 低质量设置
        if ((material as any).normalScale) {
          (material as any).normalScale.multiplyScalar(0.5);
          report.optimizations.push('低质量：降低法线贴图强度');
        }
        break;
      case 'medium':
        // 中等质量
        break;
      case 'high':
        // 高质量保持原样
        break;
    }
  }

  private getDefaultOutputs(): any {
    return {
      optimizedMaterial: null,
      optimizationReport: null,
      memoryReduction: 0,
      performanceGain: 0,
      onOptimized: false,
      onError: false
    };
  }
}

/**
 * PBR材质节点
 */
export class PBRMaterialNode extends VisualScriptNode {
  public static readonly TYPE = 'PBRMaterial';
  public static readonly NAME = 'PBR材质';
  public static readonly DESCRIPTION = '创建基于物理的渲染材质';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = PBRMaterialNode.TYPE, name: string = PBRMaterialNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建');
    this.addInput('materialId', 'string', '材质ID');
    this.addInput('baseColor', 'object', '基础颜色');
    this.addInput('metalness', 'number', '金属度');
    this.addInput('roughness', 'number', '粗糙度');
    this.addInput('normalScale', 'number', '法线强度');
    this.addInput('emissive', 'object', '自发光');
    this.addInput('emissiveIntensity', 'number', '自发光强度');
    this.addInput('envMapIntensity', 'number', '环境贴图强度');
    this.addInput('clearcoat', 'number', '清漆层');
    this.addInput('clearcoatRoughness', 'number', '清漆粗糙度');

    // 输出端口
    this.addOutput('material', 'object', 'PBR材质');
    this.addOutput('materialId', 'string', '材质ID');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onError', 'trigger', '创建失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      if (!createTrigger) {
        return this.getDefaultOutputs();
      }

      const materialId = inputs?.materialId as string || this.generateMaterialId();
      const baseColor = inputs?.baseColor as Color || new Color(0xffffff);
      const metalness = Math.max(0, Math.min(1, inputs?.metalness as number ?? 0.0));
      const roughness = Math.max(0, Math.min(1, inputs?.roughness as number ?? 0.5));
      const normalScale = inputs?.normalScale as number ?? 1.0;
      const emissive = inputs?.emissive as Color || new Color(0x000000);
      const emissiveIntensity = Math.max(0, inputs?.emissiveIntensity as number ?? 0.0);
      const envMapIntensity = Math.max(0, inputs?.envMapIntensity as number ?? 1.0);
      const clearcoat = Math.max(0, Math.min(1, inputs?.clearcoat as number ?? 0.0));
      const clearcoatRoughness = Math.max(0, Math.min(1, inputs?.clearcoatRoughness as number ?? 0.0));

      const config: MaterialConfig = {
        type: MaterialType.PHYSICAL,
        color: baseColor,
        opacity: 1.0,
        transparent: false,
        wireframe: false,
        metalness,
        roughness,
        emissive,
        emissiveIntensity,
        normalScale,
        envMapIntensity,
        clearcoat,
        clearcoatRoughness
      };

      const material = PBRMaterialNode.renderingManager.createMaterial(materialId, config);

      Debug.log('PBRMaterialNode', `PBR材质创建成功: ${materialId}`);

      return {
        material,
        materialId,
        onCreated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('PBRMaterialNode', 'PBR材质创建失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private generateMaterialId(): string {
    return 'pbr_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      material: null,
      materialId: '',
      onCreated: false,
      onError: false
    };
  }
}

/**
 * 标准材质节点
 */
export class StandardMaterialNode extends VisualScriptNode {
  public static readonly TYPE = 'StandardMaterial';
  public static readonly NAME = '标准材质';
  public static readonly DESCRIPTION = '创建标准材质';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = StandardMaterialNode.TYPE, name: string = StandardMaterialNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建');
    this.addInput('materialId', 'string', '材质ID');
    this.addInput('color', 'object', '颜色');
    this.addInput('opacity', 'number', '透明度');
    this.addInput('transparent', 'boolean', '透明');
    this.addInput('metalness', 'number', '金属度');
    this.addInput('roughness', 'number', '粗糙度');
    this.addInput('emissive', 'object', '自发光');

    // 输出端口
    this.addOutput('material', 'object', '标准材质');
    this.addOutput('materialId', 'string', '材质ID');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onError', 'trigger', '创建失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      if (!createTrigger) {
        return this.getDefaultOutputs();
      }

      const materialId = inputs?.materialId as string || this.generateMaterialId();
      const color = inputs?.color as Color || new Color(0xffffff);
      const opacity = Math.max(0, Math.min(1, inputs?.opacity as number ?? 1.0));
      const transparent = inputs?.transparent as boolean ?? false;
      const metalness = Math.max(0, Math.min(1, inputs?.metalness as number ?? 0.0));
      const roughness = Math.max(0, Math.min(1, inputs?.roughness as number ?? 1.0));
      const emissive = inputs?.emissive as Color || new Color(0x000000);

      const config: MaterialConfig = {
        type: MaterialType.STANDARD,
        color,
        opacity,
        transparent,
        wireframe: false,
        metalness,
        roughness,
        emissive,
        emissiveIntensity: 1.0
      };

      const material = StandardMaterialNode.renderingManager.createMaterial(materialId, config);

      Debug.log('StandardMaterialNode', `标准材质创建成功: ${materialId}`);

      return {
        material,
        materialId,
        onCreated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('StandardMaterialNode', '标准材质创建失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private generateMaterialId(): string {
    return 'std_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      material: null,
      materialId: '',
      onCreated: false,
      onError: false
    };
  }
}

/**
 * 自定义材质节点
 */
export class CustomMaterialNode extends VisualScriptNode {
  public static readonly TYPE = 'CustomMaterial';
  public static readonly NAME = '自定义材质';
  public static readonly DESCRIPTION = '创建自定义着色器材质';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = CustomMaterialNode.TYPE, name: string = CustomMaterialNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建');
    this.addInput('materialId', 'string', '材质ID');
    this.addInput('vertexShader', 'string', '顶点着色器');
    this.addInput('fragmentShader', 'string', '片段着色器');
    this.addInput('uniforms', 'object', '统一变量');
    this.addInput('transparent', 'boolean', '透明');
    this.addInput('side', 'string', '渲染面');

    // 输出端口
    this.addOutput('material', 'object', '自定义材质');
    this.addOutput('materialId', 'string', '材质ID');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onError', 'trigger', '创建失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      if (!createTrigger) {
        return this.getDefaultOutputs();
      }

      const materialId = inputs?.materialId as string || this.generateMaterialId();
      const vertexShader = inputs?.vertexShader as string || this.getDefaultVertexShader();
      const fragmentShader = inputs?.fragmentShader as string || this.getDefaultFragmentShader();
      const uniforms = inputs?.uniforms || {};
      const transparent = inputs?.transparent as boolean ?? false;
      const side = inputs?.side as string || 'FrontSide';

      // 创建自定义材质（这里简化实现，实际需要ShaderMaterial）
      const config: MaterialConfig = {
        type: MaterialType.CUSTOM,
        color: new Color(0xffffff),
        opacity: 1.0,
        transparent,
        wireframe: false
      };

      const material = CustomMaterialNode.renderingManager.createMaterial(materialId, config);

      // 存储着色器信息到材质
      (material as any).vertexShader = vertexShader;
      (material as any).fragmentShader = fragmentShader;
      (material as any).uniforms = uniforms;

      Debug.log('CustomMaterialNode', `自定义材质创建成功: ${materialId}`);

      return {
        material,
        materialId,
        onCreated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('CustomMaterialNode', '自定义材质创建失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private getDefaultVertexShader(): string {
    return `
      void main() {
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;
  }

  private getDefaultFragmentShader(): string {
    return `
      void main() {
        gl_FragColor = vec4(1.0, 1.0, 1.0, 1.0);
      }
    `;
  }

  private generateMaterialId(): string {
    return 'custom_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      material: null,
      materialId: '',
      onCreated: false,
      onError: false
    };
  }
}

/**
 * 材质预设节点
 */
export class MaterialPresetNode extends VisualScriptNode {
  public static readonly TYPE = 'MaterialPreset';
  public static readonly NAME = '材质预设';
  public static readonly DESCRIPTION = '使用预定义的材质预设';

  private static renderingManager: RenderingManager = new RenderingManager();
  private static presets: Map<string, MaterialConfig> = new Map();

  constructor(nodeType: string = MaterialPresetNode.TYPE, name: string = MaterialPresetNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
    this.initializePresets();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建');
    this.addInput('materialId', 'string', '材质ID');
    this.addInput('presetName', 'string', '预设名称');
    this.addInput('customization', 'object', '自定义参数');

    // 输出端口
    this.addOutput('material', 'object', '预设材质');
    this.addOutput('materialId', 'string', '材质ID');
    this.addOutput('presetInfo', 'object', '预设信息');
    this.addOutput('availablePresets', 'array', '可用预设');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onError', 'trigger', '创建失败');
  }

  private initializePresets(): void {
    if (MaterialPresetNode.presets.size > 0) return;

    // 金属预设
    MaterialPresetNode.presets.set('metal', {
      type: MaterialType.STANDARD,
      color: new Color(0x888888),
      opacity: 1.0,
      transparent: false,
      wireframe: false,
      metalness: 1.0,
      roughness: 0.1,
      emissive: new Color(0x000000),
      emissiveIntensity: 0.0
    });

    // 塑料预设
    MaterialPresetNode.presets.set('plastic', {
      type: MaterialType.STANDARD,
      color: new Color(0xff4444),
      opacity: 1.0,
      transparent: false,
      wireframe: false,
      metalness: 0.0,
      roughness: 0.8,
      emissive: new Color(0x000000),
      emissiveIntensity: 0.0
    });

    // 玻璃预设
    MaterialPresetNode.presets.set('glass', {
      type: MaterialType.PHYSICAL,
      color: new Color(0xffffff),
      opacity: 0.1,
      transparent: true,
      wireframe: false,
      metalness: 0.0,
      roughness: 0.0,
      transmission: 1.0,
      thickness: 0.5,
      ior: 1.5
    });

    // 木材预设
    MaterialPresetNode.presets.set('wood', {
      type: MaterialType.STANDARD,
      color: new Color(0x8B4513),
      opacity: 1.0,
      transparent: false,
      wireframe: false,
      metalness: 0.0,
      roughness: 0.9,
      emissive: new Color(0x000000),
      emissiveIntensity: 0.0
    });

    // 发光预设
    MaterialPresetNode.presets.set('emissive', {
      type: MaterialType.STANDARD,
      color: new Color(0x000000),
      opacity: 1.0,
      transparent: false,
      wireframe: false,
      metalness: 0.0,
      roughness: 1.0,
      emissive: new Color(0x00ff00),
      emissiveIntensity: 2.0
    });
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      if (!createTrigger) {
        return {
          ...this.getDefaultOutputs(),
          availablePresets: Array.from(MaterialPresetNode.presets.keys())
        };
      }

      const materialId = inputs?.materialId as string || this.generateMaterialId();
      const presetName = inputs?.presetName as string || 'metal';
      const customization = inputs?.customization || {};

      const presetConfig = MaterialPresetNode.presets.get(presetName);
      if (!presetConfig) {
        throw new Error(`未找到预设: ${presetName}`);
      }

      // 应用自定义参数
      const finalConfig = { ...presetConfig, ...customization };

      const material = MaterialPresetNode.renderingManager.createMaterial(materialId, finalConfig);

      const presetInfo = {
        name: presetName,
        description: this.getPresetDescription(presetName),
        properties: presetConfig
      };

      Debug.log('MaterialPresetNode', `材质预设创建成功: ${materialId} (${presetName})`);

      return {
        material,
        materialId,
        presetInfo,
        availablePresets: Array.from(MaterialPresetNode.presets.keys()),
        onCreated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('MaterialPresetNode', '材质预设创建失败', error);
      return {
        ...this.getDefaultOutputs(),
        availablePresets: Array.from(MaterialPresetNode.presets.keys()),
        onError: true
      };
    }
  }

  private getPresetDescription(presetName: string): string {
    const descriptions: { [key: string]: string } = {
      'metal': '金属材质，高金属度，低粗糙度',
      'plastic': '塑料材质，无金属度，中等粗糙度',
      'glass': '玻璃材质，透明，高透射率',
      'wood': '木材材质，无金属度，高粗糙度',
      'emissive': '发光材质，强自发光效果'
    };
    return descriptions[presetName] || '未知预设';
  }

  private generateMaterialId(): string {
    return 'preset_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      material: null,
      materialId: '',
      presetInfo: null,
      availablePresets: [],
      onCreated: false,
      onError: false
    };
  }
}
