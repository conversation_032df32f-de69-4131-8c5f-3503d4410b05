import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { VoiceInteractionService } from './voice-interaction.service';
import { SpeechRecognitionService } from './speech-recognition.service';
import { TextToSpeechService } from './text-to-speech.service';
import { VoiceCommandProcessor } from './voice-command-processor.service';
import { VoiceCommand } from '../database/entities/voice-command.entity';

/**
 * 语音交互模块
 * 提供语音识别、语音合成、语音指令处理等功能
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([VoiceCommand]),
  ],
  providers: [
    VoiceInteractionService,
    SpeechRecognitionService,
    TextToSpeechService,
    VoiceCommandProcessor,
  ],
  exports: [
    VoiceInteractionService,
    SpeechRecognitionService,
    TextToSpeechService,
    VoiceCommandProcessor,
  ],
})
export class VoiceInteractionModule {}
