{"timestamp": "2025-07-02T13:43:30.246Z", "services": {"5g-network-service": {"name": "5g-network-service", "path": "F:\\newsystem\\server\\5g-network-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": false, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": false, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "ai-engine-service": {"name": "ai-engine-service", "path": "F:\\newsystem\\server\\ai-engine-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": false, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "ai-model-service": {"name": "ai-model-service", "path": "F:\\newsystem\\server\\ai-model-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "ai-service": {"name": "ai-service", "path": "F:\\newsystem\\server\\ai-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "analytics-service": {"name": "analytics-service", "path": "F:\\newsystem\\server\\analytics-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "api-gateway": {"name": "api-gateway", "path": "F:\\newsystem\\server\\api-gateway", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "asset-service": {"name": "asset-service", "path": "F:\\newsystem\\server\\asset-service", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": true, "duplicateCode": false}}, "status": "completed"}, "avatar-service": {"name": "avatar-service", "path": "F:\\newsystem\\server\\avatar-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "behavior-decision-service": {"name": "behavior-decision-service", "path": "F:\\newsystem\\server\\behavior-decision-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "blockchain-service": {"name": "blockchain-service", "path": "F:\\newsystem\\server\\blockchain-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": true, "duplicateCode": false}}, "status": "completed"}, "cloud-edge-orchestration-service": {"name": "cloud-edge-orchestration-service", "path": "F:\\newsystem\\server\\cloud-edge-orchestration-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "collaboration-service": {"name": "collaboration-service", "path": "F:\\newsystem\\server\\collaboration-service", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "coordination-service": {"name": "coordination-service", "path": "F:\\newsystem\\server\\coordination-service", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": false}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "deeplearning-service": {"name": "deeplearning-service", "path": "F:\\newsystem\\server\\deeplearning-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": false, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "ecosystem-service": {"name": "ecosystem-service", "path": "F:\\newsystem\\server\\ecosystem-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": true, "duplicateCode": false}}, "status": "completed"}, "edge-ai-service": {"name": "edge-ai-service", "path": "F:\\newsystem\\server\\edge-ai-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "edge-enhancement": {"name": "edge-enhancement", "path": "F:\\newsystem\\server\\edge-enhancement", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "edge-game-server": {"name": "edge-game-server", "path": "F:\\newsystem\\server\\edge-game-server", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": false, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "edge-registry": {"name": "edge-registry", "path": "F:\\newsystem\\server\\edge-registry", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "edge-router": {"name": "edge-router", "path": "F:\\newsystem\\server\\edge-router", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "emotion-service": {"name": "emotion-service", "path": "F:\\newsystem\\server\\emotion-service", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "enterprise-integration-service": {"name": "enterprise-integration-service", "path": "F:\\newsystem\\server\\enterprise-integration-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "game-server": {"name": "game-server", "path": "F:\\newsystem\\server\\game-server", "tasks": {"codeStandards": {"eslint": false, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "human-machine-collaboration-service": {"name": "human-machine-collaboration-service", "path": "F:\\newsystem\\server\\human-machine-collaboration-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "industrial-data-service": {"name": "industrial-data-service", "path": "F:\\newsystem\\server\\industrial-data-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": true, "duplicateCode": false}}, "status": "completed"}, "intelligent-scheduling-service": {"name": "intelligent-scheduling-service", "path": "F:\\newsystem\\server\\intelligent-scheduling-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "knowledge-base-service": {"name": "knowledge-base-service", "path": "F:\\newsystem\\server\\knowledge-base-service", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "knowledge-graph-service": {"name": "knowledge-graph-service", "path": "F:\\newsystem\\server\\knowledge-graph-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "learning-tracking-service": {"name": "learning-tracking-service", "path": "F:\\newsystem\\server\\learning-tracking-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "mes-service": {"name": "mes-service", "path": "F:\\newsystem\\server\\mes-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "mobile-service": {"name": "mobile-service", "path": "F:\\newsystem\\server\\mobile-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "monitoring-service": {"name": "monitoring-service", "path": "F:\\newsystem\\server\\monitoring-service", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "nlp-scene-service": {"name": "nlp-scene-service", "path": "F:\\newsystem\\server\\nlp-scene-service", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "perception-service": {"name": "perception-service", "path": "F:\\newsystem\\server\\perception-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": false, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "performance-service": {"name": "performance-service", "path": "F:\\newsystem\\server\\performance-service", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "predictive-maintenance-service": {"name": "predictive-maintenance-service", "path": "F:\\newsystem\\server\\predictive-maintenance-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": false, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "project-service": {"name": "project-service", "path": "F:\\newsystem\\server\\project-service", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "rag-dialogue-service": {"name": "rag-dialogue-service", "path": "F:\\newsystem\\server\\rag-dialogue-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "recommendation-service": {"name": "recommendation-service", "path": "F:\\newsystem\\server\\recommendation-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "render-service": {"name": "render-service", "path": "F:\\newsystem\\server\\render-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "security-service": {"name": "security-service", "path": "F:\\newsystem\\server\\security-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "service-registry": {"name": "service-registry", "path": "F:\\newsystem\\server\\service-registry", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "signaling-service": {"name": "signaling-service", "path": "F:\\newsystem\\server\\signaling-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "spatial-service": {"name": "spatial-service", "path": "F:\\newsystem\\server\\spatial-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "ui-service": {"name": "ui-service", "path": "F:\\newsystem\\server\\ui-service", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "user-service": {"name": "user-service", "path": "F:\\newsystem\\server\\user-service", "tasks": {"codeStandards": {"eslint": true, "prettier": true, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": false}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}, "visual-script-service": {"name": "visual-script-service", "path": "F:\\newsystem\\server\\visual-script-service", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": true, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": true, "duplicateCode": false}}, "status": "completed"}, "voice-service": {"name": "voice-service", "path": "F:\\newsystem\\server\\voice-service", "tasks": {"codeStandards": {"eslint": false, "prettier": false, "tsconfig": true, "editorconfig": true}, "typeDefinitions": {"strictMode": true, "typeImports": false, "interfaceDefinitions": true, "utilityTypes": false}, "databaseOptimization": {"indexOptimization": false, "queryOptimization": true, "connectionPooling": false, "caching": true}, "cleanup": {"unusedImports": true, "unusedDependencies": false, "deadCode": false, "duplicateCode": false}}, "status": "completed"}}, "summary": {"total": 48, "processed": 48, "errors": 0, "warnings": 0}}