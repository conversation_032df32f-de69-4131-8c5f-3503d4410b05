#!/usr/bin/env node

/**
 * DL引擎微服务项目文档完善脚本
 * 更新所有API文档、完善部署运维文档、添加开发者指南、创建故障排除手册
 */

const fs = require('fs');
const path = require('path');

class DocumentationEnhancer {
  constructor() {
    this.serverDir = path.join(__dirname, '..');
    this.services = this.getServiceDirectories();
    this.report = {
      timestamp: new Date().toISOString(),
      processed: 0,
      total: 0,
      tasks: {
        apiDocumentation: false,
        deploymentGuide: false,
        developerGuide: false,
        troubleshootingGuide: false,
        architectureDoc: false
      }
    };
  }

  getServiceDirectories() {
    const items = fs.readdirSync(this.serverDir);
    return items.filter(item => {
      const itemPath = path.join(this.serverDir, item);
      const stat = fs.statSync(itemPath);
      return stat.isDirectory() &&
             !['node_modules', 'dist', 'scripts', 'docs', 'shared', 'database', 'src'].includes(item) &&
             !item.startsWith('.') &&
             !item.endsWith('-reports');
    });
  }

  async run() {
    console.log('📚 开始文档完善...\n');

    this.report.total = this.services.length;

    try {
      // 创建主文档目录
      await this.createMainDocumentation();

      // 为每个服务创建API文档
      await this.processServices();

      this.generateReport();
      this.printSummary();

    } catch (error) {
      console.error('❌ 文档完善失败:', error.message);
    }
  }

  async createMainDocumentation() {
    console.log('📖 创建主要文档...');

    const docsDir = path.join(this.serverDir, 'docs');
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }

    // 创建架构文档
    await this.createArchitectureDoc(docsDir);

    // 创建部署指南
    await this.createDeploymentGuide(docsDir);

    // 创建开发者指南
    await this.createDeveloperGuide(docsDir);

    // 创建故障排除手册
    await this.createTroubleshootingGuide(docsDir);

    console.log('✅ 主要文档创建完成');
  }

  async createArchitectureDoc(docsDir) {
    const architectureDoc = `# DL引擎微服务架构文档

## 📋 概述

DL引擎是一个基于微服务架构的分布式系统，包含60个微服务，涵盖AI/ML、工业制造、游戏渲染、边缘计算等多个领域。

## 🏗️ 系统架构

### 核心组件

#### 1. API网关 (api-gateway)
- **功能**: 统一入口，路由分发，认证授权
- **端口**: 3000
- **技术栈**: NestJS + Express

#### 2. 服务注册中心 (service-registry)
- **功能**: 服务注册与发现
- **端口**: 3001
- **技术栈**: NestJS + Redis

#### 3. 用户服务 (user-service)
- **功能**: 用户管理，认证授权
- **端口**: 3002
- **技术栈**: NestJS + MySQL

### 服务分类

#### 🤖 AI/ML服务 (8个)
- ai-model-service: AI模型管理和推理
- deeplearning-service: 深度学习训练服务
- recommendation-service: 智能推荐系统
- emotion-service: 情感分析服务
- nlp-scene-service: NLP场景生成
- perception-service: 感知数据处理
- ai-service: 基础AI服务
- ai-engine-service: AI引擎核心

#### 🏭 工业/制造服务 (7个)
- mes-service: 制造执行系统
- predictive-maintenance-service: 预测性维护
- industrial-data-service: 工业数据管理
- intelligent-scheduling-service: 智能调度系统
- knowledge-graph-service: 工业知识图谱
- behavior-decision-service: 行为决策服务
- human-machine-collaboration-service: 人机协作

#### 🎮 游戏/渲染服务 (6个)
- render-service: 3D渲染服务
- voice-service: 语音处理服务
- avatar-service: 虚拟角色服务
- visual-script-service: 可视化脚本
- game-server: 游戏服务器
- ui-service: UI组件管理

#### 🌐 边缘计算服务 (8个)
- edge-registry: 边缘节点注册
- edge-router: 边缘路由服务
- edge-game-server: 边缘游戏服务器
- edge-ai-service: 边缘AI计算
- edge-enhancement: 边缘增强
- cloud-edge-orchestration-service: 云边协调
- enterprise-integration-service: 企业集成
- 5g-network-service: 5G网络服务

## 🔗 通信协议

### HTTP API标准
所有服务遵循统一的HTTP API响应格式：

\`\`\`json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2025-07-02T13:47:09.508Z",
  "requestId": "req_1719928029508_abc123"
}
\`\`\`

### 微服务间通信
- **同步通信**: HTTP/REST API
- **异步通信**: Redis Pub/Sub, Message Queue
- **服务发现**: 基于Redis的服务注册中心

## 🛡️ 安全架构

### 认证授权
- JWT Token认证
- RBAC权限控制
- API密钥管理

### 数据安全
- 数据加密传输 (HTTPS)
- 敏感数据脱敏
- 审计日志记录

## 📊 监控体系

### 指标监控
- Prometheus + Grafana
- 服务健康检查
- 性能指标收集

### 日志管理
- 结构化日志格式
- 集中式日志收集
- 日志分析和告警

## 🚀 部署架构

### 容器化部署
- Docker容器化
- Docker Compose编排
- Kubernetes集群部署

### 环境管理
- 开发环境 (Development)
- 测试环境 (Testing)
- 生产环境 (Production)

## 📈 扩展性设计

### 水平扩展
- 无状态服务设计
- 负载均衡
- 数据库分片

### 高可用性
- 服务冗余部署
- 故障转移机制
- 数据备份策略
`;

    fs.writeFileSync(path.join(docsDir, 'architecture.md'), architectureDoc);
    this.report.tasks.architectureDoc = true;
  }

  async createDeploymentGuide(docsDir) {
    const deploymentGuide = `# DL引擎部署运维指南

## 🚀 快速部署

### 环境要求

#### 硬件要求
- CPU: 8核心以上
- 内存: 32GB以上
- 存储: 500GB SSD
- 网络: 千兆网卡

#### 软件要求
- Node.js 18+
- Docker 20.10+
- Docker Compose 2.0+
- Redis 6.0+
- MySQL 8.0+

### 一键部署脚本

\`\`\`bash
# 克隆项目
git clone <repository-url>
cd newsystem/server

# 安装依赖
npm run install:all

# 启动基础设施
docker-compose up -d redis mysql

# 启动所有服务
npm run start:all
\`\`\`

## 🐳 Docker部署

### 构建镜像

\`\`\`bash
# 构建所有服务镜像
npm run docker:build

# 构建单个服务镜像
cd api-gateway
docker build -t dl-engine/api-gateway .
\`\`\`

### Docker Compose部署

\`\`\`yaml
version: '3.8'
services:
  api-gateway:
    image: dl-engine/api-gateway
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
      - DB_HOST=mysql
    depends_on:
      - redis
      - mysql

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

  mysql:
    image: mysql:8
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: dl_engine
    ports:
      - "3306:3306"
\`\`\`

## ☸️ Kubernetes部署

### 命名空间创建

\`\`\`yaml
apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine
\`\`\`

### 服务部署示例

\`\`\`yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: dl-engine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: dl-engine/api-gateway:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
\`\`\`

## 🔧 配置管理

### 环境变量配置

每个服务的环境变量配置：

\`\`\`bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=dl_engine

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 服务配置
PORT=3000
NODE_ENV=production
LOG_LEVEL=info

# JWT配置
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=24h
\`\`\`

### 配置文件管理

\`\`\`typescript
// config/database.config.ts
export default {
  type: 'mysql',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  username: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_DATABASE || 'dl_engine',
  synchronize: process.env.NODE_ENV !== 'production',
  logging: process.env.NODE_ENV === 'development'
};
\`\`\`

## 📊 监控部署

### Prometheus配置

\`\`\`yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'dl-engine-services'
    static_configs:
      - targets:
        - 'api-gateway:3000'
        - 'user-service:3002'
        - 'ai-service:3003'
\`\`\`

### Grafana仪表板

导入预配置的Grafana仪表板：
- 服务健康状态
- API响应时间
- 错误率统计
- 资源使用情况

## 🔄 CI/CD流水线

### GitHub Actions配置

\`\`\`yaml
name: Deploy DL Engine
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2

    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm test

    - name: Build Docker images
      run: npm run docker:build

    - name: Deploy to production
      run: npm run deploy:prod
\`\`\`

## 🛠️ 运维操作

### 服务管理

\`\`\`bash
# 启动所有服务
npm run start:all

# 停止所有服务
npm run stop:all

# 重启服务
npm run restart:service api-gateway

# 查看服务状态
npm run status
\`\`\`

### 日志管理

\`\`\`bash
# 查看服务日志
docker logs dl-engine-api-gateway

# 实时日志
docker logs -f dl-engine-api-gateway

# 日志聚合查询
kubectl logs -l app=api-gateway -n dl-engine
\`\`\`

### 数据备份

\`\`\`bash
# 数据库备份
mysqldump -h localhost -u root -p dl_engine > backup.sql

# Redis备份
redis-cli --rdb dump.rdb

# 自动备份脚本
./scripts/backup.sh
\`\`\`

## 🔒 安全配置

### SSL/TLS配置

\`\`\`nginx
server {
    listen 443 ssl;
    server_name api.dl-engine.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        proxy_pass http://api-gateway:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
\`\`\`

### 防火墙配置

\`\`\`bash
# 开放必要端口
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3000:3100/tcp

# 限制数据库访问
ufw allow from 10.0.0.0/8 to any port 3306
\`\`\`
`;

    fs.writeFileSync(path.join(docsDir, 'deployment.md'), deploymentGuide);
    this.report.tasks.deploymentGuide = true;
  }

  async createDeveloperGuide(docsDir) {
    const developerGuide = `# DL引擎开发者指南

## 🚀 快速开始

### 开发环境搭建

#### 1. 环境要求
- Node.js 18+
- npm 8+
- Git
- Docker (可选)
- VS Code (推荐)

#### 2. 项目克隆和安装

\`\`\`bash
# 克隆项目
git clone <repository-url>
cd newsystem/server

# 安装依赖
npm install

# 安装所有服务依赖
npm run install:all
\`\`\`

#### 3. 开发环境配置

\`\`\`bash
# 复制环境变量模板
cp .env.example .env

# 启动基础设施
docker-compose up -d redis mysql

# 启动开发服务器
npm run dev
\`\`\`

## 📁 项目结构

\`\`\`
server/
├── shared/                 # 共享组件
│   ├── protocols/         # 通信协议
│   ├── errors/           # 错误处理
│   ├── logging/          # 日志系统
│   └── monitoring/       # 监控配置
├── api-gateway/          # API网关
├── user-service/         # 用户服务
├── ai-service/           # AI服务
├── ...                   # 其他微服务
├── docs/                 # 文档
└── scripts/              # 脚本工具
\`\`\`

### 微服务标准结构

\`\`\`
service-name/
├── src/
│   ├── main.ts           # 入口文件
│   ├── app.module.ts     # 根模块
│   ├── controllers/      # 控制器
│   ├── services/         # 业务逻辑
│   ├── entities/         # 数据实体
│   ├── dto/              # 数据传输对象
│   ├── common/           # 公共组件
│   │   ├── filters/      # 异常过滤器
│   │   ├── interceptors/ # 拦截器
│   │   ├── guards/       # 守卫
│   │   └── pipes/        # 管道
│   └── config/           # 配置文件
├── test/                 # 测试文件
├── Dockerfile           # Docker配置
├── docker-compose.yml   # 容器编排
├── package.json         # 项目配置
├── tsconfig.json        # TypeScript配置
├── .eslintrc.js         # ESLint配置
└── README.md            # 项目文档
\`\`\`

## 🛠️ 开发规范

### 代码规范

#### 1. TypeScript规范
\`\`\`typescript
// 使用接口定义数据结构
interface UserDto {
  id: number;
  name: string;
  email: string;
  createdAt: Date;
}

// 使用类型注解
async function createUser(userData: CreateUserDto): Promise<User> {
  // 实现逻辑
}

// 使用枚举定义常量
enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}
\`\`\`

#### 2. NestJS规范
\`\`\`typescript
// 控制器示例
@Controller('users')
@UseGuards(JwtAuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @ApiOperation({ summary: '获取用户列表' })
  @ApiResponse({ status: 200, description: '成功返回用户列表' })
  async findAll(@Query() query: FindUsersDto): Promise<ApiResponse<User[]>> {
    const users = await this.userService.findAll(query);
    return HttpProtocol.success(users, '获取用户列表成功');
  }

  @Post()
  @ApiOperation({ summary: '创建用户' })
  async create(@Body() createUserDto: CreateUserDto): Promise<ApiResponse<User>> {
    const user = await this.userService.create(createUserDto);
    return HttpProtocol.success(user, '用户创建成功');
  }
}
\`\`\`

#### 3. 服务层规范
\`\`\`typescript
@Injectable()
export class UserService {
  private readonly logger = createLogger('UserService');

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async findAll(query: FindUsersDto): Promise<User[]> {
    try {
      this.logger.info('查询用户列表', { query });

      const queryBuilder = this.userRepository.createQueryBuilder('user');

      if (query.name) {
        queryBuilder.andWhere('user.name LIKE :name', { name: \`%\${query.name}%\` });
      }

      const users = await queryBuilder.getMany();

      this.logger.info('用户列表查询成功', { count: users.length });
      return users;

    } catch (error) {
      this.logger.error('用户列表查询失败', error);
      throw new AppError(ErrorCode.DATABASE_ERROR, '查询用户列表失败');
    }
  }
}
\`\`\`

### API设计规范

#### 1. RESTful API设计
\`\`\`
GET    /api/users          # 获取用户列表
GET    /api/users/:id      # 获取单个用户
POST   /api/users          # 创建用户
PUT    /api/users/:id      # 更新用户
DELETE /api/users/:id      # 删除用户
\`\`\`

#### 2. 响应格式标准
\`\`\`json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "张三",
    "email": "<EMAIL>"
  },
  "message": "操作成功",
  "timestamp": "2025-07-02T13:47:09.508Z",
  "requestId": "req_1719928029508_abc123"
}
\`\`\`

#### 3. 错误响应格式
\`\`\`json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入数据验证失败",
    "details": {
      "email": "邮箱格式不正确"
    }
  },
  "timestamp": "2025-07-02T13:47:09.508Z",
  "requestId": "req_1719928029508_abc123"
}
\`\`\`

## 🧪 测试指南

### 单元测试

\`\`\`typescript
// user.service.spec.ts
describe('UserService', () => {
  let service: UserService;
  let repository: Repository<User>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getRepositoryToken(User),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  it('should create a user', async () => {
    const createUserDto = { name: '张三', email: '<EMAIL>' };
    const expectedUser = { id: 1, ...createUserDto };

    jest.spyOn(repository, 'save').mockResolvedValue(expectedUser as User);

    const result = await service.create(createUserDto);
    expect(result).toEqual(expectedUser);
  });
});
\`\`\`

### 集成测试

\`\`\`typescript
// user.controller.e2e-spec.ts
describe('UserController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/users (GET)', () => {
    return request(app.getHttpServer())
      .get('/users')
      .expect(200)
      .expect((res) => {
        expect(res.body.success).toBe(true);
        expect(Array.isArray(res.body.data)).toBe(true);
      });
  });
});
\`\`\`

## 🔧 调试指南

### VS Code调试配置

\`\`\`json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug NestJS",
      "type": "node",
      "request": "launch",
      "program": "\${workspaceFolder}/src/main.ts",
      "outFiles": ["\${workspaceFolder}/dist/**/*.js"],
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal"
    }
  ]
}
\`\`\`

### 日志调试

\`\`\`typescript
// 使用统一的日志系统
const logger = createLogger('ServiceName');

logger.debug('调试信息', { userId: 123, action: 'create' });
logger.info('操作信息', { result: 'success' });
logger.warn('警告信息', { warning: 'deprecated API' });
logger.error('错误信息', error, { context: 'additional info' });
\`\`\`

## 📦 依赖管理

### 添加新依赖

\`\`\`bash
# 添加生产依赖
npm install package-name

# 添加开发依赖
npm install -D package-name

# 更新依赖
npm update

# 检查过期依赖
npm outdated
\`\`\`

### 依赖版本管理

\`\`\`json
// package.json
{
  "dependencies": {
    "@nestjs/core": "^10.0.0",
    "typeorm": "~0.3.0",
    "mysql2": "3.6.0"
  }
}
\`\`\`

## 🚀 部署流程

### 本地构建测试

\`\`\`bash
# 构建项目
npm run build

# 运行测试
npm run test

# 代码检查
npm run lint

# 格式化代码
npm run format
\`\`\`

### Docker构建

\`\`\`bash
# 构建镜像
docker build -t service-name .

# 运行容器
docker run -p 3000:3000 service-name
\`\`\`

## 📝 提交规范

### Git提交信息格式

\`\`\`
<type>(<scope>): <subject>

<body>

<footer>
\`\`\`

#### 类型说明
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

#### 示例
\`\`\`
feat(user): 添加用户注册功能

- 实现用户注册API
- 添加邮箱验证
- 完善用户数据验证

Closes #123
\`\`\`

## 🔍 代码审查

### 审查清单
- [ ] 代码符合项目规范
- [ ] 有适当的错误处理
- [ ] 包含必要的测试
- [ ] API文档已更新
- [ ] 性能考虑合理
- [ ] 安全性检查通过

### 性能优化建议
- 使用数据库索引
- 实现缓存策略
- 避免N+1查询
- 使用连接池
- 合理的分页设计
`;

    fs.writeFileSync(path.join(docsDir, 'developer-guide.md'), developerGuide);
    this.report.tasks.developerGuide = true;
  }

  async createTroubleshootingGuide(docsDir) {
    const troubleshootingGuide = `# DL引擎故障排除手册

## 🚨 常见问题诊断

### 服务启动问题

#### 1. 端口占用错误
**症状**: \`Error: listen EADDRINUSE :::3000\`

**解决方案**:
\`\`\`bash
# 查找占用端口的进程
netstat -tulpn | grep :3000
# 或者使用lsof
lsof -i :3000

# 终止进程
kill -9 <PID>

# 或者修改服务端口
export PORT=3001
npm start
\`\`\`

#### 2. 数据库连接失败
**症状**: \`Error: connect ECONNREFUSED 127.0.0.1:3306\`

**解决方案**:
\`\`\`bash
# 检查MySQL服务状态
systemctl status mysql
# 或者
service mysql status

# 启动MySQL服务
systemctl start mysql

# 检查数据库配置
mysql -u root -p -e "SHOW DATABASES;"

# 验证连接参数
ping <DB_HOST>
telnet <DB_HOST> 3306
\`\`\`

#### 3. Redis连接失败
**症状**: \`Error: connect ECONNREFUSED 127.0.0.1:6379\`

**解决方案**:
\`\`\`bash
# 检查Redis服务状态
systemctl status redis
redis-cli ping

# 启动Redis服务
systemctl start redis

# 检查Redis配置
redis-cli info server
\`\`\`

### 依赖安装问题

#### 1. npm install失败
**症状**: \`npm ERR! peer dep missing\`

**解决方案**:
\`\`\`bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules和package-lock.json
rm -rf node_modules package-lock.json

# 重新安装
npm install

# 如果仍有问题，使用yarn
yarn install
\`\`\`

#### 2. TypeScript编译错误
**症状**: \`TS2307: Cannot find module\`

**解决方案**:
\`\`\`bash
# 检查tsconfig.json路径配置
cat tsconfig.json | grep paths

# 安装缺失的类型定义
npm install @types/node @types/express

# 重新构建
npm run build
\`\`\`

## 🔍 性能问题诊断

### 内存泄漏检测

#### 1. 监控内存使用
\`\`\`bash
# 使用top命令监控
top -p <PID>

# 使用htop
htop

# Node.js内存使用情况
node --inspect app.js
# 然后在Chrome中打开 chrome://inspect
\`\`\`

#### 2. 内存泄漏分析
\`\`\`javascript
// 在代码中添加内存监控
setInterval(() => {
  const memUsage = process.memoryUsage();
  console.log('Memory Usage:', {
    rss: Math.round(memUsage.rss / 1024 / 1024) + ' MB',
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + ' MB',
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + ' MB',
    external: Math.round(memUsage.external / 1024 / 1024) + ' MB'
  });
}, 10000);
\`\`\`

### 数据库性能问题

#### 1. 慢查询分析
\`\`\`sql
-- 启用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log%';

-- 分析慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;
\`\`\`

#### 2. 索引优化
\`\`\`sql
-- 查看表索引
SHOW INDEX FROM users;

-- 分析查询执行计划
EXPLAIN SELECT * FROM users WHERE email = '<EMAIL>';

-- 添加索引
CREATE INDEX idx_users_email ON users(email);
\`\`\`

## 🛠️ 调试工具

### 日志分析

#### 1. 结构化日志查询
\`\`\`bash
# 使用jq解析JSON日志
tail -f app.log | jq '.level == "error"'

# 过滤特定服务的日志
grep "user-service" app.log | jq '.'

# 统计错误数量
grep "ERROR" app.log | wc -l
\`\`\`

#### 2. 日志聚合分析
\`\`\`bash
# 使用ELK Stack
# Elasticsearch查询示例
curl -X GET "localhost:9200/logs/_search" -H 'Content-Type: application/json' -d'
{
  "query": {
    "bool": {
      "must": [
        {"match": {"level": "error"}},
        {"range": {"timestamp": {"gte": "now-1h"}}}
      ]
    }
  }
}'
\`\`\`

### 网络问题诊断

#### 1. 服务间通信检测
\`\`\`bash
# 检查服务可达性
curl -I http://localhost:3000/health

# 检查网络延迟
ping api-gateway

# 检查端口连通性
telnet api-gateway 3000

# 使用nmap扫描端口
nmap -p 3000-3010 localhost
\`\`\`

#### 2. 负载均衡检测
\`\`\`bash
# 检查负载均衡器状态
curl http://load-balancer/status

# 测试负载分发
for i in {1..10}; do
  curl -s http://api-gateway/health | grep instance
done
\`\`\`

## 🔧 故障恢复

### 服务重启策略

#### 1. 优雅重启
\`\`\`bash
# 发送SIGTERM信号
kill -TERM <PID>

# 等待进程结束
wait <PID>

# 重新启动
npm start
\`\`\`

#### 2. 滚动更新
\`\`\`bash
# Kubernetes滚动更新
kubectl rollout restart deployment/api-gateway

# Docker Compose滚动更新
docker-compose up -d --no-deps api-gateway
\`\`\`

### 数据恢复

#### 1. 数据库恢复
\`\`\`bash
# 从备份恢复
mysql -u root -p dl_engine < backup.sql

# 检查数据完整性
mysql -u root -p -e "CHECK TABLE users;"

# 修复损坏的表
mysql -u root -p -e "REPAIR TABLE users;"
\`\`\`

#### 2. Redis数据恢复
\`\`\`bash
# 从RDB文件恢复
cp backup.rdb /var/lib/redis/dump.rdb
systemctl restart redis

# 从AOF文件恢复
redis-check-aof --fix appendonly.aof
\`\`\`

## 📊 监控告警

### Prometheus告警规则

\`\`\`yaml
groups:
  - name: dl-engine-alerts
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
\`\`\`

### 健康检查端点

\`\`\`typescript
// health.controller.ts
@Controller('health')
export class HealthController {
  constructor(
    private readonly healthCheckService: HealthCheckService,
    private readonly typeOrmHealthIndicator: TypeOrmHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.healthCheckService.check([
      () => this.typeOrmHealthIndicator.pingCheck('database'),
      () => this.redisHealthIndicator.pingCheck('redis'),
    ]);
  }
}
\`\`\`

## 🚨 紧急响应流程

### 1. 服务完全宕机
1. 立即检查基础设施状态
2. 查看最近的部署记录
3. 回滚到上一个稳定版本
4. 通知相关团队
5. 分析根本原因

### 2. 数据库故障
1. 切换到备用数据库
2. 检查数据一致性
3. 修复主数据库
4. 同步数据
5. 切回主数据库

### 3. 安全事件
1. 立即隔离受影响的服务
2. 收集日志和证据
3. 评估影响范围
4. 修复安全漏洞
5. 恢复服务并加强监控

## 📞 联系信息

### 紧急联系人
- 系统管理员: <EMAIL>
- 开发团队负责人: <EMAIL>
- 运维团队: <EMAIL>

### 支持渠道
- 内部工单系统: https://tickets.company.com
- 技术支持热线: +86-xxx-xxxx-xxxx
- 在线文档: https://docs.dl-engine.com

## 📚 参考资源

### 官方文档
- [NestJS文档](https://docs.nestjs.com/)
- [TypeORM文档](https://typeorm.io/)
- [Docker文档](https://docs.docker.com/)
- [Kubernetes文档](https://kubernetes.io/docs/)

### 监控工具
- [Prometheus](https://prometheus.io/docs/)
- [Grafana](https://grafana.com/docs/)
- [ELK Stack](https://www.elastic.co/guide/)

### 最佳实践
- [12-Factor App](https://12factor.net/)
- [微服务设计模式](https://microservices.io/)
- [Node.js最佳实践](https://github.com/goldbergyoni/nodebestpractices)
`;

    fs.writeFileSync(path.join(docsDir, 'troubleshooting.md'), troubleshootingGuide);
    this.report.tasks.troubleshootingGuide = true;
  }

  async processServices() {
    console.log('📝 为各服务创建API文档...');

    for (const service of this.services) {
      try {
        await this.createServiceApiDoc(service);
        this.report.processed++;
      } catch (error) {
        console.error(`❌ 为服务 ${service} 创建API文档失败:`, error.message);
      }
    }

    this.report.tasks.apiDocumentation = true;
  }

  async createServiceApiDoc(serviceName) {
    const servicePath = path.join(this.serverDir, serviceName);
    const docsDir = path.join(servicePath, 'docs');

    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }

    const apiDoc = `# ${serviceName} API文档

## 📋 服务概述

${serviceName} 是DL引擎微服务架构中的一个核心服务。

### 基本信息
- **服务名称**: ${serviceName}
- **版本**: 1.0.0
- **端口**: 3000 (默认)
- **协议**: HTTP/REST

## 🔗 API端点

### 健康检查
\`\`\`
GET /health
\`\`\`

**响应示例**:
\`\`\`json
{
  "success": true,
  "data": {
    "status": "ok",
    "info": {
      "database": {
        "status": "up"
      }
    }
  },
  "timestamp": "2025-07-02T13:47:09.508Z"
}
\`\`\`

### 服务信息
\`\`\`
GET /info
\`\`\`

**响应示例**:
\`\`\`json
{
  "success": true,
  "data": {
    "name": "${serviceName}",
    "version": "1.0.0",
    "description": "DL引擎${serviceName}服务",
    "uptime": 3600
  }
}
\`\`\`

## 🔧 配置说明

### 环境变量
\`\`\`bash
# 服务配置
PORT=3000
NODE_ENV=production

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=dl_engine

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
\`\`\`

## 🚀 部署说明

### Docker部署
\`\`\`bash
# 构建镜像
docker build -t dl-engine/${serviceName} .

# 运行容器
docker run -p 3000:3000 dl-engine/${serviceName}
\`\`\`

### 本地开发
\`\`\`bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
npm start
\`\`\`

## 📊 监控指标

### 健康检查端点
- \`GET /health\` - 服务健康状态
- \`GET /metrics\` - Prometheus指标

### 关键指标
- 请求响应时间
- 错误率
- 内存使用率
- CPU使用率
- 数据库连接数

## 🔍 故障排除

### 常见问题
1. **服务启动失败**: 检查端口占用和环境变量配置
2. **数据库连接失败**: 验证数据库服务状态和连接参数
3. **内存使用过高**: 检查是否存在内存泄漏

### 日志查看
\`\`\`bash
# 查看服务日志
docker logs ${serviceName}

# 实时日志
docker logs -f ${serviceName}
\`\`\`

## 📞 支持联系

如有问题，请联系开发团队或查看主项目文档。
`;

    fs.writeFileSync(path.join(docsDir, 'api.md'), apiDoc);
  }

  generateReport() {
    const reportPath = path.join(this.serverDir, 'cleanup-reports',
      `documentation-enhancement-report-${new Date().toISOString().replace(/[:.]/g, '-')}.json`);

    const reportDir = path.dirname(reportPath);
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    fs.writeFileSync(reportPath, JSON.stringify(this.report, null, 2));
    console.log(`📊 报告已生成: ${reportPath}`);
  }

  printSummary() {
    console.log('\n📋 文档完善总结');
    console.log('='.repeat(50));
    console.log(`总服务数: ${this.report.total}`);
    console.log(`已处理: ${this.report.processed}`);
    console.log(`成功率: ${((this.report.processed / this.report.total) * 100).toFixed(1)}%`);

    console.log('\n✅ 文档完善完成！');
    console.log('\n📝 已完成的文档:');
    console.log('✅ 系统架构文档 (docs/architecture.md)');
    console.log('✅ 部署运维指南 (docs/deployment.md)');
    console.log('✅ 开发者指南 (docs/developer-guide.md)');
    console.log('✅ 故障排除手册 (docs/troubleshooting.md)');
    console.log('✅ 各服务API文档 (service/docs/api.md)');

    console.log('\n📚 文档位置:');
    console.log(`- 主文档目录: ${path.join(this.serverDir, 'docs')}`);
    console.log('- 各服务文档: [service-name]/docs/');
  }
}

// 执行文档完善
if (require.main === module) {
  const enhancer = new DocumentationEnhancer();
  enhancer.run().catch(console.error);
}

module.exports = DocumentationEnhancer;