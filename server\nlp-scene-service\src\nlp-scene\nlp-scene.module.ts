/**
 * NLP场景生成模块
 */
import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { NLPSceneController } from './nlp-scene.controller';
import { NLPSceneService } from './nlp-scene.service';
import { AIModelService } from './ai-model.service';
import { SceneStorageService } from './scene-storage.service';
import { GeneratedScene } from './entities/generated-scene.entity';
import { SceneTemplate } from './entities/scene-template.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([GeneratedScene, SceneTemplate]),
    HttpModule,
  ],
  controllers: [NLPSceneController],
  providers: [NLPSceneService, AIModelService, SceneStorageService],
  exports: [NLPSceneService],
})
export class NLPSceneModule {}
