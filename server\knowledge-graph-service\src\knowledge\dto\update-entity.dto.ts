import { IsString, <PERSON><PERSON><PERSON>, IsObject, IsNumber, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import type {  EntityType  } from '../knowledge-graph.service';

export class UpdateEntityDto {
  @ApiProperty({
    description: '实体类型',
    enum: EntityType,
    example: EntityType.EQUIPMENT,
    required: false,
  })
  @IsEnum(EntityType)
  @IsOptional()
  type?: EntityType;

  @ApiProperty({
    description: '实体名称',
    example: '数控机床-001',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: '实体属性',
    example: {
      model: 'CNC-2000',
      manufacturer: '某某机械',
      installDate: '2023-01-01',
    },
    required: false,
  })
  @IsObject()
  @IsOptional()
  properties?: Record<string, any>;

  @ApiProperty({
    description: '置信度',
    minimum: 0,
    maximum: 1,
    example: 0.9,
    required: false,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  confidence?: number;

  @ApiProperty({
    description: '数据来源',
    example: 'manual_update',
    required: false,
  })
  @IsString()
  @IsOptional()
  source?: string;
}
