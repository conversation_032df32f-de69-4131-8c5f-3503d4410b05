import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan, LessThanOrEqual } from 'typeorm';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { EventEmitter2 } from '@nestjs/event-emitter';
import type {  NotificationChannelEntity, NotificationChannelStatus, NotificationChannelType  } from './entities/notification-channel.entity';
import { NotificationHistoryEntity, NotificationStatus } from './entities/notification-history.entity';
import { AlertEntity, AlertSeverity } from '../alert/entities/alert.entity';
import { EmailNotifierService } from './notifiers/email-notifier.service';
import { WebhookNotifierService } from './notifiers/webhook-notifier.service';
import { SlackNotifierService } from './notifiers/slack-notifier.service';
import { DingTalkNotifierService } from './notifiers/dingtalk-notifier.service';
import { WeChatNotifierService } from './notifiers/wechat-notifier.service';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  private readonly notifiers = new Map<NotificationChannelType, any>();
  private readonly channelRateLimits = new Map<string, {
    count: number;
    resetTime: Date;
  }>();

  constructor(
    @InjectRepository(NotificationChannelEntity)
    private readonly channelRepository: Repository<NotificationChannelEntity>,
    @InjectRepository(NotificationHistoryEntity)
    private readonly historyRepository: Repository<NotificationHistoryEntity>,
    private readonly eventEmitter: EventEmitter2,
    private readonly emailNotifier: EmailNotifierService,
    private readonly webhookNotifier: WebhookNotifierService,
    private readonly slackNotifier: SlackNotifierService,
    private readonly dingtalkNotifier: DingTalkNotifierService,
    private readonly wechatNotifier: WeChatNotifierService,
  ) {
    // 注册通知器
    this.notifiers.set(NotificationChannelType.EMAIL, emailNotifier);
    this.notifiers.set(NotificationChannelType.WEBHOOK, webhookNotifier);
    this.notifiers.set(NotificationChannelType.SLACK, slackNotifier);
    this.notifiers.set(NotificationChannelType.DINGTALK, dingtalkNotifier);
    this.notifiers.set(NotificationChannelType.WECHAT, wechatNotifier);
  }

  /**
   * 发送告警通知
   */
  async sendAlertNotification(alert: AlertEntity): Promise<void> {
    try {
      // 获取适用的通知渠道
      const channels = await this.getApplicableChannels(alert);
      
      if (channels.length === 0) {
        this.logger.debug(`没有适用的通知渠道用于告警 ${alert.id}`);
        return;
      }
      
      // 为每个渠道发送通知
      for (const channel of channels) {
        let history: NotificationHistoryEntity | null = null;

        try {
          // 检查速率限制
          if (this.isRateLimited(channel)) {
            this.logger.warn(`通知渠道 ${channel.name} 已达到速率限制`);

            // 创建速率限制的通知历史记录
            await this.createNotificationHistory(
              channel,
              alert,
              `告警: ${alert.name}`,
              this.formatAlertContent(alert),
              NotificationStatus.RATE_LIMITED,
              '已达到速率限制',
            );

            continue;
          }

          // 创建待处理的通知历史记录
          history = await this.createNotificationHistory(
            channel,
            alert,
            `告警: ${alert.name}`,
            this.formatAlertContent(alert),
            NotificationStatus.PENDING,
          );

          // 发送通知
          const notifier = this.notifiers.get(channel.type);

          if (!notifier) {
            throw new Error(`未找到通知器: ${channel.type}`);
          }

          await notifier.send(channel, alert, history);

          // 更新通知历史记录为成功
          await this.updateNotificationStatus(history.id, NotificationStatus.SUCCESS);

          // 更新渠道的最后通知时间和计数
          await this.updateChannelStats(channel.id, true);

          // 更新速率限制计数
          this.incrementRateLimitCount(channel);
        } catch (error) {
          this.logger.error(`通过渠道 ${channel.name} 发送告警通知失败: ${error.message}`, error.stack);

          // 更新通知历史记录为失败（如果已创建）
          if (history) {
            await this.updateNotificationStatus(
              history.id,
              NotificationStatus.FAILED,
              error.message,
            );
          }

          // 更新渠道的错误计数和最后错误
          await this.updateChannelError(channel.id, error.message);
        }
      }
    } catch (error) {
      this.logger.error(`发送告警通知失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取适用的通知渠道
   */
  private async getApplicableChannels(alert: AlertEntity): Promise<NotificationChannelEntity[]> {
    try {
      const query = this.channelRepository.createQueryBuilder('channel')
        .where('channel.enabled = :enabled', { enabled: true })
        .andWhere('channel.status = :status', { status: NotificationChannelStatus.ACTIVE });
      
      // 过滤告警严重性
      query.andWhere(`
        channel.alertSeverities = '' OR
        FIND_IN_SET(:severity, channel.alertSeverities) > 0
      `, { severity: alert.severity });
      
      // 过滤服务类型
      if (alert.serviceType) {
        query.andWhere(`
          channel.serviceTypes = '' OR
          FIND_IN_SET(:serviceType, channel.serviceTypes) > 0
        `, { serviceType: alert.serviceType });
      }
      
      return query.getMany();
    } catch (error) {
      this.logger.error(`获取适用的通知渠道失败: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * 创建通知历史记录
   */
  private async createNotificationHistory(
    channel: NotificationChannelEntity,
    alert: AlertEntity,
    subject: string,
    content: string,
    status: NotificationStatus,
    errorMessage?: string,
  ): Promise<NotificationHistoryEntity> {
    const history = this.historyRepository.create({
      channelId: channel.id,
      alertId: alert.id,
      subject,
      content,
      data: {
        alert: {
          id: alert.id,
          name: alert.name,
          severity: alert.severity,
          status: alert.status,
          serviceId: alert.serviceId,
          serviceType: alert.serviceType,
          instanceId: alert.instanceId,
          hostname: alert.hostname,
          startTime: alert.startTime,
        },
      },
      status,
      errorMessage,
    });
    
    return this.historyRepository.save(history);
  }

  /**
   * 更新通知状态
   */
  private async updateNotificationStatus(
    id: string,
    status: NotificationStatus,
    errorMessage?: string,
  ): Promise<void> {
    await this.historyRepository.update(id, {
      status,
      errorMessage,
    });
  }

  /**
   * 更新渠道统计信息
   */
  private async updateChannelStats(id: string, success: boolean): Promise<void> {
    await this.channelRepository.update(id, {
      lastNotificationTime: new Date(),
      notificationCount: () => 'notificationCount + 1',
    });
  }

  /**
   * 更新渠道错误信息
   */
  private async updateChannelError(id: string, errorMessage: string): Promise<void> {
    await this.channelRepository.update(id, {
      errorCount: () => 'errorCount + 1',
      lastError: errorMessage,
    });
  }

  /**
   * 检查是否达到速率限制
   */
  private isRateLimited(channel: NotificationChannelEntity): boolean {
    if (channel.rateLimitPerMinute <= 0) {
      return false;
    }
    
    const now = new Date();
    const rateLimitInfo = this.channelRateLimits.get(channel.id);
    
    if (!rateLimitInfo) {
      // 没有速率限制信息，创建新的
      this.channelRateLimits.set(channel.id, {
        count: 1,
        resetTime: new Date(now.getTime() + 60000), // 1分钟后重置
      });
      
      return false;
    }
    
    if (now > rateLimitInfo.resetTime) {
      // 已经过了重置时间，重置计数
      rateLimitInfo.count = 1;
      rateLimitInfo.resetTime = new Date(now.getTime() + 60000);
      
      return false;
    }
    
    // 检查是否超过限制
    return rateLimitInfo.count >= channel.rateLimitPerMinute;
  }

  /**
   * 增加速率限制计数
   */
  private incrementRateLimitCount(channel: NotificationChannelEntity): void {
    const rateLimitInfo = this.channelRateLimits.get(channel.id);
    
    if (rateLimitInfo) {
      rateLimitInfo.count++;
    }
  }

  /**
   * 格式化告警内容
   */
  private formatAlertContent(alert: AlertEntity): string {
    const severityText = this.getSeverityText(alert.severity);
    
    let content = `
告警: ${alert.name}
严重性: ${severityText}
状态: ${alert.status}
时间: ${alert.startTime.toLocaleString()}
描述: ${alert.description}
`;
    
    if (alert.serviceType) {
      content += `服务类型: ${alert.serviceType}\n`;
    }
    
    if (alert.hostname) {
      content += `主机: ${alert.hostname}\n`;
    }
    
    if (alert.labels && Object.keys(alert.labels).length > 0) {
      content += `标签:\n`;
      
      for (const [key, value] of Object.entries(alert.labels)) {
        content += `  ${key}: ${value}\n`;
      }
    }
    
    if (alert.value && Object.keys(alert.value).length > 0) {
      content += `值:\n`;
      
      for (const [key, value] of Object.entries(alert.value)) {
        content += `  ${key}: ${value}\n`;
      }
    }
    
    return content;
  }

  /**
   * 获取严重性文本
   */
  private getSeverityText(severity: AlertSeverity): string {
    switch (severity) {
      case AlertSeverity.INFO:
        return '信息';
      case AlertSeverity.WARNING:
        return '警告';
      case AlertSeverity.ERROR:
        return '错误';
      case AlertSeverity.CRITICAL:
        return '严重';
      default:
        return severity;
    }
  }

  /**
   * 定时重试失败的通知
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async retryFailedNotifications(): Promise<void> {
    try {
      this.logger.debug('开始重试失败的通知');
      
      // 获取失败的通知
      const failedNotifications = await this.historyRepository.find({
        where: {
          status: NotificationStatus.FAILED,
          retryCount: LessThan(3), // 最多重试3次
          nextRetryTime: LessThanOrEqual(new Date()),
        },
        relations: ['channel'],
      });
      
      this.logger.debug(`找到 ${failedNotifications.length} 个失败的通知需要重试`);
      
      for (const notification of failedNotifications) {
        try {
          // 检查渠道是否可用
          if (!notification.channel || !notification.channel.enabled || notification.channel.status !== NotificationChannelStatus.ACTIVE) {
            this.logger.debug(`通知渠道不可用，跳过重试: ${notification.id}`);
            continue;
          }
          
          // 检查速率限制
          if (this.isRateLimited(notification.channel)) {
            this.logger.warn(`通知渠道 ${notification.channel.name} 已达到速率限制，稍后重试`);
            
            // 更新下次重试时间
            await this.historyRepository.update(notification.id, {
              nextRetryTime: new Date(Date.now() + 5 * 60 * 1000), // 5分钟后重试
            });
            
            continue;
          }
          
          // 获取通知器
          const notifier = this.notifiers.get(notification.channel.type);
          
          if (!notifier) {
            throw new Error(`未找到通知器: ${notification.channel.type}`);
          }
          
          // 重试发送通知
          await notifier.send(notification.channel, notification.data.alert, notification);
          
          // 更新通知状态为成功
          await this.historyRepository.update(notification.id, {
            status: NotificationStatus.SUCCESS,
            errorMessage: null,
          });
          
          // 更新渠道统计信息
          await this.updateChannelStats(notification.channel.id, true);
          
          // 更新速率限制计数
          this.incrementRateLimitCount(notification.channel);
        } catch (error) {
          this.logger.error(`重试通知 ${notification.id} 失败: ${error.message}`, error.stack);
          
          // 更新重试次数和下次重试时间
          await this.historyRepository.update(notification.id, {
            retryCount: notification.retryCount + 1,
            nextRetryTime: new Date(Date.now() + Math.pow(2, notification.retryCount + 1) * 60 * 1000), // 指数退避
            errorMessage: error.message,
          });
          
          // 更新渠道错误信息
          await this.updateChannelError(notification.channel.id, error.message);
        }
      }
      
      this.logger.debug('重试失败的通知完成');
    } catch (error) {
      this.logger.error(`重试失败的通知出错: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取通知历史
   */
  async getNotificationHistory(options: {
    page: number;
    limit: number;
    type?: string;
    status?: string;
  }): Promise<any> {
    // 简化实现，返回模拟数据
    return {
      notifications: [],
      total: 0,
      page: options.page,
      limit: options.limit,
    };
  }

  /**
   * 获取通知渠道
   */
  async getNotificationChannels(): Promise<any[]> {
    // 简化实现，返回模拟数据
    return [];
  }

  /**
   * 创建通知渠道
   */
  async createNotificationChannel(channelData: any): Promise<any> {
    // 简化实现
    return { id: 'new-channel', ...channelData };
  }

  /**
   * 更新通知渠道
   */
  async updateNotificationChannel(id: string, channelData: any): Promise<any> {
    // 简化实现
    return { id, ...channelData };
  }

  /**
   * 删除通知渠道
   */
  async deleteNotificationChannel(id: string): Promise<void> {
    // 简化实现
    this.logger.log(`删除通知渠道: ${id}`);
  }

  /**
   * 测试通知渠道
   */
  async testNotificationChannel(id: string): Promise<any> {
    // 简化实现
    return { success: true, message: '测试成功' };
  }

  /**
   * 切换通知渠道状态
   */
  async toggleNotificationChannel(id: string, enabled: boolean): Promise<any> {
    // 简化实现
    return { id, enabled };
  }

  /**
   * 获取通知统计
   */
  async getNotificationStats(options: {
    from?: Date;
    to?: Date;
  }): Promise<any> {
    // 简化实现，返回模拟数据
    return {
      total: 100,
      success: 85,
      failed: 15,
      byChannel: {},
      byType: {},
    };
  }
}
