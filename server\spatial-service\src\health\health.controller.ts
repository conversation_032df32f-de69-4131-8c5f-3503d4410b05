/**
 * 健康检查控制器
 */
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import type { 
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
 } from '@nestjs/terminus';
import { HealthService } from './health.service';

@ApiTags('健康检查')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
    private healthService: HealthService,
  ) {}

  @Get()
  @ApiOperation({ summary: '基础健康检查' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  @HealthCheck()
  check() {
    return this.health.check([
      // 数据库连接检查
      () => this.db.pingCheck('database'),
      
      // 内存使用检查
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
      
      // 磁盘空间检查
      () => this.disk.checkStorage('storage', {
        path: '/',
        thresholdPercent: 0.9,
      }),
    ]);
  }

  @Get('detailed')
  @ApiOperation({ summary: '详细健康检查' })
  @ApiResponse({ status: 200, description: '详细健康状态' })
  async detailedCheck() {
    return this.healthService.getDetailedHealth();
  }

  @Get('spatial')
  @ApiOperation({ summary: '空间服务健康检查' })
  @ApiResponse({ status: 200, description: '空间服务健康状态' })
  async spatialCheck() {
    return this.healthService.checkSpatialServices();
  }

  @Get('database')
  @ApiOperation({ summary: '数据库健康检查' })
  @ApiResponse({ status: 200, description: '数据库健康状态' })
  async databaseCheck() {
    return this.healthService.checkDatabaseHealth();
  }

  @Get('performance')
  @ApiOperation({ summary: '性能指标检查' })
  @ApiResponse({ status: 200, description: '性能指标' })
  async performanceCheck() {
    return this.healthService.getPerformanceMetrics();
  }
}
