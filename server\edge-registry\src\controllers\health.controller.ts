import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import type { 
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
 } from '@nestjs/terminus';

import { ApiResponseDto } from '../dto/response.dto';

/**
 * 健康检查控制器
 */
@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly db: TypeOrmHealthIndicator,
    private readonly memory: MemoryHealthIndicator,
    private readonly disk: DiskHealthIndicator,
  ) {}

  /**
   * 基础健康检查
   */
  @Get()
  @ApiOperation({ summary: '基础健康检查', description: '检查服务基本健康状态' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.db.pingCheck('database'),
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
    ]);
  }

  /**
   * 详细健康检查
   */
  @Get('detailed')
  @ApiOperation({ summary: '详细健康检查', description: '检查服务详细健康状态，包括数据库、内存、磁盘等' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  @HealthCheck()
  detailedCheck() {
    return this.health.check([
      () => this.db.pingCheck('database'),
      () => this.memory.checkHeap('memory_heap', 200 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 200 * 1024 * 1024),
      () => this.disk.checkStorage('storage', { 
        path: '/', 
        thresholdPercent: 0.9 
      }),
    ]);
  }

  /**
   * 就绪检查
   */
  @Get('ready')
  @ApiOperation({ summary: '就绪检查', description: '检查服务是否准备好接收请求' })
  @ApiResponse({ status: 200, description: '服务就绪' })
  @ApiResponse({ status: 503, description: '服务未就绪' })
  async readinessCheck(): Promise<ApiResponseDto> {
    try {
      await this.health.check([
        () => this.db.pingCheck('database'),
      ]);
      
      return ApiResponseDto.success('服务就绪');
    } catch (error) {
      return ApiResponseDto.error('服务未就绪', 'SERVICE_NOT_READY');
    }
  }

  /**
   * 存活检查
   */
  @Get('live')
  @ApiOperation({ summary: '存活检查', description: '检查服务是否存活' })
  @ApiResponse({ status: 200, description: '服务存活' })
  async livenessCheck(): Promise<ApiResponseDto> {
    return ApiResponseDto.success('服务存活');
  }
}
