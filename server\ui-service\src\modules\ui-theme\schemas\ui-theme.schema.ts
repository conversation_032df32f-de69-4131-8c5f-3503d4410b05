import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import type {  Document, Types  } from 'mongoose';

export type UIThemeDocument = UITheme & Document;

/**
 * 主题类型
 */
export enum ThemeType {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto',
  CUSTOM = 'custom',
}

/**
 * 主题状态
 */
export enum ThemeStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

/**
 * 颜色配置
 */
export interface ColorConfig {
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  shadow: string;
  [key: string]: string;
}

/**
 * 字体配置
 */
export interface FontConfig {
  primary: string;
  secondary: string;
  monospace: string;
  sizes: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
  };
  weights: {
    light: number;
    normal: number;
    medium: number;
    bold: number;
  };
}

/**
 * 间距配置
 */
export interface SpacingConfig {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  xxl: string;
}

/**
 * 圆角配置
 */
export interface BorderRadiusConfig {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  full: string;
}

/**
 * 阴影配置
 */
export interface ShadowConfig {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
}

/**
 * 过渡动画配置
 */
export interface TransitionConfig {
  duration: {
    fast: string;
    normal: string;
    slow: string;
  };
  easing: {
    ease: string;
    easeIn: string;
    easeOut: string;
    easeInOut: string;
  };
}

@Schema({
  timestamps: true,
  collection: 'ui_themes',
})
export class UITheme {
  @Prop({ required: true, maxlength: 100 })
  name: string;

  @Prop({ maxlength: 500 })
  description?: string;

  @Prop({ 
    type: String, 
    enum: Object.values(ThemeType),
    required: true,
    index: true 
  })
  type: ThemeType;

  @Prop({ 
    type: String, 
    enum: Object.values(ThemeStatus),
    default: ThemeStatus.DRAFT,
    index: true 
  })
  status: ThemeStatus;

  @Prop({ type: Object, required: true })
  colors: ColorConfig;

  @Prop({ type: Object, required: true })
  fonts: FontConfig;

  @Prop({ type: Object, required: true })
  spacing: SpacingConfig;

  @Prop({ type: Object, required: true })
  borderRadius: BorderRadiusConfig;

  @Prop({ type: Object, required: true })
  shadows: ShadowConfig;

  @Prop({ type: Object, required: true })
  transitions: TransitionConfig;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: Object })
  metadata?: {
    version?: string;
    author?: string;
    license?: string;
    homepage?: string;
    repository?: string;
    keywords?: string[];
    preview?: string;
    screenshots?: string[];
  };

  @Prop({ type: Types.ObjectId, index: true })
  organizationId?: Types.ObjectId;

  @Prop({ default: true })
  isPublic: boolean;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ type: Object, default: {} })
  statistics: {
    downloads?: number;
    likes?: number;
    rating?: number;
    ratingCount?: number;
    views?: number;
  };

  @Prop({ type: Object, default: {} })
  settings: {
    allowFork?: boolean;
    allowComments?: boolean;
    allowRating?: boolean;
    requireApproval?: boolean;
  };

  @Prop({ type: Types.ObjectId, required: true })
  createdBy: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true })
  updatedBy: Types.ObjectId;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;

  @Prop({ type: Date })
  deletedAt?: Date;

  @Prop({ type: Types.ObjectId })
  deletedBy?: Types.ObjectId;
}

export const UIThemeSchema = SchemaFactory.createForClass(UITheme);

// 创建索引
UIThemeSchema.index({ name: 1, organizationId: 1 }, { unique: true });
UIThemeSchema.index({ type: 1, status: 1 });
UIThemeSchema.index({ tags: 1 });
UIThemeSchema.index({ isPublic: 1, isActive: 1 });
UIThemeSchema.index({ 'statistics.rating': -1 });
UIThemeSchema.index({ 'statistics.downloads': -1 });
UIThemeSchema.index({ createdAt: -1 });

// 添加文本搜索索引
UIThemeSchema.index({
  name: 'text',
  description: 'text',
  tags: 'text',
  'metadata.keywords': 'text',
});

// 添加实例方法
UIThemeSchema.methods.canAccess = function(userId: Types.ObjectId, organizationId?: Types.ObjectId): boolean {
  if (this.isPublic) {
    return true;
  }
  
  if (this.organizationId && organizationId) {
    return this.organizationId.equals(organizationId);
  }
  
  return this.createdBy.equals(userId);
};

UIThemeSchema.methods.canEdit = function(userId: Types.ObjectId): boolean {
  return this.createdBy.equals(userId);
};

UIThemeSchema.methods.incrementDownloads = function(): Promise<UITheme> {
  this.statistics.downloads = (this.statistics.downloads || 0) + 1;
  return this.save();
};

UIThemeSchema.methods.incrementViews = function(): Promise<UITheme> {
  this.statistics.views = (this.statistics.views || 0) + 1;
  return this.save();
};

UIThemeSchema.methods.softDelete = function(userId: Types.ObjectId): Promise<UITheme> {
  this.deletedAt = new Date();
  this.deletedBy = userId;
  this.isActive = false;
  return this.save();
};

UIThemeSchema.methods.restore = function(): Promise<UITheme> {
  this.deletedAt = undefined;
  this.deletedBy = undefined;
  this.isActive = true;
  return this.save();
};
