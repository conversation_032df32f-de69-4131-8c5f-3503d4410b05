/**
 * 相机管理节点集合
 * 批次1.1：相机管理节点 (6个)
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { 
  Camera,
  PerspectiveCamera,
  OrthographicCamera,
  Vector3,
  Quaternion,
  Euler,
  Scene,
  Object3D
} from 'three';

/**
 * 相机类型枚举
 */
export enum CameraType {
  PERSPECTIVE = 'perspective',
  ORTHOGRAPHIC = 'orthographic'
}

/**
 * 相机控制模式枚举
 */
export enum CameraControlMode {
  ORBIT = 'orbit',
  FLY = 'fly',
  FIRST_PERSON = 'firstPerson',
  THIRD_PERSON = 'thirdPerson',
  FIXED = 'fixed'
}

/**
 * 相机管理器
 */
class CameraManager {
  private cameras: Map<string, Camera> = new Map();
  private activeCamera: string | null = null;
  private scene: Scene | null = null;
  private eventListeners: Map<string, Function[]> = new Map();
  private cameraControls: Map<string, any> = new Map();

  /**
   * 设置场景
   */
  setScene(scene: Scene): void {
    this.scene = scene;
  }

  /**
   * 创建相机
   */
  createCamera(id: string, type: CameraType, config: any): Camera {
    let camera: Camera;

    switch (type) {
      case CameraType.PERSPECTIVE:
        camera = new PerspectiveCamera(
          config.fov || 75,
          config.aspect || window.innerWidth / window.innerHeight,
          config.near || 0.1,
          config.far || 1000
        );
        break;

      case CameraType.ORTHOGRAPHIC:
        const width = config.width || window.innerWidth;
        const height = config.height || window.innerHeight;
        const zoom = config.zoom || 1;
        camera = new OrthographicCamera(
          -width / 2 / zoom,
          width / 2 / zoom,
          height / 2 / zoom,
          -height / 2 / zoom,
          config.near || 0.1,
          config.far || 1000
        );
        break;

      default:
        throw new Error(`不支持的相机类型: ${type}`);
    }

    // 设置位置和旋转
    if (config.position) camera.position.copy(config.position);
    if (config.rotation) camera.rotation.copy(config.rotation);
    if (config.lookAt) camera.lookAt(config.lookAt);

    this.cameras.set(id, camera);

    // 添加到场景
    if (this.scene) {
      this.scene.add(camera);
    }

    this.emit('cameraCreated', { id, camera, type });
    Debug.log('CameraManager', `相机创建成功: ${id} (${type})`);

    return camera;
  }

  /**
   * 获取相机
   */
  getCamera(id: string): Camera | undefined {
    return this.cameras.get(id);
  }

  /**
   * 删除相机
   */
  removeCamera(id: string): boolean {
    const camera = this.cameras.get(id);
    if (!camera) return false;

    // 从场景移除
    if (this.scene) {
      this.scene.remove(camera);
    }

    // 如果是活动相机，清除活动状态
    if (this.activeCamera === id) {
      this.activeCamera = null;
    }

    this.cameras.delete(id);
    this.cameraControls.delete(id);
    this.emit('cameraRemoved', { id, camera });
    Debug.log('CameraManager', `相机删除成功: ${id}`);

    return true;
  }

  /**
   * 设置活动相机
   */
  setActiveCamera(id: string): boolean {
    const camera = this.cameras.get(id);
    if (!camera) return false;

    const previousCamera = this.activeCamera;
    this.activeCamera = id;

    this.emit('activeCameraChanged', { previousCamera, currentCamera: id, camera });
    Debug.log('CameraManager', `活动相机切换: ${previousCamera} -> ${id}`);

    return true;
  }

  /**
   * 获取活动相机
   */
  getActiveCamera(): Camera | null {
    return this.activeCamera ? this.cameras.get(this.activeCamera) || null : null;
  }

  /**
   * 更新相机属性
   */
  updateCamera(id: string, properties: any): boolean {
    const camera = this.cameras.get(id);
    if (!camera) return false;

    if (properties.position) camera.position.copy(properties.position);
    if (properties.rotation) camera.rotation.copy(properties.rotation);
    if (properties.lookAt) camera.lookAt(properties.lookAt);

    // 透视相机特定属性
    if (camera instanceof PerspectiveCamera) {
      if (properties.fov !== undefined) {
        camera.fov = properties.fov;
        camera.updateProjectionMatrix();
      }
      if (properties.aspect !== undefined) {
        camera.aspect = properties.aspect;
        camera.updateProjectionMatrix();
      }
    }

    // 正交相机特定属性
    if (camera instanceof OrthographicCamera) {
      if (properties.zoom !== undefined) {
        camera.zoom = properties.zoom;
        camera.updateProjectionMatrix();
      }
    }

    // 通用属性
    if (properties.near !== undefined) {
      camera.near = properties.near;
      camera.updateProjectionMatrix();
    }
    if (properties.far !== undefined) {
      camera.far = properties.far;
      camera.updateProjectionMatrix();
    }

    this.emit('cameraUpdated', { id, camera, properties });
    return true;
  }

  /**
   * 设置相机控制
   */
  setCameraControl(id: string, mode: CameraControlMode, config?: any): boolean {
    const camera = this.cameras.get(id);
    if (!camera) return false;

    const controlConfig = {
      mode,
      target: config?.target || new Vector3(0, 0, 0),
      distance: config?.distance || 10,
      minDistance: config?.minDistance || 1,
      maxDistance: config?.maxDistance || 100,
      enableDamping: config?.enableDamping ?? true,
      dampingFactor: config?.dampingFactor || 0.05,
      enableZoom: config?.enableZoom ?? true,
      enableRotate: config?.enableRotate ?? true,
      enablePan: config?.enablePan ?? true
    };

    this.cameraControls.set(id, controlConfig);
    this.emit('cameraControlSet', { id, camera, mode, config: controlConfig });
    Debug.log('CameraManager', `相机控制设置: ${id} (${mode})`);

    return true;
  }

  /**
   * 获取相机控制
   */
  getCameraControl(id: string): any {
    return this.cameraControls.get(id);
  }

  /**
   * 获取所有相机ID
   */
  getAllCameraIds(): string[] {
    return Array.from(this.cameras.keys());
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('CameraManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 清理所有相机
   */
  cleanup(): void {
    for (const camera of this.cameras.values()) {
      if (this.scene) {
        this.scene.remove(camera);
      }
    }
    this.cameras.clear();
    this.cameraControls.clear();
    this.activeCamera = null;
    this.eventListeners.clear();
  }
}

// 全局相机管理器实例
const globalCameraManager = new CameraManager();

/**
 * 创建相机节点
 */
export class CreateCameraNode extends VisualScriptNode {
  public static readonly TYPE = 'CreateCamera';
  public static readonly NAME = '创建相机';
  public static readonly DESCRIPTION = '创建透视或正交相机';

  constructor(nodeType: string = CreateCameraNode.TYPE, name: string = CreateCameraNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建');
    this.addInput('cameraId', 'string', '相机ID');
    this.addInput('cameraType', 'string', '相机类型');
    this.addInput('fov', 'number', '视野角度');
    this.addInput('aspect', 'number', '宽高比');
    this.addInput('near', 'number', '近裁剪面');
    this.addInput('far', 'number', '远裁剪面');
    this.addInput('position', 'object', '相机位置');
    this.addInput('lookAt', 'object', '观察目标');

    // 输出端口
    this.addOutput('camera', 'object', '相机对象');
    this.addOutput('cameraId', 'string', '相机ID');
    this.addOutput('cameraType', 'string', '相机类型');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onError', 'trigger', '创建失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      if (!createTrigger) {
        return this.getDefaultOutputs();
      }

      const cameraId = inputs?.cameraId as string || this.generateCameraId();
      const cameraType = inputs?.cameraType as string || 'perspective';
      const fov = inputs?.fov as number ?? 75;
      const aspect = inputs?.aspect as number ?? window.innerWidth / window.innerHeight;
      const near = inputs?.near as number ?? 0.1;
      const far = inputs?.far as number ?? 1000;
      const position = inputs?.position as Vector3 || new Vector3(0, 0, 5);
      const lookAt = inputs?.lookAt as Vector3 || new Vector3(0, 0, 0);

      const config = {
        fov,
        aspect,
        near,
        far,
        position,
        lookAt
      };

      const camera = globalCameraManager.createCamera(cameraId, cameraType as CameraType, config);

      Debug.log('CreateCameraNode', `相机创建成功: ${cameraId} (${cameraType})`);

      return {
        camera,
        cameraId,
        cameraType,
        onCreated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('CreateCameraNode', '相机创建失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private generateCameraId(): string {
    return 'camera_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      camera: null,
      cameraId: '',
      cameraType: '',
      onCreated: false,
      onError: false
    };
  }
}
