import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import type {  PerformanceMetric, MetricType, MetricUnit  } from './entities/performance-metric.entity';

/**
 * 指标收集服务
 */
@Injectable()
export class MetricsCollectionService {
  private readonly logger = new Logger(MetricsCollectionService.name);

  constructor(
    @InjectRepository(PerformanceMetric)
    private readonly metricRepository: Repository<PerformanceMetric>,
  ) {}

  /**
   * 获取指标数据
   */
  async getMetrics(options: {
    metricType?: string;
    source?: string;
    startTime?: Date;
    endTime?: Date;
    limit?: number;
  }): Promise<{ metrics: PerformanceMetric[]; total: number }> {
    try {
      const { metricType, source, startTime, endTime, limit = 100 } = options;

      const queryBuilder = this.metricRepository.createQueryBuilder('metric');

      if (metricType) {
        queryBuilder.andWhere('metric.metricType = :metricType', { metricType });
      }

      if (source) {
        queryBuilder.andWhere('metric.source = :source', { source });
      }

      if (startTime) {
        queryBuilder.andWhere('metric.timestamp >= :startTime', { startTime });
      }

      if (endTime) {
        queryBuilder.andWhere('metric.timestamp <= :endTime', { endTime });
      }

      const [metrics, total] = await queryBuilder
        .orderBy('metric.timestamp', 'DESC')
        .take(limit)
        .getManyAndCount();

      return { metrics, total };
    } catch (error) {
      this.logger.error(`获取指标数据失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 创建自定义指标
   */
  async createCustomMetric(metricData: {
    metricName: string;
    metricType: string;
    source: string;
    value: number;
    unit?: string;
    tags?: Record<string, string>;
    targetValue?: number;
    thresholdMin?: number;
    thresholdMax?: number;
  }): Promise<PerformanceMetric> {
    try {
      const metric = this.metricRepository.create({
        ...metricData,
        metricType: metricData.metricType as MetricType,
        unit: (metricData.unit as MetricUnit) || MetricUnit.COUNT,
        timestamp: new Date(),
      });

      // 检查是否异常
      metric.isAnomaly = this.detectAnomaly(metric);
      if (metric.isAnomaly) {
        metric.anomalyScore = this.calculateAnomalyScore(metric);
      }

      const savedMetric = await this.metricRepository.save(metric);
      this.logger.log(`自定义指标创建成功: ${savedMetric.metricName}`);

      return savedMetric;
    } catch (error) {
      this.logger.error(`创建自定义指标失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量收集指标
   */
  async collectMetrics(metrics: any[]): Promise<void> {
    try {
      const metricEntities = metrics.map(metric =>
        this.metricRepository.create({
          ...metric,
          timestamp: new Date(),
          isAnomaly: this.detectAnomaly(metric),
        })
      );

      await this.metricRepository.save(metricEntities as any);
      this.logger.log(`批量收集指标成功: ${metrics.length} 个指标`);
    } catch (error) {
      this.logger.error(`批量收集指标失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取指标聚合数据
   */
  async getAggregatedMetrics(options: {
    metricType: string;
    source?: string;
    aggregation: 'avg' | 'sum' | 'min' | 'max' | 'count';
    period: 'hour' | 'day' | 'week' | 'month';
    startTime?: Date;
    endTime?: Date;
  }): Promise<any[]> {
    try {
      const { metricType, source, aggregation, period, startTime, endTime } = options;

      // 构建聚合查询
      const queryBuilder = this.metricRepository.createQueryBuilder('metric');

      queryBuilder.select([
        this.getDateTruncFunction(period) + ' as period',
        `${aggregation.toUpperCase()}(metric.value) as value`,
      ]);

      queryBuilder.where('metric.metricType = :metricType', { metricType });

      if (source) {
        queryBuilder.andWhere('metric.source = :source', { source });
      }

      if (startTime) {
        queryBuilder.andWhere('metric.timestamp >= :startTime', { startTime });
      }

      if (endTime) {
        queryBuilder.andWhere('metric.timestamp <= :endTime', { endTime });
      }

      queryBuilder.groupBy(this.getDateTruncFunction(period));
      queryBuilder.orderBy('period', 'ASC');

      const results = await queryBuilder.getRawMany();

      return results.map(result => ({
        period: result.period,
        value: parseFloat(result.value),
      }));
    } catch (error) {
      this.logger.error(`获取聚合指标失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取指标统计
   */
  async getMetricStatistics(metricType: string, period: string = 'day'): Promise<any> {
    try {
      const now = new Date();
      let startDate: Date;

      switch (period) {
        case 'hour':
          startDate = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case 'day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }

      const metrics = await this.metricRepository.find({
        where: {
          metricType: metricType as MetricType,
          timestamp: {
            $gte: startDate,
          } as any,
        },
        order: { timestamp: 'ASC' },
      });

      if (metrics.length === 0) {
        return {
          count: 0,
          average: 0,
          min: 0,
          max: 0,
          trend: 'stable',
        };
      }

      const values = metrics.map(m => Number(m.value));
      const average = values.reduce((sum, val) => sum + val, 0) / values.length;
      const min = Math.min(...values);
      const max = Math.max(...values);

      // 计算趋势
      const trend = this.calculateTrend(values);

      return {
        count: metrics.length,
        average: Math.round(average * 100) / 100,
        min,
        max,
        trend,
        anomalyCount: metrics.filter(m => m.isAnomaly).length,
        dataQuality: metrics.reduce((sum, m) => sum + Number(m.dataQuality), 0) / metrics.length,
      };
    } catch (error) {
      this.logger.error(`获取指标统计失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 检测异常
   */
  private detectAnomaly(metric: any): boolean {
    // 简单的阈值检测
    if (metric.thresholdMin !== undefined && metric.value < metric.thresholdMin) {
      return true;
    }
    if (metric.thresholdMax !== undefined && metric.value > metric.thresholdMax) {
      return true;
    }
    return false;
  }

  /**
   * 计算异常评分
   */
  private calculateAnomalyScore(metric: any): number {
    // 简化的异常评分计算
    if (metric.thresholdMin !== undefined && metric.value < metric.thresholdMin) {
      return Math.min(1, (metric.thresholdMin - metric.value) / metric.thresholdMin);
    }
    if (metric.thresholdMax !== undefined && metric.value > metric.thresholdMax) {
      return Math.min(1, (metric.value - metric.thresholdMax) / metric.thresholdMax);
    }
    return 0;
  }

  /**
   * 获取日期截断函数
   */
  private getDateTruncFunction(period: string): string {
    // 这里需要根据具体数据库类型调整
    switch (period) {
      case 'hour':
        return "DATE_FORMAT(metric.timestamp, '%Y-%m-%d %H:00:00')";
      case 'day':
        return "DATE_FORMAT(metric.timestamp, '%Y-%m-%d')";
      case 'week':
        return "DATE_FORMAT(metric.timestamp, '%Y-%u')";
      case 'month':
        return "DATE_FORMAT(metric.timestamp, '%Y-%m')";
      default:
        return "DATE_FORMAT(metric.timestamp, '%Y-%m-%d')";
    }
  }

  /**
   * 计算趋势
   */
  private calculateTrend(values: number[]): string {
    if (values.length < 2) return 'stable';

    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));

    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;

    const change = ((secondAvg - firstAvg) / firstAvg) * 100;

    if (change > 5) return 'increasing';
    if (change < -5) return 'decreasing';
    return 'stable';
  }
}
