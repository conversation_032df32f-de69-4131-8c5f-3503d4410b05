import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import type {  Types  } from 'mongoose';
import { UIThemeService } from './ui-theme.service';
import { CreateUIThemeDto, UpdateUIThemeDto, QueryUIThemeDto } from './dto/ui-theme.dto';

@ApiTags('ui-theme')
@Controller('ui-theme')
// @UseGuards(JwtAuthGuard) // 需要实现认证守卫
export class UIThemeController {
  constructor(private readonly themeService: UIThemeService) {}

  @Post()
  @ApiOperation({ summary: '创建UI主题' })
  @ApiResponse({ status: HttpStatus.CREATED, description: '主题创建成功' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: '请求参数错误' })
  @ApiBearerAuth()
  async create(@Body() createThemeDto: CreateUIThemeDto, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.themeService.create(createThemeDto, userId);
  }

  @Get()
  @ApiOperation({ summary: '获取UI主题列表' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiBearerAuth()
  async findAll(@Query() queryDto: QueryUIThemeDto, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.themeService.findAll(queryDto, userId);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取单个UI主题' })
  @ApiParam({ name: 'id', description: '主题ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '主题不存在' })
  @ApiBearerAuth()
  async findOne(@Param('id') id: string, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.themeService.findOne(id, userId);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新UI主题' })
  @ApiParam({ name: 'id', description: '主题ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '更新成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '主题不存在' })
  @ApiBearerAuth()
  async update(
    @Param('id') id: string,
    @Body() updateThemeDto: UpdateUIThemeDto,
    @Request() req: any,
  ) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.themeService.update(id, updateThemeDto, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除UI主题' })
  @ApiParam({ name: 'id', description: '主题ID' })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: '删除成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '主题不存在' })
  @ApiBearerAuth()
  async remove(@Param('id') id: string, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    await this.themeService.remove(id, userId);
  }

  @Post(':id/publish')
  @ApiOperation({ summary: '发布UI主题' })
  @ApiParam({ name: 'id', description: '主题ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '发布成功' })
  @ApiBearerAuth()
  async publish(@Param('id') id: string, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.themeService.publish(id, userId);
  }

  @Post(':id/download')
  @ApiOperation({ summary: '下载UI主题' })
  @ApiParam({ name: 'id', description: '主题ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '下载成功' })
  @ApiBearerAuth()
  async download(@Param('id') id: string, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.themeService.download(id, userId);
  }
}
