import { IsString, IsEnum, IsOptional, IsObject, IsBoolean, IsDateString, IsMongoId, MaxLength, IsNumber, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import type {  Transform, Type  } from 'class-transformer';
import type {  ConfigType, ConfigScope, ConfigData  } from '../schemas/ui-config.schema';

export class CreateUIConfigDto {
  @ApiProperty({ description: '配置名称', maxLength: 100 })
  @IsString()
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({ description: '配置描述', maxLength: 500 })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({ description: '配置类型', enum: ConfigType })
  @IsEnum(ConfigType)
  type: ConfigType;

  @ApiProperty({ description: '配置范围', enum: ConfigScope })
  @IsEnum(ConfigScope)
  scope: ConfigScope;

  @ApiPropertyOptional({ description: '用户ID' })
  @IsOptional()
  @IsMongoId()
  userId?: string;

  @ApiPropertyOptional({ description: '组织ID' })
  @IsOptional()
  @IsMongoId()
  organizationId?: string;

  @ApiPropertyOptional({ description: '项目ID' })
  @IsOptional()
  @IsMongoId()
  projectId?: string;

  @ApiProperty({ description: '配置数据', type: 'object' })
  @IsObject()
  data: ConfigData;

  @ApiPropertyOptional({ 
    description: '元数据',
    type: 'object',
    properties: {
      version: { type: 'string' },
      tags: { type: 'array', items: { type: 'string' } },
      category: { type: 'string' },
      priority: { type: 'number' },
      readonly: { type: 'boolean' },
      encrypted: { type: 'boolean' }
    }
  })
  @IsOptional()
  @IsObject()
  metadata?: {
    version?: string;
    tags?: string[];
    category?: string;
    priority?: number;
    readonly?: boolean;
    encrypted?: boolean;
  };

  @ApiPropertyOptional({ description: '过期时间' })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;
}

export class UpdateUIConfigDto {
  @ApiPropertyOptional({ description: '配置名称', maxLength: 100 })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({ description: '配置描述', maxLength: 500 })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({ description: '配置数据', type: 'object' })
  @IsOptional()
  @IsObject()
  data?: ConfigData;

  @ApiPropertyOptional({ description: '元数据', type: 'object' })
  @IsOptional()
  @IsObject()
  metadata?: {
    version?: string;
    tags?: string[];
    category?: string;
    priority?: number;
    readonly?: boolean;
    encrypted?: boolean;
  };

  @ApiPropertyOptional({ description: '是否激活' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({ description: '过期时间' })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;
}

export class QueryUIConfigDto {
  @ApiPropertyOptional({ description: '页码', minimum: 1, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100, default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: '配置类型', enum: ConfigType })
  @IsOptional()
  @IsEnum(ConfigType)
  type?: ConfigType;

  @ApiPropertyOptional({ description: '配置范围', enum: ConfigScope })
  @IsOptional()
  @IsEnum(ConfigScope)
  scope?: ConfigScope;

  @ApiPropertyOptional({ description: '用户ID' })
  @IsOptional()
  @IsMongoId()
  userId?: string;

  @ApiPropertyOptional({ description: '组织ID' })
  @IsOptional()
  @IsMongoId()
  organizationId?: string;

  @ApiPropertyOptional({ description: '项目ID' })
  @IsOptional()
  @IsMongoId()
  projectId?: string;

  @ApiPropertyOptional({ description: '分类' })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ description: '标签' })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiPropertyOptional({ description: '是否激活' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({ description: '排序字段', default: 'createdAt' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', enum: ['asc', 'desc'], default: 'desc' })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}
