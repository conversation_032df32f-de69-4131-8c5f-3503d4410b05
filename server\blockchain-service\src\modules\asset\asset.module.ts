/**
 * 资产模块
 */

import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { AssetService } from './asset.service';
import { AssetController } from './asset.controller';
import { BlockchainAsset } from '../../entities/blockchain-asset.entity';
import { AssetMetadata } from '../../entities/asset-metadata.entity';

@Module({
  imports: [TypeOrmModule.forFeature([BlockchainAsset, AssetMetadata])],
  controllers: [AssetController],
  providers: [AssetService],
  exports: [AssetService],
})
export class AssetModule {}
