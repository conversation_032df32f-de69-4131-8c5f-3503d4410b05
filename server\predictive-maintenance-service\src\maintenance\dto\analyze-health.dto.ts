/**
 * 分析设备健康状态DTO
 */

import { IsString, IsOptional, IsDateString, ValidateNested } from 'class-validator';
import type {  Type  } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

class TimeRangeDto {
  @ApiProperty({ description: '开始时间', example: '2024-01-01T00:00:00.000Z' })
  @IsDateString()
  start: string;

  @ApiProperty({ description: '结束时间', example: '2024-01-07T23:59:59.999Z' })
  @IsDateString()
  end: string;
}

export class AnalyzeHealthDto {
  @ApiProperty({ 
    description: '设备ID', 
    example: 'device-001',
    minLength: 1,
    maxLength: 50
  })
  @IsString()
  deviceId: string;

  @ApiPropertyOptional({ 
    description: '时间范围',
    type: TimeRangeDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => TimeRangeDto)
  timeRange?: {
    start: Date;
    end: Date;
  };
}
