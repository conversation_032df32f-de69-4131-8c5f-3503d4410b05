# learning-tracking-service API文档

## 📋 服务概述

learning-tracking-service 是DL引擎微服务架构中的一个核心服务。

### 基本信息
- **服务名称**: learning-tracking-service
- **版本**: 1.0.0
- **端口**: 3000 (默认)
- **协议**: HTTP/REST

## 🔗 API端点

### 健康检查
```
GET /health
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "status": "ok",
    "info": {
      "database": {
        "status": "up"
      }
    }
  },
  "timestamp": "2025-07-02T13:47:09.508Z"
}
```

### 服务信息
```
GET /info
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "name": "learning-tracking-service",
    "version": "1.0.0",
    "description": "DL引擎learning-tracking-service服务",
    "uptime": 3600
  }
}
```

## 🔧 配置说明

### 环境变量
```bash
# 服务配置
PORT=3000
NODE_ENV=production

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=dl_engine

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
```

## 🚀 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t dl-engine/learning-tracking-service .

# 运行容器
docker run -p 3000:3000 dl-engine/learning-tracking-service
```

### 本地开发
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
npm start
```

## 📊 监控指标

### 健康检查端点
- `GET /health` - 服务健康状态
- `GET /metrics` - Prometheus指标

### 关键指标
- 请求响应时间
- 错误率
- 内存使用率
- CPU使用率
- 数据库连接数

## 🔍 故障排除

### 常见问题
1. **服务启动失败**: 检查端口占用和环境变量配置
2. **数据库连接失败**: 验证数据库服务状态和连接参数
3. **内存使用过高**: 检查是否存在内存泄漏

### 日志查看
```bash
# 查看服务日志
docker logs learning-tracking-service

# 实时日志
docker logs -f learning-tracking-service
```

## 📞 支持联系

如有问题，请联系开发团队或查看主项目文档。
