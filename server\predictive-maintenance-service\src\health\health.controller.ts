/**
 * 健康检查控制器
 */

import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import type {  
  HealthCheckService, 
  HealthCheck, 
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator
 } from '@nestjs/terminus';
import { HealthService } from './health.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly db: TypeOrmHealthIndicator,
    private readonly memory: MemoryHealthIndicator,
    private readonly disk: DiskHealthIndicator,
    private readonly healthService: HealthService,
  ) {}

  @Get()
  @ApiOperation({ summary: '综合健康检查' })
  @ApiResponse({ 
    status: 200, 
    description: '服务健康',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        info: { type: 'object' },
        error: { type: 'object' },
        details: { type: 'object' }
      }
    }
  })
  @ApiResponse({ status: 503, description: '服务不健康' })
  @HealthCheck()
  check() {
    return this.health.check([
      // 数据库健康检查
      () => this.db.pingCheck('database'),
      
      // 内存使用检查
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
      
      // 磁盘空间检查
      () => this.disk.checkStorage('storage', { 
        path: '/', 
        thresholdPercent: 0.9 
      }),
      
      // 自定义健康检查
      () => this.healthService.checkAIModels(),
      () => this.healthService.checkRedisConnection(),
      () => this.healthService.checkDeviceConnections(),
    ]);
  }

  @Get('database')
  @ApiOperation({ summary: '数据库健康检查' })
  @ApiResponse({ status: 200, description: '数据库连接正常' })
  @ApiResponse({ status: 503, description: '数据库连接异常' })
  @HealthCheck()
  checkDatabase() {
    return this.health.check([
      () => this.db.pingCheck('database'),
    ]);
  }

  @Get('memory')
  @ApiOperation({ summary: '内存使用检查' })
  @ApiResponse({ status: 200, description: '内存使用正常' })
  @ApiResponse({ status: 503, description: '内存使用过高' })
  @HealthCheck()
  checkMemory() {
    return this.health.check([
      () => this.memory.checkHeap('memory_heap', 200 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 200 * 1024 * 1024),
    ]);
  }

  @Get('ai-models')
  @ApiOperation({ summary: 'AI模型状态检查' })
  @ApiResponse({ status: 200, description: 'AI模型状态正常' })
  @ApiResponse({ status: 503, description: 'AI模型状态异常' })
  @HealthCheck()
  checkAIModels() {
    return this.health.check([
      () => this.healthService.checkAIModels(),
    ]);
  }

  @Get('redis')
  @ApiOperation({ summary: 'Redis连接检查' })
  @ApiResponse({ status: 200, description: 'Redis连接正常' })
  @ApiResponse({ status: 503, description: 'Redis连接异常' })
  @HealthCheck()
  checkRedis() {
    return this.health.check([
      () => this.healthService.checkRedisConnection(),
    ]);
  }

  @Get('devices')
  @ApiOperation({ summary: '设备连接检查' })
  @ApiResponse({ status: 200, description: '设备连接正常' })
  @ApiResponse({ status: 503, description: '设备连接异常' })
  @HealthCheck()
  checkDevices() {
    return this.health.check([
      () => this.healthService.checkDeviceConnections(),
    ]);
  }

  @Get('detailed')
  @ApiOperation({ summary: '详细健康状态' })
  @ApiResponse({ 
    status: 200, 
    description: '详细健康状态信息',
    schema: {
      type: 'object',
      properties: {
        service: { type: 'string' },
        status: { type: 'string' },
        timestamp: { type: 'string' },
        uptime: { type: 'number' },
        version: { type: 'string' },
        environment: { type: 'string' },
        database: { type: 'object' },
        redis: { type: 'object' },
        aiModels: { type: 'object' },
        devices: { type: 'object' },
        system: { type: 'object' }
      }
    }
  })
  async getDetailedHealth() {
    return await this.healthService.getDetailedHealthStatus();
  }
}
