/**
 * 空间分析模块
 */
import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { SpatialAnalysisController } from './spatial-analysis.controller';
import { SpatialAnalysisService } from './spatial-analysis.service';
import { GeometryService } from './geometry.service';
import { TopologyService } from './topology.service';
import { BufferAnalysisService } from './buffer-analysis.service';
import { OverlayAnalysisService } from './overlay-analysis.service';
import { NetworkAnalysisService } from './network-analysis.service';
import { StatisticalAnalysisService } from './statistical-analysis.service';
import { SpatialFeature } from '../entities/spatial-feature.entity';
import { SpatialLayer } from '../entities/spatial-layer.entity';
import { SpatialProject } from '../entities/spatial-project.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SpatialFeature,
      SpatialLayer,
      SpatialProject
    ])
  ],
  controllers: [SpatialAnalysisController],
  providers: [
    SpatialAnalysisService,
    GeometryService,
    TopologyService,
    BufferAnalysisService,
    OverlayAnalysisService,
    NetworkAnalysisService,
    StatisticalAnalysisService
  ],
  exports: [
    SpatialAnalysisService,
    GeometryService,
    TopologyService,
    BufferAnalysisService,
    OverlayAnalysisService,
    NetworkAnalysisService,
    StatisticalAnalysisService
  ]
})
export class SpatialAnalysisModule {}
