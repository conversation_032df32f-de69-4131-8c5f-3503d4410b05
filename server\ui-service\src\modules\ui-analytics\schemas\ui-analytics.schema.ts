import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import type {  Document, Types  } from 'mongoose';

export type UIAnalyticsDocument = UIAnalytics & Document;

export enum EventType {
  VIEW = 'view',
  CLICK = 'click',
  DOWNLOAD = 'download',
  LIKE = 'like',
  SHARE = 'share',
  COMMENT = 'comment',
  EDIT = 'edit',
  CREATE = 'create',
  DELETE = 'delete',
}

export enum ResourceType {
  TEMPLATE = 'template',
  COMPONENT = 'component',
  THEME = 'theme',
  CONFIG = 'config',
}

@Schema({
  timestamps: true,
  collection: 'ui_analytics',
})
export class UIAnalytics {
  @Prop({ 
    type: String, 
    enum: Object.values(EventType),
    required: true,
    index: true 
  })
  eventType: EventType;

  @Prop({ 
    type: String, 
    enum: Object.values(ResourceType),
    required: true,
    index: true 
  })
  resourceType: ResourceType;

  @Prop({ type: Types.ObjectId, required: true, index: true })
  resourceId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, index: true })
  userId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, index: true })
  organizationId?: Types.ObjectId;

  @Prop({ type: String })
  sessionId?: string;

  @Prop({ type: Object })
  metadata?: {
    userAgent?: string;
    ip?: string;
    referer?: string;
    location?: {
      country?: string;
      region?: string;
      city?: string;
    };
    device?: {
      type?: string;
      os?: string;
      browser?: string;
    };
    [key: string]: any;
  };

  @Prop({ default: Date.now, index: true })
  timestamp: Date;
}

export const UIAnalyticsSchema = SchemaFactory.createForClass(UIAnalytics);

// 创建复合索引
UIAnalyticsSchema.index({ eventType: 1, resourceType: 1, timestamp: -1 });
UIAnalyticsSchema.index({ resourceId: 1, eventType: 1, timestamp: -1 });
UIAnalyticsSchema.index({ userId: 1, timestamp: -1 });
UIAnalyticsSchema.index({ organizationId: 1, timestamp: -1 });
UIAnalyticsSchema.index({ timestamp: -1 }); // TTL索引，可以设置数据过期时间
