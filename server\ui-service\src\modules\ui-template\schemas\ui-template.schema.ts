import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import type {  Document, Types  } from 'mongoose';

export type UITemplateDocument = UITemplate & Document;

// UI元素接口
export interface UIElement {
  id: string;
  type: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  zIndex: number;
  visible: boolean;
  locked: boolean;
  properties: Record<string, any>;
  style: Record<string, any>;
  events: Record<string, any>;
  children?: UIElement[];
  parentId?: string;
}

// 模板分类枚举
export enum TemplateCategory {
  BASIC = 'basic',
  FORM = 'form',
  NAVIGATION = 'navigation',
  DASHBOARD = 'dashboard',
  GAME_UI = 'game_ui',
  MOBILE = 'mobile',
  DESKTOP = 'desktop',
  CUSTOM = 'custom'
}

// 模板状态枚举
export enum TemplateStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  DEPRECATED = 'deprecated'
}

// 访问权限枚举
export enum AccessLevel {
  PRIVATE = 'private',
  TEAM = 'team',
  ORGANIZATION = 'organization',
  PUBLIC = 'public'
}

@Schema({ timestamps: true })
export class UITemplate {
  @Prop({ required: true, trim: true })
  name: string;

  @Prop({ trim: true })
  description: string;

  @Prop({ required: true, enum: TemplateCategory })
  category: TemplateCategory;

  @Prop([String])
  tags: string[];

  @Prop({ type: Object, required: true })
  elements: UIElement[];

  @Prop({ type: Object })
  metadata: {
    canvasSize: { width: number; height: number };
    gridSize: number;
    snapToGrid: boolean;
    backgroundColor: string;
    backgroundImage?: string;
    responsive: boolean;
    breakpoints?: Record<string, number>;
  };

  @Prop({ required: true, enum: TemplateStatus, default: TemplateStatus.DRAFT })
  status: TemplateStatus;

  @Prop({ required: true, enum: AccessLevel, default: AccessLevel.PRIVATE })
  accessLevel: AccessLevel;

  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  createdBy: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  updatedBy: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Organization' })
  organizationId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Team' })
  teamId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Project' })
  projectId: Types.ObjectId;

  @Prop({ default: '1.0.0' })
  version: string;

  @Prop({ type: Types.ObjectId, ref: 'UITemplate' })
  parentTemplateId: Types.ObjectId;

  @Prop([{ type: Types.ObjectId, ref: 'UITemplate' }])
  childTemplateIds: Types.ObjectId[];

  @Prop({ type: String })
  thumbnail: string;

  @Prop([String])
  screenshots: string[];

  @Prop({ type: Object })
  dependencies: {
    components: string[];
    themes: string[];
    assets: string[];
  };

  @Prop({ type: Object })
  settings: {
    allowFork: boolean;
    allowComments: boolean;
    allowRating: boolean;
    requireApproval: boolean;
  };

  @Prop({ type: Object })
  statistics: {
    views: number;
    downloads: number;
    forks: number;
    likes: number;
    rating: number;
    ratingCount: number;
  };

  @Prop({ type: Object })
  performance: {
    loadTime: number;
    renderTime: number;
    memoryUsage: number;
    complexity: number;
  };

  @Prop({ type: Date })
  publishedAt: Date;

  @Prop({ type: Date })
  archivedAt: Date;

  @Prop({ type: Date })
  lastAccessedAt: Date;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  @Prop({ type: Date })
  deletedAt: Date;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  deletedBy: Types.ObjectId;
}

export const UITemplateSchema = SchemaFactory.createForClass(UITemplate);

// 创建索引
UITemplateSchema.index({ name: 'text', description: 'text', tags: 'text' });
UITemplateSchema.index({ category: 1, status: 1 });
UITemplateSchema.index({ createdBy: 1, status: 1 });
UITemplateSchema.index({ organizationId: 1, status: 1 });
UITemplateSchema.index({ projectId: 1, status: 1 });
UITemplateSchema.index({ 'statistics.rating': -1 });
UITemplateSchema.index({ 'statistics.downloads': -1 });
UITemplateSchema.index({ createdAt: -1 });
UITemplateSchema.index({ updatedAt: -1 });
UITemplateSchema.index({ isDeleted: 1 });

// 虚拟字段
UITemplateSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

UITemplateSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

// 中间件
UITemplateSchema.pre('save', function(next) {
  if (this.isNew) {
    this.statistics = {
      views: 0,
      downloads: 0,
      forks: 0,
      likes: 0,
      rating: 0,
      ratingCount: 0
    };
    this.settings = {
      allowFork: true,
      allowComments: true,
      allowRating: true,
      requireApproval: false
    };
  }
  next();
});

UITemplateSchema.pre('find', function() {
  this.where({ isDeleted: { $ne: true } });
});

UITemplateSchema.pre('findOne', function() {
  this.where({ isDeleted: { $ne: true } });
});

UITemplateSchema.pre('findOneAndUpdate', function() {
  this.where({ isDeleted: { $ne: true } });
});

UITemplateSchema.pre('countDocuments', function() {
  this.where({ isDeleted: { $ne: true } });
});

// 实例方法
UITemplateSchema.methods.softDelete = function(userId: Types.ObjectId) {
  this.isDeleted = true;
  this.deletedAt = new Date();
  this.deletedBy = userId;
  return this.save();
};

UITemplateSchema.methods.restore = function() {
  this.isDeleted = false;
  this.deletedAt = undefined;
  this.deletedBy = undefined;
  return this.save();
};

UITemplateSchema.methods.incrementViews = function() {
  this.statistics.views += 1;
  this.lastAccessedAt = new Date();
  return this.save();
};

UITemplateSchema.methods.incrementDownloads = function() {
  this.statistics.downloads += 1;
  return this.save();
};

UITemplateSchema.methods.addRating = function(rating: number) {
  const currentTotal = this.statistics.rating * this.statistics.ratingCount;
  this.statistics.ratingCount += 1;
  this.statistics.rating = (currentTotal + rating) / this.statistics.ratingCount;
  return this.save();
};

UITemplateSchema.methods.canAccess = function(userId: Types.ObjectId, userOrganizationId?: Types.ObjectId, userTeamId?: Types.ObjectId) {
  // 公开模板
  if (this.accessLevel === AccessLevel.PUBLIC) {
    return true;
  }

  // 创建者
  if (this.createdBy.equals(userId)) {
    return true;
  }

  // 组织级别
  if (this.accessLevel === AccessLevel.ORGANIZATION && userOrganizationId && this.organizationId.equals(userOrganizationId)) {
    return true;
  }

  // 团队级别
  if (this.accessLevel === AccessLevel.TEAM && userTeamId && this.teamId.equals(userTeamId)) {
    return true;
  }

  return false;
};

UITemplateSchema.methods.canEdit = function(userId: Types.ObjectId, userRole?: string) {
  // 创建者
  if (this.createdBy.equals(userId)) {
    return true;
  }

  // 管理员
  if (userRole === 'admin' || userRole === 'owner') {
    return true;
  }

  return false;
};

// 静态方法
UITemplateSchema.statics.findByCategory = function(category: TemplateCategory, options: any = {}) {
  return this.find({ category, status: TemplateStatus.PUBLISHED, ...options });
};

UITemplateSchema.statics.findPopular = function(limit: number = 10) {
  return this.find({ status: TemplateStatus.PUBLISHED })
    .sort({ 'statistics.downloads': -1, 'statistics.rating': -1 })
    .limit(limit);
};

UITemplateSchema.statics.findRecent = function(limit: number = 10) {
  return this.find({ status: TemplateStatus.PUBLISHED })
    .sort({ publishedAt: -1 })
    .limit(limit);
};

UITemplateSchema.statics.search = function(query: string, options: any = {}) {
  return this.find({
    $text: { $search: query },
    status: TemplateStatus.PUBLISHED,
    ...options
  }).sort({ score: { $meta: 'textScore' } });
};
