/**
 * DTO 类型定义
 */
import type {  ModelType, ModelStatus  } from '../entities/ai-model.entity';

export interface CreateModelDto {
  name: string;
  description?: string;
  type: ModelType;
  version: string;
  currentVersion: string;
  filePath: string;
  config?: Record<string, any>;
  hardwareRequirements?: Record<string, any>;
  tags?: string[];
}

export interface UpdateModelDto {
  name?: string;
  description?: string;
  filePath?: string;
  config?: Record<string, any>;
  hardwareRequirements?: Record<string, any>;
  tags?: string[];
  isActive?: boolean;
}

export interface InferenceRequestDto {
  inputData: any;
  input?: any; // 兼容性字段
  parameters?: Record<string, any>;
  batchSize?: number;
}

export interface ModelQueryDto {
  page?: number;
  limit?: number;
  pageSize?: number;
  type?: ModelType;
  status?: ModelStatus;
  purpose?: string;
  isActive?: boolean;
  search?: string;
  tags?: string[];
}
