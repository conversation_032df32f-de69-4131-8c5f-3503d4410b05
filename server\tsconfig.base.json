{
  "compilerOptions": {
    // 基础配置
    "target": "ES2020",
    "lib": ["ES2020"],
    "module": "commonjs",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true,
    "resolveJsonModule": true,

    // 输出配置
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "outDir": "./dist",
    "removeComments": false,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,

    // 严格性检查
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "exactOptionalPropertyTypes": true,

    // 额外检查
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitThis": true,
    "alwaysStrict": true,

    // 模块解析
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"],
      "@shared/*": ["../shared/*"],
      "@engine/*": ["../../editor/src/libs/*"],
      "@types/*": ["src/types/*"],
      "@common/*": ["src/common/*"],
      "@config/*": ["src/config/*"],
      "@utils/*": ["src/utils/*"],
      "@interfaces/*": ["src/interfaces/*"],
      "@entities/*": ["src/entities/*"],
      "@dto/*": ["src/dto/*"],
      "@services/*": ["src/services/*"],
      "@controllers/*": ["src/controllers/*"],
      "@modules/*": ["src/modules/*"],
      "@middleware/*": ["src/middleware/*"],
      "@guards/*": ["src/guards/*"],
      "@decorators/*": ["src/decorators/*"],
      "@filters/*": ["src/filters/*"],
      "@interceptors/*": ["src/interceptors/*"],
      "@pipes/*": ["src/pipes/*"]
    },

    // 性能优化
    "incremental": true,
    "tsBuildInfoFile": "./dist/.tsbuildinfo",

    // 类型定义
    "types": ["node", "jest"],
    "typeRoots": ["node_modules/@types", "src/types"]
  },
  "include": [
    "src/**/*",
    "src/types/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "test",
    "tests",
    "**/*spec.ts",
    "**/*test.ts",
    "coverage",
    "*.js"
  ],
  "ts-node": {
    "esm": true,
    "experimentalSpecifierResolution": "node"
  }
}
