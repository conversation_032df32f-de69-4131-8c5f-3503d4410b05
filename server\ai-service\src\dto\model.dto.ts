/**
 * 模型相关的数据传输对象
 */

import { IsString, IsOptional, IsObject, IsEnum, IsNumber, IsBoolean, ValidateNested } from 'class-validator';
import type {  Type  } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 训练配置DTO
 */
export class TrainingConfigDto {
  @ApiProperty({ description: '训练轮数', example: 100 })
  @IsNumber()
  epochs: number;

  @ApiProperty({ description: '批次大小', example: 32 })
  @IsNumber()
  batchSize: number;

  @ApiProperty({ description: '学习率', example: 0.001 })
  @IsNumber()
  learningRate: number;

  @ApiProperty({ description: '优化器', example: 'adam' })
  @IsString()
  optimizer: string;

  @ApiProperty({ description: '损失函数', example: 'categorical_crossentropy' })
  @IsString()
  lossFunction: string;

  @ApiProperty({ description: '评估指标', example: ['accuracy', 'precision'] })
  @IsString({ each: true })
  metrics: string[];

  @ApiProperty({ description: '验证集比例', example: 0.2 })
  @IsNumber()
  validationSplit: number;

  @ApiPropertyOptional({ description: '早停配置' })
  @IsOptional()
  @IsObject()
  earlyStopping?: {
    enabled: boolean;
    patience: number;
    minDelta: number;
  };

  @ApiPropertyOptional({ description: '检查点配置' })
  @IsOptional()
  @IsObject()
  checkpoints?: {
    enabled: boolean;
    frequency: number;
    keepBest: boolean;
  };
}

/**
 * 创建模型DTO
 */
export class CreateModelDto {
  @ApiProperty({ description: '模型名称', example: 'ResNet50分类器' })
  @IsString()
  name: string;

  @ApiProperty({ 
    description: '模型类型', 
    enum: ['reinforcement_learning', 'neural_network', 'hybrid'],
    example: 'neural_network'
  })
  @IsEnum(['reinforcement_learning', 'neural_network', 'hybrid'])
  type: 'reinforcement_learning' | 'neural_network' | 'hybrid';

  @ApiPropertyOptional({ description: '模型版本', example: '1.0.0' })
  @IsOptional()
  @IsString()
  version?: string;

  @ApiPropertyOptional({ description: '模型描述', example: '用于图像分类的ResNet50模型' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '模型架构配置' })
  @IsObject()
  architecture: any;

  @ApiPropertyOptional({ description: '超参数配置' })
  @IsOptional()
  @IsObject()
  hyperparameters?: any;

  @ApiProperty({ description: '训练配置' })
  @ValidateNested()
  @Type(() => TrainingConfigDto)
  trainingConfig: TrainingConfigDto;
}

/**
 * 更新模型DTO
 */
export class UpdateModelDto {
  @ApiPropertyOptional({ description: '模型名称' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '模型描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '模型架构配置' })
  @IsOptional()
  @IsObject()
  architecture?: any;

  @ApiPropertyOptional({ description: '超参数配置' })
  @IsOptional()
  @IsObject()
  hyperparameters?: any;

  @ApiPropertyOptional({ description: '训练配置' })
  @IsOptional()
  @ValidateNested()
  @Type(() => TrainingConfigDto)
  trainingConfig?: TrainingConfigDto;
}

/**
 * 开始训练DTO
 */
export class StartTrainingDto {
  @ApiPropertyOptional({ description: '自定义训练配置' })
  @IsOptional()
  @ValidateNested()
  @Type(() => TrainingConfigDto)
  trainingConfig?: Partial<TrainingConfigDto>;
}

/**
 * 推理请求DTO
 */
export class InferenceRequestDto {
  @ApiProperty({ description: '模型ID', example: 'model_123' })
  @IsString()
  modelId: string;

  @ApiProperty({ description: '模型版本', example: '1.0.0' })
  @IsString()
  modelVersion: string;

  @ApiProperty({ description: '输入数据' })
  @IsObject()
  inputData: any;

  @ApiPropertyOptional({ description: '推理选项' })
  @IsOptional()
  @IsObject()
  options?: {
    timeout?: number;
    priority?: number;
    requireGPU?: boolean;
    preferredRegion?: string;
  };

  @ApiPropertyOptional({ description: '用户ID' })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({ description: '会话ID' })
  @IsOptional()
  @IsString()
  sessionId?: string;
}

/**
 * 注册推理节点DTO
 */
export class RegisterInferenceNodeDto {
  @ApiPropertyOptional({ description: '节点ID' })
  @IsOptional()
  @IsString()
  nodeId?: string;

  @ApiProperty({ description: '节点端点', example: 'http://localhost:8080' })
  @IsString()
  endpoint: string;

  @ApiProperty({ description: '节点能力配置' })
  @IsObject()
  capabilities: {
    maxConcurrentRequests: number;
    supportedModels: string[];
    cpuCores: number;
    memoryGB: number;
    gpuMemory?: number;
  };

  @ApiPropertyOptional({ description: '节点区域', example: 'us-west-1' })
  @IsOptional()
  @IsString()
  region?: string;

  @ApiPropertyOptional({ description: '节点优先级', example: 1 })
  @IsOptional()
  @IsNumber()
  priority?: number;
}

/**
 * 模型缓存配置DTO
 */
export class ModelCacheConfigDto {
  @ApiProperty({ description: '模型ID' })
  @IsString()
  modelId: string;

  @ApiProperty({ description: '模型版本' })
  @IsString()
  version: string;

  @ApiProperty({ 
    description: '缓存策略',
    enum: ['memory', 'disk', 'hybrid'],
    example: 'hybrid'
  })
  @IsEnum(['memory', 'disk', 'hybrid'])
  cacheStrategy: 'memory' | 'disk' | 'hybrid';

  @ApiProperty({ description: '最大缓存大小(MB)', example: 1024 })
  @IsNumber()
  maxCacheSize: number;

  @ApiProperty({ description: '缓存TTL(秒)', example: 3600 })
  @IsNumber()
  ttl: number;

  @ApiProperty({ description: '启动时预加载', example: true })
  @IsBoolean()
  preloadOnStartup: boolean;

  @ApiProperty({ description: '启用压缩', example: true })
  @IsBoolean()
  compressionEnabled: boolean;

  @ApiProperty({ description: '复制因子', example: 2 })
  @IsNumber()
  replicationFactor: number;
}

/**
 * 负载均衡策略DTO
 */
export class LoadBalancingStrategyDto {
  @ApiProperty({ 
    description: '负载均衡类型',
    enum: ['round_robin', 'least_connections', 'weighted', 'latency_based', 'ai_optimized'],
    example: 'ai_optimized'
  })
  @IsEnum(['round_robin', 'least_connections', 'weighted', 'latency_based', 'ai_optimized'])
  type: 'round_robin' | 'least_connections' | 'weighted' | 'latency_based' | 'ai_optimized';

  @ApiPropertyOptional({ description: '节点权重配置' })
  @IsOptional()
  @IsObject()
  weights?: Record<string, number>;

  @ApiProperty({ description: '健康检查间隔(毫秒)', example: 30000 })
  @IsNumber()
  healthCheckInterval: number;

  @ApiProperty({ description: '启用故障转移', example: true })
  @IsBoolean()
  failoverEnabled: boolean;

  @ApiProperty({ description: '最大重试次数', example: 3 })
  @IsNumber()
  maxRetries: number;
}
