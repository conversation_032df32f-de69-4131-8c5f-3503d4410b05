/**
 * 空间要素DTO
 */
import { IsString, IsOptional, IsObject, IsEnum, IsBoolean, IsNumber, IsArray, ValidateNested } from 'class-validator';
import type {  Type  } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateSpatialFeatureDto {
  @ApiProperty({ description: '要素名称' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: '要素描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    description: '要素类型',
    enum: ['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon']
  })
  @IsEnum(['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon'])
  featureType: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';

  @ApiProperty({ description: '几何数据（GeoJSON格式）' })
  @IsObject()
  geometry: any;

  @ApiPropertyOptional({ description: '要素属性' })
  @IsOptional()
  @IsObject()
  properties?: Record<string, any>;

  @ApiPropertyOptional({ description: '样式配置' })
  @IsOptional()
  @IsObject()
  style?: {
    color?: string;
    fillColor?: string;
    weight?: number;
    opacity?: number;
    fillOpacity?: number;
    radius?: number;
    strokeWidth?: number;
    strokeColor?: string;
    strokeOpacity?: number;
    fillPattern?: string;
    iconUrl?: string;
    iconSize?: [number, number];
    iconAnchor?: [number, number];
  };

  @ApiProperty({ description: '所属图层ID' })
  @IsString()
  layerId: string;

  @ApiPropertyOptional({ description: '是否可见', default: true })
  @IsOptional()
  @IsBoolean()
  visible?: boolean;

  @ApiPropertyOptional({ description: '是否可编辑', default: true })
  @IsOptional()
  @IsBoolean()
  editable?: boolean;

  @ApiPropertyOptional({ description: '是否可选择', default: true })
  @IsOptional()
  @IsBoolean()
  selectable?: boolean;

  @ApiPropertyOptional({ description: 'Z轴顺序', default: 0 })
  @IsOptional()
  @IsNumber()
  zIndex?: number;

  @ApiPropertyOptional({ description: '元数据' })
  @IsOptional()
  @IsObject()
  metadata?: {
    source?: string;
    accuracy?: number;
    confidence?: number;
    tags?: string[];
    category?: string;
    subcategory?: string;
    version?: number;
    [key: string]: any;
  };

  @ApiPropertyOptional({ description: '创建者ID' })
  @IsOptional()
  @IsString()
  createdBy?: string;
}

export class UpdateSpatialFeatureDto {
  @ApiPropertyOptional({ description: '要素名称' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '要素描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ 
    description: '要素类型',
    enum: ['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon']
  })
  @IsOptional()
  @IsEnum(['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon'])
  featureType?: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';

  @ApiPropertyOptional({ description: '几何数据（GeoJSON格式）' })
  @IsOptional()
  @IsObject()
  geometry?: any;

  @ApiPropertyOptional({ description: '要素属性' })
  @IsOptional()
  @IsObject()
  properties?: Record<string, any>;

  @ApiPropertyOptional({ description: '样式配置' })
  @IsOptional()
  @IsObject()
  style?: {
    color?: string;
    fillColor?: string;
    weight?: number;
    opacity?: number;
    fillOpacity?: number;
    radius?: number;
    strokeWidth?: number;
    strokeColor?: string;
    strokeOpacity?: number;
    fillPattern?: string;
    iconUrl?: string;
    iconSize?: [number, number];
    iconAnchor?: [number, number];
  };

  @ApiPropertyOptional({ description: '是否可见' })
  @IsOptional()
  @IsBoolean()
  visible?: boolean;

  @ApiPropertyOptional({ description: '是否可编辑' })
  @IsOptional()
  @IsBoolean()
  editable?: boolean;

  @ApiPropertyOptional({ description: '是否可选择' })
  @IsOptional()
  @IsBoolean()
  selectable?: boolean;

  @ApiPropertyOptional({ description: 'Z轴顺序' })
  @IsOptional()
  @IsNumber()
  zIndex?: number;

  @ApiPropertyOptional({ description: '元数据' })
  @IsOptional()
  @IsObject()
  metadata?: {
    source?: string;
    accuracy?: number;
    confidence?: number;
    tags?: string[];
    category?: string;
    subcategory?: string;
    version?: number;
    [key: string]: any;
  };

  @ApiPropertyOptional({ description: '更新者ID' })
  @IsOptional()
  @IsString()
  updatedBy?: string;
}

export class SpatialFeatureQueryDto {
  @ApiPropertyOptional({ description: '图层ID' })
  @IsOptional()
  @IsString()
  layerId?: string;

  @ApiPropertyOptional({ description: '边界框 [minX, minY, maxX, maxY]' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  bbox?: [number, number, number, number];

  @ApiPropertyOptional({ description: '要素类型过滤' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  featureTypes?: string[];

  @ApiPropertyOptional({ description: '属性过滤条件' })
  @IsOptional()
  @IsObject()
  propertyFilter?: Record<string, any>;

  @ApiPropertyOptional({ description: '分页大小', default: 100 })
  @IsOptional()
  @IsNumber()
  limit?: number;

  @ApiPropertyOptional({ description: '分页偏移', default: 0 })
  @IsOptional()
  @IsNumber()
  offset?: number;

  @ApiPropertyOptional({ description: '排序字段' })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiPropertyOptional({ description: '排序方向', enum: ['ASC', 'DESC'], default: 'ASC' })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC';

  @ApiPropertyOptional({ description: '是否包含几何数据', default: true })
  @IsOptional()
  @IsBoolean()
  includeGeometry?: boolean;

  @ApiPropertyOptional({ description: '几何简化容差' })
  @IsOptional()
  @IsNumber()
  simplifyTolerance?: number;
}

export class BatchCreateFeaturesDto {
  @ApiProperty({ description: '要素列表' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateSpatialFeatureDto)
  features: CreateSpatialFeatureDto[];

  @ApiPropertyOptional({ description: '是否忽略错误', default: false })
  @IsOptional()
  @IsBoolean()
  ignoreErrors?: boolean;

  @ApiPropertyOptional({ description: '批次大小', default: 100 })
  @IsOptional()
  @IsNumber()
  batchSize?: number;
}

export class BatchUpdateFeaturesDto {
  @ApiProperty({ description: '要素ID列表' })
  @IsArray()
  @IsString({ each: true })
  featureIds: string[];

  @ApiProperty({ description: '更新数据' })
  @ValidateNested()
  @Type(() => UpdateSpatialFeatureDto)
  updateData: UpdateSpatialFeatureDto;

  @ApiPropertyOptional({ description: '是否忽略错误', default: false })
  @IsOptional()
  @IsBoolean()
  ignoreErrors?: boolean;
}

export class BatchDeleteFeaturesDto {
  @ApiProperty({ description: '要素ID列表' })
  @IsArray()
  @IsString({ each: true })
  featureIds: string[];

  @ApiPropertyOptional({ description: '是否软删除', default: false })
  @IsOptional()
  @IsBoolean()
  softDelete?: boolean;
}

export class FeatureStatisticsDto {
  @ApiPropertyOptional({ description: '图层ID' })
  @IsOptional()
  @IsString()
  layerId?: string;

  @ApiPropertyOptional({ description: '要素类型' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  featureTypes?: string[];

  @ApiPropertyOptional({ description: '统计字段' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fields?: string[];

  @ApiPropertyOptional({ description: '分组字段' })
  @IsOptional()
  @IsString()
  groupBy?: string;

  @ApiPropertyOptional({ description: '边界框过滤' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  bbox?: [number, number, number, number];
}

export class FeatureValidationDto {
  @ApiProperty({ description: '要素ID' })
  @IsString()
  featureId: string;

  @ApiPropertyOptional({ description: '验证规则' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  rules?: string[];

  @ApiPropertyOptional({ description: '是否修复错误', default: false })
  @IsOptional()
  @IsBoolean()
  autoFix?: boolean;
}

export class FeatureResponseDto {
  @ApiProperty({ description: '要素ID' })
  id: string;

  @ApiProperty({ description: '要素名称' })
  name: string;

  @ApiPropertyOptional({ description: '要素描述' })
  description?: string;

  @ApiProperty({ description: '要素类型' })
  featureType: string;

  @ApiProperty({ description: '几何数据' })
  geometry: any;

  @ApiPropertyOptional({ description: '要素属性' })
  properties?: Record<string, any>;

  @ApiPropertyOptional({ description: '样式配置' })
  style?: any;

  @ApiProperty({ description: '所属图层ID' })
  layerId: string;

  @ApiProperty({ description: '是否可见' })
  visible: boolean;

  @ApiProperty({ description: '是否可编辑' })
  editable: boolean;

  @ApiProperty({ description: '是否可选择' })
  selectable: boolean;

  @ApiProperty({ description: 'Z轴顺序' })
  zIndex: number;

  @ApiPropertyOptional({ description: '元数据' })
  metadata?: any;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: '创建者' })
  createdBy?: string;

  @ApiPropertyOptional({ description: '更新者' })
  updatedBy?: string;
}
