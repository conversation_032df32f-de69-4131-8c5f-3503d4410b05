import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { TerminusModule } from '@nestjs/terminus';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';
import { EnterpriseSystemEntity } from '../integration/entities/enterprise-system.entity';
import { IntegrationFlowEntity } from '../integration/entities/integration-flow.entity';
import { SyncTaskEntity } from '../integration/entities/sync-task.entity';

/**
 * 健康检查模块
 * 提供服务健康状态监控功能
 */
@Module({
  imports: [
    TerminusModule,
    TypeOrmModule.forFeature([
      EnterpriseSystemEntity,
      IntegrationFlowEntity,
      SyncTaskEntity,
    ]),
  ],
  controllers: [HealthController],
  providers: [HealthService],
  exports: [HealthService],
})
export class HealthModule {}
