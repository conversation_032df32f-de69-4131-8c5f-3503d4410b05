/**
 * 数据服务节点测试
 * 批次2.1 - 服务器集成节点测试
 */
import {
  DatabaseConnectionNode,
  DatabaseQueryNode,
  DatabaseInsertNode,
  DatabaseUpdateNode,
  DatabaseDeleteNode,
  DatabaseTransactionNode
} from '../../src/visual-script/nodes/data/DataServiceNodes';

import {
  DataValidationNode,
  DataTransformationNode,
  DataAggregationNode
} from '../../src/visual-script/nodes/data/DataServiceNodes2';

import {
  DataBackupNode,
  DataSyncNode,
  DataAnalyticsNode
} from '../../src/visual-script/nodes/data/DataServiceNodes3';

describe('数据服务节点测试', () => {
  describe('DatabaseConnectionNode', () => {
    let node: DatabaseConnectionNode;

    beforeEach(() => {
      node = new DatabaseConnectionNode();
    });

    it('应该正确创建数据库连接节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('DatabaseConnection');
      expect(node.name).toBe('数据库连接');
    });

    it('应该有正确的输入端口', () => {
      const inputs = node.getInputs();
      expect(inputs).toHaveLength(4);
      expect(inputs.map(input => input.name)).toEqual([
        'config', 'autoConnect', 'retryCount', 'retryDelay'
      ]);
    });

    it('应该有正确的输出端口', () => {
      const outputs = node.getOutputs();
      expect(outputs).toHaveLength(7);
      expect(outputs.map(output => output.name)).toEqual([
        'connected', 'connection', 'connectionInfo', 'errorMessage',
        'onConnected', 'onDisconnected', 'onError'
      ]);
    });

    it('应该能够执行数据库连接', () => {
      const config = {
        type: 'mysql' as const,
        host: 'localhost',
        port: 3306,
        database: 'test',
        username: 'user',
        password: 'pass'
      };

      const result = node.execute({ config });
      
      expect(result.connected).toBe(true);
      expect(result.connection).toBeDefined();
      expect(result.connectionInfo).toBeDefined();
      expect(result.errorMessage).toBeNull();
    });

    it('应该处理无效配置', () => {
      const result = node.execute({});
      
      expect(result.connected).toBe(false);
      expect(result.errorMessage).toBe('数据库配置不能为空');
    });
  });

  describe('DatabaseQueryNode', () => {
    let node: DatabaseQueryNode;

    beforeEach(() => {
      node = new DatabaseQueryNode();
    });

    it('应该正确创建数据库查询节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('DatabaseQuery');
      expect(node.name).toBe('数据库查询');
    });

    it('应该能够执行查询', () => {
      const connection = { connected: true };
      const sql = 'SELECT * FROM users';
      
      const result = node.execute({ connection, sql });
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.rowCount).toBeGreaterThanOrEqual(0);
    });

    it('应该处理无连接情况', () => {
      const sql = 'SELECT * FROM users';
      
      const result = node.execute({ sql });
      
      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('数据库连接不能为空');
    });
  });

  describe('DataValidationNode', () => {
    let node: DataValidationNode;

    beforeEach(() => {
      node = new DataValidationNode();
    });

    it('应该正确创建数据验证节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('DataValidation');
      expect(node.name).toBe('数据验证');
    });

    it('应该能够验证数据', () => {
      const data = { name: 'John', age: 30 };
      const rules = [
        { field: 'name', type: 'required' as const, message: '姓名必填' },
        { field: 'age', type: 'type' as const, value: 'number', message: '年龄必须是数字' }
      ];
      
      const result = node.execute({ data, rules });
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.validatedData).toEqual(data);
    });

    it('应该检测验证错误', () => {
      const data = { age: 'invalid' };
      const rules = [
        { field: 'name', type: 'required' as const, message: '姓名必填' }
      ];
      
      const result = node.execute({ data, rules });
      
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('DataTransformationNode', () => {
    let node: DataTransformationNode;

    beforeEach(() => {
      node = new DataTransformationNode();
    });

    it('应该正确创建数据转换节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('DataTransformation');
      expect(node.name).toBe('数据转换');
    });

    it('应该能够转换数据', () => {
      const data = [
        { firstName: 'John', lastName: 'Doe', age: 30 },
        { firstName: 'Jane', lastName: 'Smith', age: 25 }
      ];
      const mapping = {
        fullName: 'firstName',
        userAge: 'age'
      };
      
      const result = node.execute({ data, transformType: 'map', mapping });
      
      expect(result.success).toBe(true);
      expect(result.transformedData).toBeDefined();
      expect(result.transformCount).toBe(2);
    });
  });

  describe('DataAggregationNode', () => {
    let node: DataAggregationNode;

    beforeEach(() => {
      node = new DataAggregationNode();
    });

    it('应该正确创建数据聚合节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('DataAggregation');
      expect(node.name).toBe('数据聚合');
    });

    it('应该能够聚合数据', () => {
      const data = [
        { category: 'A', value: 10 },
        { category: 'A', value: 20 },
        { category: 'B', value: 15 }
      ];
      const groupBy = ['category'];
      const aggregations = [
        { field: 'value', function: 'sum', alias: 'total' }
      ];
      
      const result = node.execute({ data, groupBy, aggregations });
      
      expect(result.success).toBe(true);
      expect(result.aggregatedData).toBeDefined();
      expect(result.groupCount).toBeGreaterThan(0);
    });
  });

  describe('DataBackupNode', () => {
    let node: DataBackupNode;

    beforeEach(() => {
      node = new DataBackupNode();
    });

    it('应该正确创建数据备份节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('DataBackup');
      expect(node.name).toBe('数据备份');
    });

    it('应该能够执行备份', () => {
      const connection = { connected: true };
      const destination = '/backup';
      
      const result = node.execute({ connection, destination });
      
      expect(result.success).toBe(true);
      expect(result.backupId).toBeDefined();
      expect(result.backupPath).toBeDefined();
      expect(result.backupSize).toBeGreaterThan(0);
    });
  });

  describe('DataSyncNode', () => {
    let node: DataSyncNode;

    beforeEach(() => {
      node = new DataSyncNode();
    });

    it('应该正确创建数据同步节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('DataSync');
      expect(node.name).toBe('数据同步');
    });

    it('应该能够执行同步', () => {
      const sourceConnection = { connected: true };
      const targetConnection = { connected: true };
      
      const result = node.execute({ sourceConnection, targetConnection });
      
      expect(result.success).toBe(true);
      expect(result.syncId).toBeDefined();
      expect(result.syncedRecords).toBeGreaterThanOrEqual(0);
    });
  });

  describe('DataAnalyticsNode', () => {
    let node: DataAnalyticsNode;

    beforeEach(() => {
      node = new DataAnalyticsNode();
    });

    it('应该正确创建数据分析节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('DataAnalytics');
      expect(node.name).toBe('数据分析');
    });

    it('应该能够执行分析', () => {
      const data = [
        { value: 10, category: 'A' },
        { value: 20, category: 'B' },
        { value: 15, category: 'A' }
      ];
      const metrics = ['value'];
      
      const result = node.execute({ data, metrics });
      
      expect(result.success).toBe(true);
      expect(result.insights).toBeDefined();
      expect(result.statistics).toBeDefined();
      expect(result.recommendations).toBeDefined();
    });
  });
});
