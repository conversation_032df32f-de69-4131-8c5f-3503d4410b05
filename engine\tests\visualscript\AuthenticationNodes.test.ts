/**
 * 认证授权节点测试
 * 批次2.1 - 服务器集成节点测试
 */
import {
  JWTTokenNode,
  OAuth2Node,
  RBACNode
} from '../../src/visual-script/nodes/auth/AuthenticationNodes';

import {
  PermissionCheckNode,
  SecurityAuditNode,
  EncryptionNode,
  DecryptionNode,
  SecurityMonitoringNode
} from '../../src/visual-script/nodes/auth/AuthenticationNodes2';

describe('认证授权节点测试', () => {
  describe('JWTTokenNode', () => {
    let node: JWTTokenNode;

    beforeEach(() => {
      node = new JWTTokenNode();
    });

    it('应该正确创建JWT令牌节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('JWTToken');
      expect(node.name).toBe('JWT令牌');
    });

    it('应该有正确的输入端口', () => {
      const inputs = node.getInputs();
      expect(inputs).toHaveLength(6);
      expect(inputs.map(input => input.name)).toEqual([
        'action', 'payload', 'secret', 'token', 'algorithm', 'expiresIn'
      ]);
    });

    it('应该能够生成JWT令牌', () => {
      const payload = { userId: '123', username: 'test' };
      const secret = 'test-secret';
      
      const result = node.execute({ action: 'generate', payload, secret });
      
      expect(result.success).toBe(true);
      expect(result.token).toBeDefined();
      expect(result.payload).toEqual(expect.objectContaining(payload));
      expect(result.valid).toBe(true);
    });

    it('应该能够验证JWT令牌', () => {
      const secret = 'test-secret';
      // 首先生成一个令牌
      const generateResult = node.execute({ 
        action: 'generate', 
        payload: { userId: '123' }, 
        secret 
      });
      
      // 然后验证这个令牌
      const verifyResult = node.execute({ 
        action: 'verify', 
        token: generateResult.token, 
        secret 
      });
      
      expect(verifyResult.success).toBe(true);
      expect(verifyResult.valid).toBe(true);
    });

    it('应该处理无效密钥', () => {
      const result = node.execute({ action: 'generate', payload: {} });
      
      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('密钥不能为空');
    });
  });

  describe('OAuth2Node', () => {
    let node: OAuth2Node;

    beforeEach(() => {
      node = new OAuth2Node();
    });

    it('应该正确创建OAuth2节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('OAuth2');
      expect(node.name).toBe('OAuth2认证');
    });

    it('应该能够生成授权URL', () => {
      const config = {
        clientId: 'test-client',
        clientSecret: 'test-secret',
        redirectUri: 'http://localhost/callback',
        scope: ['read', 'write'],
        authorizationUrl: 'https://auth.example.com/oauth/authorize',
        tokenUrl: 'https://auth.example.com/oauth/token'
      };
      
      const result = node.execute({ action: 'getAuthUrl', config });
      
      expect(result.success).toBe(true);
      expect(result.authUrl).toBeDefined();
      expect(result.authUrl).toContain(config.authorizationUrl);
    });

    it('应该能够交换授权码', () => {
      const config = {
        clientId: 'test-client',
        clientSecret: 'test-secret',
        redirectUri: 'http://localhost/callback',
        scope: ['read'],
        authorizationUrl: 'https://auth.example.com/oauth/authorize',
        tokenUrl: 'https://auth.example.com/oauth/token'
      };
      const authCode = 'test-auth-code';
      
      const result = node.execute({ action: 'exchangeToken', config, authCode });
      
      expect(result.success).toBe(true);
      expect(result.accessToken).toBeDefined();
      expect(result.refreshToken).toBeDefined();
      expect(result.expiresIn).toBeDefined();
    });
  });

  describe('RBACNode', () => {
    let node: RBACNode;

    beforeEach(() => {
      node = new RBACNode();
    });

    it('应该正确创建RBAC节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('RBAC');
      expect(node.name).toBe('基于角色的访问控制');
    });

    it('应该能够检查访问权限', () => {
      const userId = 'user123';
      const resource = 'profile';
      const operation = 'read';
      
      const result = node.execute({ action: 'checkAccess', userId, resource, operation });
      
      expect(result.success).toBe(true);
      expect(typeof result.allowed).toBe('boolean');
      expect(result.roles).toBeDefined();
      expect(result.permissions).toBeDefined();
    });

    it('应该能够获取用户角色', () => {
      const userId = 'user123';
      
      const result = node.execute({ action: 'getUserRoles', userId });
      
      expect(result.success).toBe(true);
      expect(Array.isArray(result.roles)).toBe(true);
    });
  });

  describe('PermissionCheckNode', () => {
    let node: PermissionCheckNode;

    beforeEach(() => {
      node = new PermissionCheckNode();
    });

    it('应该正确创建权限检查节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('PermissionCheck');
      expect(node.name).toBe('权限检查');
    });

    it('应该能够检查权限', () => {
      const userId = 'user123';
      const resource = 'data';
      const action = 'read';
      
      const result = node.execute({ userId, resource, action });
      
      expect(result).toBeDefined();
      expect(typeof result.allowed).toBe('boolean');
      expect(Array.isArray(result.permissions)).toBe(true);
    });
  });

  describe('SecurityAuditNode', () => {
    let node: SecurityAuditNode;

    beforeEach(() => {
      node = new SecurityAuditNode();
    });

    it('应该正确创建安全审计节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('SecurityAudit');
      expect(node.name).toBe('安全审计');
    });

    it('应该能够记录安全事件', () => {
      const userId = 'user123';
      const event = 'login';
      const details = { ip: '***********' };
      
      const result = node.execute({ action: 'log', userId, event, details });
      
      expect(result.success).toBe(true);
      expect(result.auditId).toBeDefined();
      expect(Array.isArray(result.logs)).toBe(true);
    });

    it('应该能够分析安全事件', () => {
      const userId = 'user123';
      
      const result = node.execute({ action: 'analyze', userId });
      
      expect(result.success).toBe(true);
      expect(Array.isArray(result.alerts)).toBe(true);
      expect(typeof result.riskScore).toBe('number');
    });
  });

  describe('EncryptionNode', () => {
    let node: EncryptionNode;

    beforeEach(() => {
      node = new EncryptionNode();
    });

    it('应该正确创建加密节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('Encryption');
      expect(node.name).toBe('加密');
    });

    it('应该能够加密数据', () => {
      const data = 'sensitive data';
      const key = 'encryption-key';
      
      const result = node.execute({ data, key });
      
      expect(result.success).toBe(true);
      expect(result.encryptedData).toBeDefined();
      expect(result.encryptedData).not.toBe(data);
      expect(result.keyInfo).toBeDefined();
    });

    it('应该处理空数据', () => {
      const key = 'encryption-key';
      
      const result = node.execute({ key });
      
      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('待加密数据不能为空');
    });
  });

  describe('DecryptionNode', () => {
    let node: DecryptionNode;

    beforeEach(() => {
      node = new DecryptionNode();
    });

    it('应该正确创建解密节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('Decryption');
      expect(node.name).toBe('解密');
    });

    it('应该能够解密数据', () => {
      // 首先用加密节点加密数据
      const encryptionNode = new EncryptionNode();
      const originalData = 'test data';
      const key = 'test-key';
      
      const encryptResult = encryptionNode.execute({ data: originalData, key });
      
      // 然后解密
      const decryptResult = node.execute({ 
        encryptedData: encryptResult.encryptedData, 
        key 
      });
      
      expect(decryptResult.success).toBe(true);
      expect(decryptResult.decryptedData).toBeDefined();
    });
  });

  describe('SecurityMonitoringNode', () => {
    let node: SecurityMonitoringNode;

    beforeEach(() => {
      node = new SecurityMonitoringNode();
    });

    it('应该正确创建安全监控节点', () => {
      expect(node).toBeDefined();
      expect(node.type).toBe('SecurityMonitoring');
      expect(node.name).toBe('安全监控');
    });

    it('应该能够监控安全状态', () => {
      const metrics = ['login_attempts', 'failed_requests'];
      
      const result = node.execute({ action: 'monitor', metrics });
      
      expect(result.success).toBe(true);
      expect(result.status).toBeDefined();
      expect(Array.isArray(result.alerts)).toBe(true);
      expect(result.metrics).toBeDefined();
    });

    it('应该能够分析威胁', () => {
      const metrics = ['suspicious_ips'];
      
      const result = node.execute({ action: 'analyze', metrics });
      
      expect(result.success).toBe(true);
      expect(Array.isArray(result.threats)).toBe(true);
      expect(Array.isArray(result.recommendations)).toBe(true);
    });
  });
});
