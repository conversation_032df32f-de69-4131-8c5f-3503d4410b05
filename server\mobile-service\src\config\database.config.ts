/**
 * 数据库配置
 * 
 * 提供TypeORM数据库连接配置
 */

import { ConfigService } from '@nestjs/config';
import type {  TypeOrmModuleOptions  } from '@nestjs/typeorm';
import { DataSource, DataSourceOptions } from 'typeorm';

// 实体导入
import { MobileDevice } from '../device/entities/mobile-device.entity';
import { DeviceSession } from '../device/entities/device-session.entity';
import { SyncRecord } from '../mobile-sync/entities/sync-record.entity';
import { ConflictRecord } from '../mobile-sync/entities/conflict-record.entity';

/**
 * 获取数据库配置
 */
export const getDatabaseConfig = (configService: ConfigService): TypeOrmModuleOptions => {
  return {
    type: 'mysql',
    host: configService.get<string>('DB_HOST', 'localhost'),
    port: configService.get<number>('DB_PORT', 3306),
    username: configService.get<string>('DB_USERNAME', 'root'),
    password: configService.get<string>('DB_PASSWORD', ''),
    database: configService.get<string>('DB_DATABASE', 'mobile_service'),
    entities: [
      MobileDevice,
      DeviceSession,
      SyncRecord,
      ConflictRecord,
    ],
    migrations: ['dist/database/migrations/*.js'],
    synchronize: configService.get<boolean>('DB_SYNCHRONIZE', false),
    logging: configService.get<boolean>('DB_LOGGING', false),
    timezone: '+08:00',
    charset: 'utf8mb4',
    extra: {
      connectionLimit: 10,
    },
  };
};

/**
 * 数据源配置（用于CLI）
 */
const dataSourceOptions: DataSourceOptions = {
  type: 'mysql',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  username: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'mobile_service',
  entities: [
    'src/**/*.entity.ts',
  ],
  migrations: ['src/database/migrations/*.ts'],
  synchronize: false,
  logging: false,
  timezone: '+08:00',
  charset: 'utf8mb4',
};

export const AppDataSource = new DataSource(dataSourceOptions);
