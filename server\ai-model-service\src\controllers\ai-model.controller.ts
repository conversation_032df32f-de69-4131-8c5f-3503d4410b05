/**
 * AI模型控制器
 */
import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseInterceptors,
  UploadedFile,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiConsumes,
} from '@nestjs/swagger';
import { AIModelService } from '../services/ai-model.service';
import type {  AIModel, ModelType, ModelStatus  } from '../entities/ai-model.entity';
import { CreateModelDto, UpdateModelDto, InferenceRequestDto, ModelQueryDto } from '../dto';

@ApiTags('AI模型管理')
@Controller('models')
export class AIModelController {
  private readonly logger = new Logger(AIModelController.name);

  constructor(private readonly aiModelService: AIModelService) {}

  @Get()
  @ApiOperation({ summary: '获取模型列表' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'type', required: false, enum: ModelType, description: '模型类型' })
  @ApiQuery({ name: 'status', required: false, enum: ModelStatus, description: '模型状态' })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  @ApiResponse({ status: HttpStatus.OK, description: '成功获取模型列表' })
  async getModels(@Query() query: ModelQueryDto) {
    return await this.aiModelService.findModels(query);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取模型详情' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '成功获取模型详情' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '模型不存在' })
  async getModel(@Param('id') id: string): Promise<AIModel> {
    return await this.aiModelService.findById(id);
  }

  @Post()
  @ApiOperation({ summary: '创建新模型' })
  @ApiBody({ description: '模型创建数据' })
  @ApiResponse({ status: HttpStatus.CREATED, description: '成功创建模型' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: '请求参数错误' })
  async createModel(@Body() createModelDto: CreateModelDto): Promise<AIModel> {
    return await this.aiModelService.create(createModelDto);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新模型信息' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiBody({ description: '模型更新数据' })
  @ApiResponse({ status: HttpStatus.OK, description: '成功更新模型' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '模型不存在' })
  async updateModel(
    @Param('id') id: string,
    @Body() updateModelDto: UpdateModelDto,
  ): Promise<AIModel> {
    return await this.aiModelService.update(id, updateModelDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除模型' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '成功删除模型' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '模型不存在' })
  async deleteModel(@Param('id') id: string): Promise<void> {
    return await this.aiModelService.delete(id);
  }

  @Post(':id/upload')
  @ApiOperation({ summary: '上传模型文件' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiResponse({ status: HttpStatus.OK, description: '成功上传模型文件' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: '文件格式不支持' })
  async uploadModelFile(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
  ): Promise<AIModel> {
    return await this.aiModelService.uploadModelFile(id, file);
  }

  @Post(':id/load')
  @ApiOperation({ summary: '加载模型到内存' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '成功加载模型' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '模型不存在' })
  async loadModel(@Param('id') id: string): Promise<{ message: string; status: string }> {
    await this.aiModelService.loadModel(id);
    return { message: '模型加载成功', status: 'loaded' };
  }

  @Post(':id/unload')
  @ApiOperation({ summary: '卸载模型' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '成功卸载模型' })
  async unloadModel(@Param('id') id: string): Promise<{ message: string; status: string }> {
    await this.aiModelService.unloadModel(id);
    return { message: '模型卸载成功', status: 'unloaded' };
  }

  @Post(':id/inference')
  @ApiOperation({ summary: '模型推理' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiBody({ description: '推理请求数据' })
  @ApiResponse({ status: HttpStatus.OK, description: '推理成功' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: '推理参数错误' })
  async inference(
    @Param('id') id: string,
    @Body() inferenceRequest: InferenceRequestDto,
  ): Promise<any> {
    return await this.aiModelService.inference(id, inferenceRequest);
  }

  @Get(':id/status')
  @ApiOperation({ summary: '获取模型状态' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '成功获取模型状态' })
  async getModelStatus(@Param('id') id: string): Promise<any> {
    return await this.aiModelService.getModelStatus(id);
  }

  @Get(':id/metrics')
  @ApiOperation({ summary: '获取模型性能指标' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiQuery({ name: 'startDate', required: false, description: '开始日期' })
  @ApiQuery({ name: 'endDate', required: false, description: '结束日期' })
  @ApiResponse({ status: HttpStatus.OK, description: '成功获取性能指标' })
  async getModelMetrics(
    @Param('id') id: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<any> {
    return await this.aiModelService.getModelMetrics(id, startDate, endDate);
  }

  @Get(':id/logs')
  @ApiOperation({ summary: '获取模型推理日志' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiResponse({ status: HttpStatus.OK, description: '成功获取推理日志' })
  async getInferenceLogs(
    @Param('id') id: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
  ): Promise<any> {
    return await this.aiModelService.getInferenceLogs(id, page, limit);
  }
}
