import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import type {  Types  } from 'mongoose';
import { UIAnalyticsService } from './ui-analytics.service';
import type {  EventType, ResourceType  } from './schemas/ui-analytics.schema';

@ApiTags('ui-analytics')
@Controller('ui-analytics')
export class UIAnalyticsController {
  constructor(private readonly analyticsService: UIAnalyticsService) {}

  @Post('event')
  @ApiOperation({ summary: '记录分析事件' })
  @ApiResponse({ status: 201, description: '事件记录成功' })
  async recordEvent(@Body() eventData: {
    eventType: EventType;
    resourceType: ResourceType;
    resourceId: string;
    userId?: string;
    organizationId?: string;
    sessionId?: string;
    metadata?: any;
  }) {
    return this.analyticsService.recordEvent(
      eventData.eventType,
      eventData.resourceType,
      new Types.ObjectId(eventData.resourceId),
      eventData.userId ? new Types.ObjectId(eventData.userId) : undefined,
      eventData.organizationId ? new Types.ObjectId(eventData.organizationId) : undefined,
      eventData.sessionId,
      eventData.metadata,
    );
  }

  @Get('resource/:resourceId/stats')
  @ApiOperation({ summary: '获取资源统计' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getResourceStats(
    @Param('resourceId') resourceId: string,
    @Query('days') days?: number,
  ) {
    return this.analyticsService.getResourceStats(
      new Types.ObjectId(resourceId),
      days,
    );
  }

  @Get('popular/:resourceType')
  @ApiOperation({ summary: '获取热门资源' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getPopularResources(
    @Param('resourceType') resourceType: ResourceType,
    @Query('limit') limit?: number,
    @Query('days') days?: number,
  ) {
    return this.analyticsService.getPopularResources(resourceType, limit, days);
  }
}
