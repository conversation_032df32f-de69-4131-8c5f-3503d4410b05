import { IsString, <PERSON><PERSON>ptional, IsEnum, IsArray, IsObject, IsBoolean, IsNumber, Min, Max, IsMongoId } from 'class-validator';
import type {  Type, Transform  } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TemplateCategory, TemplateStatus, AccessLevel, UIElement } from '../schemas/ui-template.schema';

export class CreateTemplateDto {
  @ApiProperty({ description: '模板名称' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: '模板描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '模板分类', enum: TemplateCategory })
  @IsEnum(TemplateCategory)
  category: TemplateCategory;

  @ApiPropertyOptional({ description: '标签列表' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'UI元素列表' })
  @IsOptional()
  @IsArray()
  elements?: UIElement[];

  @ApiPropertyOptional({ description: '元数据' })
  @IsOptional()
  @IsObject()
  metadata?: {
    canvasSize: { width: number; height: number };
    gridSize: number;
    snapToGrid: boolean;
    backgroundColor: string;
    backgroundImage?: string;
    responsive: boolean;
    breakpoints?: Record<string, number>;
  };

  @ApiPropertyOptional({ description: '访问级别', enum: AccessLevel })
  @IsOptional()
  @IsEnum(AccessLevel)
  accessLevel?: AccessLevel;

  @ApiPropertyOptional({ description: '组织ID' })
  @IsOptional()
  @IsMongoId()
  organizationId?: string;

  @ApiPropertyOptional({ description: '团队ID' })
  @IsOptional()
  @IsMongoId()
  teamId?: string;

  @ApiPropertyOptional({ description: '项目ID' })
  @IsOptional()
  @IsMongoId()
  projectId?: string;

  @ApiPropertyOptional({ description: '缩略图' })
  @IsOptional()
  @IsString()
  thumbnail?: string;

  @ApiPropertyOptional({ description: '截图列表' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  screenshots?: string[];

  @ApiPropertyOptional({ description: '依赖项' })
  @IsOptional()
  @IsObject()
  dependencies?: {
    components: string[];
    themes: string[];
    assets: string[];
  };

  @ApiPropertyOptional({ description: '设置' })
  @IsOptional()
  @IsObject()
  settings?: {
    allowFork: boolean;
    allowComments: boolean;
    allowRating: boolean;
    requireApproval: boolean;
  };
}

export class UpdateTemplateDto {
  @ApiPropertyOptional({ description: '模板名称' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '模板描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '模板分类', enum: TemplateCategory })
  @IsOptional()
  @IsEnum(TemplateCategory)
  category?: TemplateCategory;

  @ApiPropertyOptional({ description: '标签列表' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'UI元素列表' })
  @IsOptional()
  @IsArray()
  elements?: UIElement[];

  @ApiPropertyOptional({ description: '元数据' })
  @IsOptional()
  @IsObject()
  metadata?: {
    canvasSize: { width: number; height: number };
    gridSize: number;
    snapToGrid: boolean;
    backgroundColor: string;
    backgroundImage?: string;
    responsive: boolean;
    breakpoints?: Record<string, number>;
  };

  @ApiPropertyOptional({ description: '模板状态', enum: TemplateStatus })
  @IsOptional()
  @IsEnum(TemplateStatus)
  status?: TemplateStatus;

  @ApiPropertyOptional({ description: '访问级别', enum: AccessLevel })
  @IsOptional()
  @IsEnum(AccessLevel)
  accessLevel?: AccessLevel;

  @ApiPropertyOptional({ description: '组织ID' })
  @IsOptional()
  @IsMongoId()
  organizationId?: string;

  @ApiPropertyOptional({ description: '团队ID' })
  @IsOptional()
  @IsMongoId()
  teamId?: string;

  @ApiPropertyOptional({ description: '项目ID' })
  @IsOptional()
  @IsMongoId()
  projectId?: string;

  @ApiPropertyOptional({ description: '缩略图' })
  @IsOptional()
  @IsString()
  thumbnail?: string;

  @ApiPropertyOptional({ description: '截图列表' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  screenshots?: string[];

  @ApiPropertyOptional({ description: '依赖项' })
  @IsOptional()
  @IsObject()
  dependencies?: {
    components: string[];
    themes: string[];
    assets: string[];
  };

  @ApiPropertyOptional({ description: '设置' })
  @IsOptional()
  @IsObject()
  settings?: {
    allowFork: boolean;
    allowComments: boolean;
    allowRating: boolean;
    requireApproval: boolean;
  };

  @ApiPropertyOptional({ description: '版本描述' })
  @IsOptional()
  @IsString()
  versionDescription?: string;
}

export class QueryTemplateDto {
  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({ description: '模板分类', enum: TemplateCategory })
  @IsOptional()
  @IsEnum(TemplateCategory)
  category?: TemplateCategory;

  @ApiPropertyOptional({ description: '模板状态', enum: TemplateStatus })
  @IsOptional()
  @IsEnum(TemplateStatus)
  status?: TemplateStatus;

  @ApiPropertyOptional({ description: '访问级别', enum: AccessLevel })
  @IsOptional()
  @IsEnum(AccessLevel)
  accessLevel?: AccessLevel;

  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: '排序字段', default: 'createdAt' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', enum: ['asc', 'desc'], default: 'desc' })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';

  @ApiPropertyOptional({ description: '创建者ID' })
  @IsOptional()
  @IsMongoId()
  createdBy?: string;

  @ApiPropertyOptional({ description: '组织ID' })
  @IsOptional()
  @IsMongoId()
  organizationId?: string;

  @ApiPropertyOptional({ description: '项目ID' })
  @IsOptional()
  @IsMongoId()
  projectId?: string;
}

export class ForkTemplateDto {
  @ApiProperty({ description: '新模板名称' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: '新模板描述' })
  @IsOptional()
  @IsString()
  description?: string;
}

export class RateTemplateDto {
  @ApiProperty({ description: '评分', minimum: 1, maximum: 5 })
  @IsNumber()
  @Min(1)
  @Max(5)
  rating: number;

  @ApiPropertyOptional({ description: '评价内容' })
  @IsOptional()
  @IsString()
  comment?: string;
}

export class TemplateStatsDto {
  @ApiProperty({ description: '模板ID' })
  @IsMongoId()
  templateId: string;

  @ApiProperty({ description: '统计类型', enum: ['view', 'download', 'fork', 'like'] })
  @IsEnum(['view', 'download', 'fork', 'like'])
  type: 'view' | 'download' | 'fork' | 'like';

  @ApiPropertyOptional({ description: '用户ID' })
  @IsOptional()
  @IsMongoId()
  userId?: string;

  @ApiPropertyOptional({ description: '用户代理' })
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiPropertyOptional({ description: 'IP地址' })
  @IsOptional()
  @IsString()
  ipAddress?: string;
}

export class TemplateResponseDto {
  @ApiProperty({ description: '模板ID' })
  id: string;

  @ApiProperty({ description: '模板名称' })
  name: string;

  @ApiProperty({ description: '模板描述' })
  description: string;

  @ApiProperty({ description: '模板分类' })
  category: TemplateCategory;

  @ApiProperty({ description: '标签列表' })
  tags: string[];

  @ApiProperty({ description: 'UI元素列表' })
  elements: UIElement[];

  @ApiProperty({ description: '元数据' })
  metadata: any;

  @ApiProperty({ description: '模板状态' })
  status: TemplateStatus;

  @ApiProperty({ description: '访问级别' })
  accessLevel: AccessLevel;

  @ApiProperty({ description: '创建者信息' })
  createdBy: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };

  @ApiProperty({ description: '更新者信息' })
  updatedBy: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };

  @ApiProperty({ description: '版本号' })
  version: string;

  @ApiProperty({ description: '缩略图' })
  thumbnail?: string;

  @ApiProperty({ description: '截图列表' })
  screenshots: string[];

  @ApiProperty({ description: '统计信息' })
  statistics: {
    views: number;
    downloads: number;
    forks: number;
    likes: number;
    rating: number;
    ratingCount: number;
  };

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({ description: '发布时间' })
  publishedAt?: Date;
}

export class TemplateListResponseDto {
  @ApiProperty({ description: '模板列表', type: [TemplateResponseDto] })
  templates: TemplateResponseDto[];

  @ApiProperty({ description: '总数' })
  total: number;

  @ApiProperty({ description: '当前页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;

  @ApiProperty({ description: '总页数' })
  totalPages: number;

  @ApiProperty({ description: '是否有下一页' })
  hasNext: boolean;

  @ApiProperty({ description: '是否有上一页' })
  hasPrev: boolean;
}
