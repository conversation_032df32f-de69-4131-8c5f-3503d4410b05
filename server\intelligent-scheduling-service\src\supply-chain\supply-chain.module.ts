import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';

// 实体
import { Supplier } from './entities/supplier.entity';
import { SupplyChainNode } from './entities/supply-chain-node.entity';
import { CollaborationPlan } from './entities/collaboration-plan.entity';

// 控制器
import { SupplyChainController } from './supply-chain.controller';

// 服务
import { SupplyChainOptimizationService } from './supply-chain-optimization.service';
import { CollaborationService } from './collaboration.service';
import { SupplyChainAnalyticsService } from './supply-chain-analytics.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Supplier,
      SupplyChainNode,
      CollaborationPlan,
    ]),
  ],

  controllers: [SupplyChainController],

  providers: [
    SupplyChainOptimizationService,
    CollaborationService,
    SupplyChainAnalyticsService,
  ],

  exports: [
    SupplyChainOptimizationService,
    CollaborationService,
    SupplyChainAnalyticsService,
  ],
})
export class SupplyChainModule {}
