import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { StandardsController } from './standards.controller';
import { StandardsService } from './standards.service';
import { IndustryStandard, StandardCertification, ComplianceRecord } from '../../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([IndustryStandard, StandardCertification, ComplianceRecord]),
  ],
  controllers: [StandardsController],
  providers: [StandardsService],
  exports: [StandardsService],
})
export class StandardsModule {}
