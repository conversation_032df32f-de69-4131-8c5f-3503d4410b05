/**
 * 空间分析DTO定义
 */
import { IsString, IsNumber, IsArray, IsOptional, IsEnum, IsBoolean, ValidateNested } from 'class-validator';
import type {  Type  } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum BufferType {
  FIXED = 'fixed',
  VARIABLE = 'variable',
  MULTIPLE = 'multiple'
}

export enum OverlayOperation {
  INTERSECTION = 'intersection',
  UNION = 'union',
  DIFFERENCE = 'difference',
  SYMMETRIC_DIFFERENCE = 'symmetric_difference',
  CLIP = 'clip'
}

export enum NetworkAnalysisType {
  SHORTEST_PATH = 'shortest_path',
  SERVICE_AREA = 'service_area',
  CLOSEST_FACILITY = 'closest_facility',
  ROUTE_OPTIMIZATION = 'route_optimization'
}

export enum InterpolationMethod {
  IDW = 'idw',
  KRIGING = 'kriging',
  SPLINE = 'spline',
  NATURAL_NEIGHBOR = 'natural_neighbor'
}

export class BufferAnalysisDto {
  @ApiProperty({ description: '输入图层ID' })
  @IsString()
  inputLayerId: string;

  @ApiProperty({ description: '缓冲区距离' })
  @IsNumber()
  distance: number;

  @ApiProperty({ description: '缓冲区类型', enum: BufferType })
  @IsEnum(BufferType)
  bufferType: BufferType;

  @ApiPropertyOptional({ description: '距离单位' })
  @IsOptional()
  @IsString()
  unit?: string = 'meters';

  @ApiPropertyOptional({ description: '是否合并结果' })
  @IsOptional()
  @IsBoolean()
  dissolve?: boolean = false;

  @ApiPropertyOptional({ description: '输出图层名称' })
  @IsOptional()
  @IsString()
  outputLayerName?: string;
}

export class OverlayAnalysisDto {
  @ApiProperty({ description: '输入图层ID' })
  @IsString()
  inputLayerId: string;

  @ApiProperty({ description: '叠加图层ID' })
  @IsString()
  overlayLayerId: string;

  @ApiProperty({ description: '叠加操作类型', enum: OverlayOperation })
  @IsEnum(OverlayOperation)
  operation: OverlayOperation;

  @ApiPropertyOptional({ description: '输出图层名称' })
  @IsOptional()
  @IsString()
  outputLayerName?: string;

  @ApiPropertyOptional({ description: '保留属性字段' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keepFields?: string[];
}

export class NetworkAnalysisDto {
  @ApiProperty({ description: '网络图层ID' })
  @IsString()
  networkLayerId: string;

  @ApiProperty({ description: '分析类型', enum: NetworkAnalysisType })
  @IsEnum(NetworkAnalysisType)
  analysisType: NetworkAnalysisType;

  @ApiProperty({ description: '起点坐标' })
  @IsArray()
  @IsNumber({}, { each: true })
  startPoints: number[][];

  @ApiPropertyOptional({ description: '终点坐标' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  endPoints?: number[][];

  @ApiPropertyOptional({ description: '服务半径' })
  @IsOptional()
  @IsNumber()
  serviceRadius?: number;

  @ApiPropertyOptional({ description: '阻抗字段' })
  @IsOptional()
  @IsString()
  impedanceField?: string;
}

export class StatisticalAnalysisDto {
  @ApiProperty({ description: '输入图层ID' })
  @IsString()
  inputLayerId: string;

  @ApiProperty({ description: '分析字段' })
  @IsString()
  analysisField: string;

  @ApiPropertyOptional({ description: '分组字段' })
  @IsOptional()
  @IsString()
  groupByField?: string;

  @ApiPropertyOptional({ description: '统计类型' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  statisticTypes?: string[] = ['count', 'sum', 'mean', 'min', 'max', 'std'];
}

export class ProximityAnalysisDto {
  @ApiProperty({ description: '目标图层ID' })
  @IsString()
  targetLayerId: string;

  @ApiProperty({ description: '参考图层ID' })
  @IsString()
  referenceLayerId: string;

  @ApiProperty({ description: '搜索半径' })
  @IsNumber()
  searchRadius: number;

  @ApiPropertyOptional({ description: '最大邻近数量' })
  @IsOptional()
  @IsNumber()
  maxNeighbors?: number = 5;

  @ApiPropertyOptional({ description: '距离单位' })
  @IsOptional()
  @IsString()
  unit?: string = 'meters';
}

export class HotspotAnalysisDto {
  @ApiProperty({ description: '输入图层ID' })
  @IsString()
  inputLayerId: string;

  @ApiProperty({ description: '分析字段' })
  @IsString()
  analysisField: string;

  @ApiPropertyOptional({ description: '邻域距离' })
  @IsOptional()
  @IsNumber()
  neighborhoodDistance?: number;

  @ApiPropertyOptional({ description: '置信度水平' })
  @IsOptional()
  @IsNumber()
  confidenceLevel?: number = 0.95;

  @ApiPropertyOptional({ description: '聚合方法' })
  @IsOptional()
  @IsString()
  aggregationMethod?: string = 'sum';
}

export class InterpolationDto {
  @ApiProperty({ description: '输入点图层ID' })
  @IsString()
  inputLayerId: string;

  @ApiProperty({ description: '插值字段' })
  @IsString()
  valueField: string;

  @ApiProperty({ description: '插值方法', enum: InterpolationMethod })
  @IsEnum(InterpolationMethod)
  method: InterpolationMethod;

  @ApiPropertyOptional({ description: '输出栅格分辨率' })
  @IsOptional()
  @IsNumber()
  cellSize?: number = 100;

  @ApiPropertyOptional({ description: '搜索半径' })
  @IsOptional()
  @IsNumber()
  searchRadius?: number;

  @ApiPropertyOptional({ description: '幂参数(IDW)' })
  @IsOptional()
  @IsNumber()
  power?: number = 2;

  @ApiPropertyOptional({ description: '最小邻近点数' })
  @IsOptional()
  @IsNumber()
  minNeighbors?: number = 3;

  @ApiPropertyOptional({ description: '最大邻近点数' })
  @IsOptional()
  @IsNumber()
  maxNeighbors?: number = 12;
}
