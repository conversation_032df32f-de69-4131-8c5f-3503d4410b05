import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';

// 业务模块
import { KnowledgeModule } from './knowledge/knowledge.module';
import { InferenceModule } from './inference/inference.module';
import { ExpertModule } from './expert/expert.module';
import { HealthModule } from './health/health.module';

// 实体
import { entities } from './entities';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // 数据库模块（可选）
    ...(process.env.DB_HOST ? [
      TypeOrmModule.forRootAsync({
        useFactory: (configService: ConfigService) => ({
          type: 'mysql',
          host: configService.get<string>('DB_HOST', 'localhost'),
          port: configService.get<number>('DB_PORT', 3306),
          username: configService.get<string>('DB_USERNAME', 'root'),
          password: configService.get<string>('DB_PASSWORD', ''),
          database: configService.get<string>('DB_DATABASE', 'knowledge_graph'),
          entities: entities,
          synchronize: configService.get<string>('NODE_ENV') === 'development',
          logging: configService.get<string>('NODE_ENV') === 'development',
          timezone: '+08:00',
          charset: 'utf8mb4',
          extra: {
            connectionLimit: 10,
          },
        }),
        inject: [ConfigService],
      })
    ] : []),

    // 定时任务模块
    ScheduleModule.forRoot(),

    // 事件发射器模块
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // 业务模块
    KnowledgeModule,
    InferenceModule,
    ExpertModule,
    HealthModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
