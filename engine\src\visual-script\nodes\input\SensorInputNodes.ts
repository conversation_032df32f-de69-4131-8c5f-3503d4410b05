/**
 * 传感器输入节点集合
 * 提供各种设备传感器的输入功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3 } from 'three';
import { advancedInputManager } from './AdvancedInputNodes';

/**
 * 加速度计节点
 * 获取设备加速度计数据
 */
export class AccelerometerNode extends VisualScriptNode {
  public static readonly TYPE = 'input/accelerometer';
  public static readonly NAME = '加速度计';
  public static readonly DESCRIPTION = '获取设备加速度计数据，检测设备的线性加速度';

  constructor(type: string = AccelerometerNode.TYPE, name: string = AccelerometerNode.NAME, id?: string) {
    super(type, name, id);
    
    // 输入端口
    this.addInputPort('enable', '启用', 'boolean', true);
    this.addInputPort('includeGravity', '包含重力', 'boolean', true);
    this.addInputPort('sensitivity', '敏感度', 'number', 1.0);
    this.addInputPort('threshold', '阈值', 'number', 0.1);
    
    // 输出端口
    this.addOutputPort('acceleration', '加速度', 'vector3');
    this.addOutputPort('x', 'X轴加速度', 'number');
    this.addOutputPort('y', 'Y轴加速度', 'number');
    this.addOutputPort('z', 'Z轴加速度', 'number');
    this.addOutputPort('magnitude', '加速度大小', 'number');
    this.addOutputPort('timestamp', '时间戳', 'number');
    this.addOutputPort('onShake', '摇晃检测', 'event');
    this.addOutputPort('onTilt', '倾斜检测', 'event');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const includeGravity = inputs?.includeGravity !== false;
      const sensitivity = inputs?.sensitivity as number || 1.0;
      const threshold = inputs?.threshold as number || 0.1;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 确保输入管理器已初始化
      if (!advancedInputManager['initialized']) {
        advancedInputManager.initialize();
      }

      const sensorData = advancedInputManager.getSensorData('accelerometer');
      
      if (!sensorData) {
        return this.getDefaultOutputs();
      }

      const acceleration = new Vector3(
        sensorData.x * sensitivity,
        sensorData.y * sensitivity,
        sensorData.z * sensitivity
      );

      const magnitude = acceleration.length();
      const onShake = magnitude > threshold * 10; // 摇晃阈值
      const onTilt = Math.abs(acceleration.x) > threshold || Math.abs(acceleration.y) > threshold;

      return {
        acceleration,
        x: acceleration.x,
        y: acceleration.y,
        z: acceleration.z,
        magnitude,
        timestamp: sensorData.timestamp,
        onShake,
        onTilt
      };

    } catch (error) {
      Debug.error('AccelerometerNode', '加速度计数据获取失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      acceleration: new Vector3(0, 0, 0),
      x: 0,
      y: 0,
      z: 0,
      magnitude: 0,
      timestamp: 0,
      onShake: false,
      onTilt: false
    };
  }
}

/**
 * 陀螺仪节点
 * 获取设备陀螺仪数据
 */
export class GyroscopeNode extends VisualScriptNode {
  public static readonly TYPE = 'input/gyroscope';
  public static readonly NAME = '陀螺仪';
  public static readonly DESCRIPTION = '获取设备陀螺仪数据，检测设备的旋转角度';

  constructor(type: string = GyroscopeNode.TYPE, name: string = GyroscopeNode.NAME, id?: string) {
    super(type, name, id);
    
    // 输入端口
    this.addInputPort('enable', '启用', 'boolean', true);
    this.addInputPort('sensitivity', '敏感度', 'number', 1.0);
    this.addInputPort('smoothing', '平滑度', 'number', 0.1);
    
    // 输出端口
    this.addOutputPort('rotation', '旋转角度', 'vector3');
    this.addOutputPort('alpha', 'Alpha (Z轴)', 'number');
    this.addOutputPort('beta', 'Beta (X轴)', 'number');
    this.addOutputPort('gamma', 'Gamma (Y轴)', 'number');
    this.addOutputPort('timestamp', '时间戳', 'number');
    this.addOutputPort('onRotation', '旋转检测', 'event');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const sensitivity = inputs?.sensitivity as number || 1.0;
      const smoothing = inputs?.smoothing as number || 0.1;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 确保输入管理器已初始化
      if (!advancedInputManager['initialized']) {
        advancedInputManager.initialize();
      }

      const sensorData = advancedInputManager.getSensorData('gyroscope');
      
      if (!sensorData) {
        return this.getDefaultOutputs();
      }

      const rotation = new Vector3(
        sensorData.y * sensitivity, // Beta -> X轴
        sensorData.z * sensitivity, // Gamma -> Y轴
        sensorData.x * sensitivity  // Alpha -> Z轴
      );

      const onRotation = Math.abs(sensorData.x) > 1 || Math.abs(sensorData.y) > 1 || Math.abs(sensorData.z) > 1;

      return {
        rotation,
        alpha: sensorData.x,
        beta: sensorData.y,
        gamma: sensorData.z,
        timestamp: sensorData.timestamp,
        onRotation
      };

    } catch (error) {
      Debug.error('GyroscopeNode', '陀螺仪数据获取失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      rotation: new Vector3(0, 0, 0),
      alpha: 0,
      beta: 0,
      gamma: 0,
      timestamp: 0,
      onRotation: false
    };
  }
}

/**
 * 指南针节点
 * 获取设备指南针方向
 */
export class CompassNode extends VisualScriptNode {
  public static readonly TYPE = 'input/compass';
  public static readonly NAME = '指南针';
  public static readonly DESCRIPTION = '获取设备指南针方向，提供磁北方向信息';

  constructor(type: string = CompassNode.TYPE, name: string = CompassNode.NAME, id?: string) {
    super(type, name, id);
    
    // 输入端口
    this.addInputPort('enable', '启用', 'boolean', true);
    this.addInputPort('magneticDeclination', '磁偏角', 'number', 0);
    
    // 输出端口
    this.addOutputPort('heading', '方向角', 'number');
    this.addOutputPort('magneticHeading', '磁方向角', 'number');
    this.addOutputPort('trueHeading', '真方向角', 'number');
    this.addOutputPort('accuracy', '精度', 'number');
    this.addOutputPort('timestamp', '时间戳', 'number');
    this.addOutputPort('onHeadingChange', '方向改变', 'event');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const magneticDeclination = inputs?.magneticDeclination as number || 0;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 确保输入管理器已初始化
      if (!advancedInputManager['initialized']) {
        advancedInputManager.initialize();
      }

      const gyroscopeData = advancedInputManager.getSensorData('gyroscope');
      
      if (!gyroscopeData) {
        return this.getDefaultOutputs();
      }

      // 使用陀螺仪的alpha值作为磁方向角
      const magneticHeading = gyroscopeData.x;
      const trueHeading = magneticHeading + magneticDeclination;
      const heading = trueHeading >= 0 ? trueHeading : trueHeading + 360;
      
      // 模拟精度值
      const accuracy = gyroscopeData.accuracy || 5;
      const onHeadingChange = Math.abs(magneticHeading) > 1;

      return {
        heading,
        magneticHeading,
        trueHeading,
        accuracy,
        timestamp: gyroscopeData.timestamp,
        onHeadingChange
      };

    } catch (error) {
      Debug.error('CompassNode', '指南针数据获取失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      heading: 0,
      magneticHeading: 0,
      trueHeading: 0,
      accuracy: 0,
      timestamp: 0,
      onHeadingChange: false
    };
  }
}

/**
 * 距离传感器节点
 * 检测设备与物体的距离
 */
export class ProximityNode extends VisualScriptNode {
  public static readonly TYPE = 'input/proximity';
  public static readonly NAME = '距离传感器';
  public static readonly DESCRIPTION = '检测设备与物体的距离，通常用于检测设备是否靠近用户';

  constructor(type: string = ProximityNode.TYPE, name: string = ProximityNode.NAME, id?: string) {
    super(type, name, id);
    
    // 输入端口
    this.addInputPort('enable', '启用', 'boolean', true);
    this.addInputPort('threshold', '距离阈值', 'number', 5.0);
    
    // 输出端口
    this.addOutputPort('distance', '距离', 'number');
    this.addOutputPort('isNear', '是否接近', 'boolean');
    this.addOutputPort('maxRange', '最大范围', 'number');
    this.addOutputPort('timestamp', '时间戳', 'number');
    this.addOutputPort('onNear', '接近检测', 'event');
    this.addOutputPort('onFar', '远离检测', 'event');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const threshold = inputs?.threshold as number || 5.0;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 模拟距离传感器数据
      const distance = Math.random() * 10; // 0-10cm
      const isNear = distance < threshold;
      const maxRange = 10.0;
      const timestamp = Date.now();

      return {
        distance,
        isNear,
        maxRange,
        timestamp,
        onNear: isNear,
        onFar: !isNear
      };

    } catch (error) {
      Debug.error('ProximityNode', '距离传感器数据获取失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      distance: 0,
      isNear: false,
      maxRange: 10.0,
      timestamp: 0,
      onNear: false,
      onFar: false
    };
  }
}

/**
 * 光线传感器节点
 * 检测环境光线强度
 */
export class LightSensorNode extends VisualScriptNode {
  public static readonly TYPE = 'input/light_sensor';
  public static readonly NAME = '光线传感器';
  public static readonly DESCRIPTION = '检测环境光线强度，用于自动调节屏幕亮度等功能';

  constructor(type: string = LightSensorNode.TYPE, name: string = LightSensorNode.NAME, id?: string) {
    super(type, name, id);

    // 输入端口
    this.addInputPort('enable', '启用', 'boolean', true);
    this.addInputPort('sensitivity', '敏感度', 'number', 1.0);
    this.addInputPort('darkThreshold', '暗光阈值', 'number', 10);
    this.addInputPort('brightThreshold', '亮光阈值', 'number', 1000);

    // 输出端口
    this.addOutputPort('illuminance', '照度', 'number');
    this.addOutputPort('isDark', '是否暗光', 'boolean');
    this.addOutputPort('isBright', '是否亮光', 'boolean');
    this.addOutputPort('lightLevel', '光线等级', 'string');
    this.addOutputPort('timestamp', '时间戳', 'number');
    this.addOutputPort('onDark', '暗光检测', 'event');
    this.addOutputPort('onBright', '亮光检测', 'event');
    this.addOutputPort('onLightChange', '光线变化', 'event');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const sensitivity = inputs?.sensitivity as number || 1.0;
      const darkThreshold = inputs?.darkThreshold as number || 10;
      const brightThreshold = inputs?.brightThreshold as number || 1000;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 模拟光线传感器数据 (勒克斯单位)
      const illuminance = Math.random() * 2000 * sensitivity;
      const isDark = illuminance < darkThreshold;
      const isBright = illuminance > brightThreshold;

      let lightLevel = 'normal';
      if (isDark) lightLevel = 'dark';
      else if (isBright) lightLevel = 'bright';

      const timestamp = Date.now();

      return {
        illuminance,
        isDark,
        isBright,
        lightLevel,
        timestamp,
        onDark: isDark,
        onBright: isBright,
        onLightChange: true
      };

    } catch (error) {
      Debug.error('LightSensorNode', '光线传感器数据获取失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      illuminance: 0,
      isDark: false,
      isBright: false,
      lightLevel: 'normal',
      timestamp: 0,
      onDark: false,
      onBright: false,
      onLightChange: false
    };
  }
}

/**
 * 压力传感器节点
 * 检测设备受到的压力
 */
export class PressureSensorNode extends VisualScriptNode {
  public static readonly TYPE = 'input/pressure_sensor';
  public static readonly NAME = '压力传感器';
  public static readonly DESCRIPTION = '检测设备受到的压力，如屏幕按压力度等';

  constructor(type: string = PressureSensorNode.TYPE, name: string = PressureSensorNode.NAME, id?: string) {
    super(type, name, id);

    // 输入端口
    this.addInputPort('enable', '启用', 'boolean', true);
    this.addInputPort('sensitivity', '敏感度', 'number', 1.0);
    this.addInputPort('lightPressureThreshold', '轻压阈值', 'number', 0.3);
    this.addInputPort('heavyPressureThreshold', '重压阈值', 'number', 0.8);

    // 输出端口
    this.addOutputPort('pressure', '压力值', 'number');
    this.addOutputPort('normalizedPressure', '标准化压力', 'number');
    this.addOutputPort('isLightPress', '是否轻压', 'boolean');
    this.addOutputPort('isHeavyPress', '是否重压', 'boolean');
    this.addOutputPort('pressureLevel', '压力等级', 'string');
    this.addOutputPort('timestamp', '时间戳', 'number');
    this.addOutputPort('onPressureChange', '压力变化', 'event');
    this.addOutputPort('onLightPress', '轻压检测', 'event');
    this.addOutputPort('onHeavyPress', '重压检测', 'event');
  }

  public execute(inputs?: any): any {
    try {
      const enable = inputs?.enable !== false;
      const sensitivity = inputs?.sensitivity as number || 1.0;
      const lightPressureThreshold = inputs?.lightPressureThreshold as number || 0.3;
      const heavyPressureThreshold = inputs?.heavyPressureThreshold as number || 0.8;

      if (!enable) {
        return this.getDefaultOutputs();
      }

      // 模拟压力传感器数据 (0-1范围)
      const pressure = Math.random() * sensitivity;
      const normalizedPressure = Math.min(pressure, 1.0);
      const isLightPress = normalizedPressure >= lightPressureThreshold && normalizedPressure < heavyPressureThreshold;
      const isHeavyPress = normalizedPressure >= heavyPressureThreshold;

      let pressureLevel = 'none';
      if (isLightPress) pressureLevel = 'light';
      else if (isHeavyPress) pressureLevel = 'heavy';

      const timestamp = Date.now();

      return {
        pressure,
        normalizedPressure,
        isLightPress,
        isHeavyPress,
        pressureLevel,
        timestamp,
        onPressureChange: normalizedPressure > 0,
        onLightPress: isLightPress,
        onHeavyPress: isHeavyPress
      };

    } catch (error) {
      Debug.error('PressureSensorNode', '压力传感器数据获取失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      pressure: 0,
      normalizedPressure: 0,
      isLightPress: false,
      isHeavyPress: false,
      pressureLevel: 'none',
      timestamp: 0,
      onPressureChange: false,
      onLightPress: false,
      onHeavyPress: false
    };
  }
}
