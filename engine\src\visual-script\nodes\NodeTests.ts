/**
 * 视觉脚本节点测试
 * 批次1.7和批次2.1节点功能测试
 * 验证所有新开发的节点是否正常工作
 */
import { nodeRegistry, createNode, getNodeStatistics, getAllNodeInfo } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

/**
 * 节点测试类
 */
export class NodeTests {
  private testResults: Map<string, any> = new Map();

  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<void> {
    console.log('开始运行DL引擎视觉脚本节点测试...');
    
    // 测试节点注册
    this.testNodeRegistration();
    
    // 测试动画系统增强节点
    await this.testAnimationNodes();
    
    // 测试用户服务节点
    await this.testUserServiceNodes();
    
    // 输出测试结果
    this.outputTestResults();
  }

  /**
   * 测试节点注册
   */
  private testNodeRegistration(): void {
    console.log('\n=== 测试节点注册 ===');
    
    try {
      const stats = getNodeStatistics();
      const allNodes = getAllNodeInfo();
      
      console.log(`总注册节点数: ${stats.totalNodes}`);
      console.log(`节点分类数: ${stats.categories}`);
      console.log('分类统计:', stats.categoryStats);
      
      // 验证预期的节点数量
      const expectedAnimationNodes = 10;
      const expectedUserServiceNodes = 12;
      const expectedTotal = expectedAnimationNodes + expectedUserServiceNodes;
      
      if (stats.totalNodes >= expectedTotal) {
        this.testResults.set('节点注册', { 
          status: 'PASS', 
          message: `成功注册 ${stats.totalNodes} 个节点` 
        });
      } else {
        this.testResults.set('节点注册', { 
          status: 'FAIL', 
          message: `节点数量不足，期望 ${expectedTotal}，实际 ${stats.totalNodes}` 
        });
      }
      
    } catch (error) {
      this.testResults.set('节点注册', { 
        status: 'ERROR', 
        message: `注册测试失败: ${error}` 
      });
    }
  }

  /**
   * 测试动画系统增强节点
   */
  private async testAnimationNodes(): Promise<void> {
    console.log('\n=== 测试动画系统增强节点 ===');
    
    const animationNodeTypes = [
      'AnimationBlendTree',
      'AnimationStateMachine',
      'IKSystem',
      'AnimationRetargeting',
      'AnimationCompression',
      'AnimationOptimization',
      'AnimationBaking',
      'AnimationExport',
      'AnimationImport',
      'AnimationValidation'
    ];

    for (const nodeType of animationNodeTypes) {
      await this.testSingleNode(nodeType, this.getAnimationTestInputs(nodeType));
    }
  }

  /**
   * 测试用户服务节点
   */
  private async testUserServiceNodes(): Promise<void> {
    console.log('\n=== 测试用户服务节点 ===');
    
    const userServiceNodeTypes = [
      'UserAuthentication',
      'UserRegistration',
      'UserProfile',
      'UserPermission',
      'UserRole',
      'UserSession',
      'UserPreferences',
      'UserActivity',
      'UserAnalytics',
      'UserNotification',
      'UserGroup',
      'UserSync'
    ];

    for (const nodeType of userServiceNodeTypes) {
      await this.testSingleNode(nodeType, this.getUserServiceTestInputs(nodeType));
    }
  }

  /**
   * 测试单个节点
   */
  private async testSingleNode(nodeType: string, testInputs: any): Promise<void> {
    try {
      console.log(`测试节点: ${nodeType}`);
      
      // 创建节点实例
      const node = createNode(nodeType);
      
      if (!node) {
        this.testResults.set(nodeType, { 
          status: 'FAIL', 
          message: '无法创建节点实例' 
        });
        return;
      }

      // 验证节点基本属性
      if (!node.id || !node.name || !node.nodeType) {
        this.testResults.set(nodeType, { 
          status: 'FAIL', 
          message: '节点基本属性缺失' 
        });
        return;
      }

      // 执行节点
      const result = node.execute(testInputs);
      
      // 验证执行结果
      if (result !== null && result !== undefined) {
        this.testResults.set(nodeType, { 
          status: 'PASS', 
          message: '节点执行成功',
          result: this.sanitizeResult(result)
        });
      } else {
        this.testResults.set(nodeType, { 
          status: 'WARN', 
          message: '节点执行返回空结果' 
        });
      }
      
    } catch (error) {
      this.testResults.set(nodeType, { 
        status: 'ERROR', 
        message: `节点执行失败: ${error}` 
      });
    }
  }

  /**
   * 获取动画节点测试输入
   */
  private getAnimationTestInputs(nodeType: string): any {
    const baseInputs = {
      entity: { id: 'test_entity', name: 'TestEntity' },
      animation: { 
        name: 'test_animation', 
        duration: 1.0, 
        tracks: [], 
        loop: false, 
        speed: 1.0 
      }
    };

    switch (nodeType) {
      case 'AnimationBlendTree':
        return {
          ...baseInputs,
          animations: [baseInputs.animation],
          weights: [1.0],
          blendMode: 'additive'
        };
      
      case 'AnimationStateMachine':
        return {
          ...baseInputs,
          states: { idle: baseInputs.animation },
          transitions: { idle: [] },
          trigger: 'play'
        };
      
      case 'IKSystem':
        return {
          ...baseInputs,
          ikChain: { bones: ['bone1', 'bone2'] },
          targetPosition: { x: 0, y: 1, z: 0 }
        };
      
      case 'AnimationRetargeting':
        return {
          sourceAnimation: baseInputs.animation,
          sourceSkeleton: { bones: {} },
          targetSkeleton: { bones: {} }
        };
      
      case 'AnimationCompression':
        return {
          animation: baseInputs.animation,
          compressionLevel: 0.5
        };
      
      case 'AnimationOptimization':
        return {
          animations: [baseInputs.animation],
          optimizationMode: 'balanced'
        };
      
      case 'AnimationBaking':
        return {
          ...baseInputs,
          startTime: 0,
          endTime: 1,
          frameRate: 30
        };
      
      case 'AnimationExport':
        return {
          animation: baseInputs.animation,
          format: 'json'
        };
      
      case 'AnimationImport':
        return {
          fileData: JSON.stringify(baseInputs.animation),
          format: 'json'
        };
      
      case 'AnimationValidation':
        return {
          animation: baseInputs.animation,
          strictMode: false
        };
      
      default:
        return baseInputs;
    }
  }

  /**
   * 获取用户服务节点测试输入
   */
  private getUserServiceTestInputs(nodeType: string): any {
    const baseInputs = {
      userId: 'test_user_123',
      action: 'get'
    };

    switch (nodeType) {
      case 'UserAuthentication':
        return {
          username: 'testuser',
          password: 'testpass123',
          authMethod: 'password'
        };
      
      case 'UserRegistration':
        return {
          username: 'newuser',
          email: '<EMAIL>',
          password: 'newpass123',
          confirmPassword: 'newpass123',
          agreementAccepted: true
        };
      
      case 'UserProfile':
        return {
          ...baseInputs,
          action: 'get'
        };
      
      case 'UserPermission':
        return {
          ...baseInputs,
          permission: 'read',
          action: 'check'
        };
      
      case 'UserRole':
        return {
          ...baseInputs,
          roleName: 'user',
          action: 'check'
        };
      
      case 'UserSession':
        return {
          sessionId: 'test_session_123',
          action: 'validate'
        };
      
      case 'UserPreferences':
        return {
          ...baseInputs,
          action: 'get'
        };
      
      case 'UserActivity':
        return {
          ...baseInputs,
          action: 'log',
          activityType: 'test'
        };
      
      case 'UserAnalytics':
        return {
          ...baseInputs,
          analysisType: 'overview',
          timeRange: '7d'
        };
      
      case 'UserNotification':
        return {
          ...baseInputs,
          action: 'send',
          title: 'Test Notification',
          message: 'This is a test notification'
        };
      
      case 'UserGroup':
        return {
          ...baseInputs,
          action: 'list'
        };
      
      case 'UserSync':
        return {
          ...baseInputs,
          action: 'sync',
          syncData: { test: 'data' }
        };
      
      default:
        return baseInputs;
    }
  }

  /**
   * 清理结果数据（移除敏感信息）
   */
  private sanitizeResult(result: any): any {
    if (typeof result !== 'object' || result === null) {
      return result;
    }

    const sanitized = { ...result };
    
    // 移除敏感字段
    const sensitiveFields = ['password', 'token', 'sessionId', 'privateKey'];
    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  /**
   * 输出测试结果
   */
  private outputTestResults(): void {
    console.log('\n=== 测试结果汇总 ===');
    
    let passCount = 0;
    let failCount = 0;
    let errorCount = 0;
    let warnCount = 0;

    for (const [nodeName, result] of this.testResults) {
      const status = result.status;
      const message = result.message;
      
      console.log(`${nodeName}: ${status} - ${message}`);
      
      switch (status) {
        case 'PASS': passCount++; break;
        case 'FAIL': failCount++; break;
        case 'ERROR': errorCount++; break;
        case 'WARN': warnCount++; break;
      }
    }

    console.log('\n=== 统计信息 ===');
    console.log(`通过: ${passCount}`);
    console.log(`失败: ${failCount}`);
    console.log(`错误: ${errorCount}`);
    console.log(`警告: ${warnCount}`);
    console.log(`总计: ${this.testResults.size}`);

    const successRate = ((passCount / this.testResults.size) * 100).toFixed(2);
    console.log(`成功率: ${successRate}%`);

    if (failCount === 0 && errorCount === 0) {
      console.log('\n✅ 所有节点测试通过！');
    } else {
      console.log('\n❌ 部分节点测试失败，请检查上述错误信息。');
    }
  }

  /**
   * 获取测试结果
   */
  public getTestResults(): Map<string, any> {
    return new Map(this.testResults);
  }
}

/**
 * 运行测试的便捷函数
 */
export async function runNodeTests(): Promise<void> {
  const tester = new NodeTests();
  await tester.runAllTests();
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runNodeTests().catch(console.error);
}
