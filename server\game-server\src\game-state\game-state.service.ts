import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';
import { Interval } from '@nestjs/schedule';
import type { 
  GameStateUpdate,
  GameStateSnapshot,
  PlayerState,
  GameObjectState,
  EnvironmentState,
  SyncConfig,
  ConflictResolutionStrategy,
  StateConflict,
  SyncStatistics,
  GameStateType,
  SyncPriority,
  Vector3
 } from './game-state.interface';

@Injectable()
export class GameStateService {
  private readonly logger = new Logger(GameStateService.name);
  private readonly roomStates = new Map<string, GameStateSnapshot>();
  private readonly pendingUpdates = new Map<string, GameStateUpdate[]>();
  private readonly conflicts = new Map<string, StateConflict[]>();
  private readonly statistics = new Map<string, SyncStatistics>();
  
  private readonly syncConfig: SyncConfig = {
    maxUpdatesPerSecond: 60,
    interpolationEnabled: true,
    predictionEnabled: true,
    compressionEnabled: true,
    deltaCompressionEnabled: true,
    priorityThresholds: {
      low: 100,
      normal: 50,
      high: 20,
      critical: 5
    }
  };

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
  ) {
    // 从配置文件加载同步配置
    this.loadSyncConfig();
  }

  /**
   * 初始化房间游戏状态
   */
  initializeRoomState(roomId: string): GameStateSnapshot {
    const initialState: GameStateSnapshot = {
      roomId,
      timestamp: Date.now(),
      version: 1,
      players: {},
      gameObjects: {},
      environment: {
        weather: 'clear',
        timeOfDay: 12,
        lighting: {
          ambientColor: '#ffffff',
          directionalLight: {
            direction: { x: 0, y: -1, z: 0 },
            color: '#ffffff',
            intensity: 1.0
          },
          shadows: true
        },
        physics: {
          gravity: { x: 0, y: -9.81, z: 0 },
          timeScale: 1.0,
          collisionEnabled: true
        },
        lastUpdate: Date.now()
      },
      metadata: {}
    };

    this.roomStates.set(roomId, initialState);
    this.pendingUpdates.set(roomId, []);
    this.conflicts.set(roomId, []);
    this.initializeStatistics(roomId);

    this.logger.log(`房间 ${roomId} 游戏状态已初始化`);
    return initialState;
  }

  /**
   * 添加玩家到游戏状态
   */
  addPlayer(roomId: string, playerId: string, initialPosition?: Vector3): void {
    const state = this.roomStates.get(roomId);
    if (!state) {
      this.logger.error(`房间 ${roomId} 状态不存在`);
      return;
    }

    const playerState: PlayerState = {
      id: playerId,
      position: initialPosition || { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      velocity: { x: 0, y: 0, z: 0 },
      health: 100,
      score: 0,
      inventory: [],
      status: 'active',
      lastUpdate: Date.now(),
      metadata: {}
    };

    state.players[playerId] = playerState;
    state.version++;
    state.timestamp = Date.now();

    this.logger.log(`玩家 ${playerId} 已添加到房间 ${roomId} 游戏状态`);
  }

  /**
   * 移除玩家从游戏状态
   */
  removePlayer(roomId: string, playerId: string): void {
    const state = this.roomStates.get(roomId);
    if (!state) {
      return;
    }

    delete state.players[playerId];
    state.version++;
    state.timestamp = Date.now();

    this.logger.log(`玩家 ${playerId} 已从房间 ${roomId} 游戏状态移除`);
  }

  /**
   * 应用游戏状态更新
   */
  applyStateUpdate(roomId: string, update: GameStateUpdate): boolean {
    const state = this.roomStates.get(roomId);
    if (!state) {
      this.logger.error(`房间 ${roomId} 状态不存在`);
      return false;
    }

    // 检查冲突
    const conflict = this.detectConflict(roomId, update);
    if (conflict) {
      this.handleConflict(roomId, conflict);
      return false;
    }

    // 应用更新
    const success = this.applyUpdate(state, update);
    if (success) {
      state.version++;
      state.timestamp = Date.now();
      
      // 更新统计信息
      this.updateStatistics(roomId, update);
      
      // 触发状态更新事件
      this.eventEmitter.emit('game-state.updated', {
        roomId,
        update,
        state
      });
    }

    return success;
  }

  /**
   * 获取房间游戏状态
   */
  getRoomState(roomId: string): GameStateSnapshot | undefined {
    return this.roomStates.get(roomId);
  }

  /**
   * 获取玩家状态
   */
  getPlayerState(roomId: string, playerId: string): PlayerState | undefined {
    const state = this.roomStates.get(roomId);
    return state?.players[playerId];
  }

  /**
   * 批量应用状态更新
   */
  applyBatchUpdates(roomId: string, updates: GameStateUpdate[]): number {
    let successCount = 0;
    
    // 按优先级排序
    updates.sort((a, b) => this.getPriorityValue(a.priority) - this.getPriorityValue(b.priority));
    
    for (const update of updates) {
      if (this.applyStateUpdate(roomId, update)) {
        successCount++;
      }
    }
    
    return successCount;
  }

  /**
   * 创建状态快照
   */
  createSnapshot(roomId: string): GameStateSnapshot | undefined {
    const state = this.roomStates.get(roomId);
    if (!state) {
      return undefined;
    }

    // 深拷贝状态
    return JSON.parse(JSON.stringify(state));
  }

  /**
   * 恢复状态快照
   */
  restoreSnapshot(snapshot: GameStateSnapshot): void {
    this.roomStates.set(snapshot.roomId, snapshot);
    this.logger.log(`房间 ${snapshot.roomId} 状态已恢复到版本 ${snapshot.version}`);
  }

  /**
   * 清理房间状态
   */
  cleanupRoomState(roomId: string): void {
    this.roomStates.delete(roomId);
    this.pendingUpdates.delete(roomId);
    this.conflicts.delete(roomId);
    this.statistics.delete(roomId);
    
    this.logger.log(`房间 ${roomId} 游戏状态已清理`);
  }

  /**
   * 获取同步统计信息
   */
  getSyncStatistics(roomId: string): SyncStatistics | undefined {
    return this.statistics.get(roomId);
  }

  /**
   * 定时同步处理
   */
  @Interval(16) // 60 FPS
  handlePeriodicSync() {
    for (const [roomId, updates] of this.pendingUpdates.entries()) {
      if (updates.length > 0) {
        this.processPendingUpdates(roomId, updates);
      }
    }
  }

  // 私有方法

  private loadSyncConfig(): void {
    // 从配置服务加载同步配置
    this.syncConfig.maxUpdatesPerSecond = this.configService.get<number>('SYNC_MAX_UPDATES_PER_SECOND', 60);
    this.syncConfig.interpolationEnabled = this.configService.get<boolean>('SYNC_INTERPOLATION_ENABLED', true);
    this.syncConfig.predictionEnabled = this.configService.get<boolean>('SYNC_PREDICTION_ENABLED', true);
  }

  private initializeStatistics(roomId: string): void {
    const stats: SyncStatistics = {
      totalUpdates: 0,
      updatesPerSecond: 0,
      averageLatency: 0,
      packetLoss: 0,
      bandwidth: 0,
      conflicts: 0,
      resolvedConflicts: 0,
      lastUpdate: Date.now()
    };
    
    this.statistics.set(roomId, stats);
  }

  private detectConflict(roomId: string, update: GameStateUpdate): StateConflict | null {
    // 简化的冲突检测逻辑
    // 实际实现应该更复杂，考虑时间戳、优先级等因素
    return null;
  }

  private handleConflict(roomId: string, conflict: StateConflict): void {
    // 冲突解决逻辑
    this.logger.warn(`检测到状态冲突: ${conflict.id}`);
    
    const conflicts = this.conflicts.get(roomId) || [];
    conflicts.push(conflict);
    this.conflicts.set(roomId, conflicts);
  }

  private applyUpdate(state: GameStateSnapshot, update: GameStateUpdate): boolean {
    try {
      switch (update.type) {
        case GameStateType.PLAYER_POSITION:
          return this.updatePlayerPosition(state, update);
        case GameStateType.PLAYER_ACTION:
          return this.updatePlayerAction(state, update);
        case GameStateType.GAME_OBJECT:
          return this.updateGameObject(state, update);
        case GameStateType.ENVIRONMENT:
          return this.updateEnvironment(state, update);
        case GameStateType.SCORE:
          return this.updateScore(state, update);
        default:
          this.logger.warn(`未知的状态更新类型: ${update.type}`);
          return false;
      }
    } catch (error) {
      this.logger.error(`应用状态更新失败: ${error.message}`, error.stack);
      return false;
    }
  }

  private updatePlayerPosition(state: GameStateSnapshot, update: GameStateUpdate): boolean {
    if (!update.playerId) return false;
    
    const player = state.players[update.playerId];
    if (!player) return false;
    
    if (update.data.position) {
      player.position = update.data.position;
    }
    if (update.data.rotation) {
      player.rotation = update.data.rotation;
    }
    if (update.data.velocity) {
      player.velocity = update.data.velocity;
    }
    
    player.lastUpdate = update.timestamp;
    return true;
  }

  private updatePlayerAction(state: GameStateSnapshot, update: GameStateUpdate): boolean {
    if (!update.playerId) return false;
    
    const player = state.players[update.playerId];
    if (!player) return false;
    
    // 处理玩家动作
    if (update.data.action) {
      player.metadata.lastAction = update.data.action;
      player.lastUpdate = update.timestamp;
    }
    
    return true;
  }

  private updateGameObject(state: GameStateSnapshot, update: GameStateUpdate): boolean {
    if (!update.objectId) return false;
    
    let gameObject = state.gameObjects[update.objectId];
    if (!gameObject) {
      // 创建新的游戏对象
      gameObject = {
        id: update.objectId,
        type: update.data.type || 'unknown',
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
        properties: {},
        lastUpdate: update.timestamp
      };
      state.gameObjects[update.objectId] = gameObject;
    }
    
    // 更新游戏对象属性
    Object.assign(gameObject, update.data);
    gameObject.lastUpdate = update.timestamp;
    
    return true;
  }

  private updateEnvironment(state: GameStateSnapshot, update: GameStateUpdate): boolean {
    Object.assign(state.environment, update.data);
    state.environment.lastUpdate = update.timestamp;
    return true;
  }

  private updateScore(state: GameStateSnapshot, update: GameStateUpdate): boolean {
    if (!update.playerId) return false;
    
    const player = state.players[update.playerId];
    if (!player) return false;
    
    if (typeof update.data.score === 'number') {
      player.score = update.data.score;
      player.lastUpdate = update.timestamp;
    }
    
    return true;
  }

  private updateStatistics(roomId: string, update: GameStateUpdate): void {
    const stats = this.statistics.get(roomId);
    if (!stats) return;
    
    stats.totalUpdates++;
    stats.lastUpdate = Date.now();
    
    // 计算每秒更新数
    const timeDiff = (Date.now() - stats.lastUpdate) / 1000;
    if (timeDiff > 0) {
      stats.updatesPerSecond = stats.totalUpdates / timeDiff;
    }
  }

  private getPriorityValue(priority: SyncPriority): number {
    switch (priority) {
      case SyncPriority.CRITICAL: return 1;
      case SyncPriority.HIGH: return 2;
      case SyncPriority.NORMAL: return 3;
      case SyncPriority.LOW: return 4;
      default: return 3;
    }
  }

  private processPendingUpdates(roomId: string, updates: GameStateUpdate[]): void {
    // 处理待处理的更新
    const maxUpdates = Math.min(updates.length, this.syncConfig.maxUpdatesPerSecond / 60);
    const processedUpdates = updates.splice(0, maxUpdates);
    
    this.applyBatchUpdates(roomId, processedUpdates);
  }
}
