import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import type {  Document, Types  } from 'mongoose';

export type UIConfigDocument = UIConfig & Document;

/**
 * UI配置类型
 */
export enum ConfigType {
  GLOBAL = 'global',           // 全局配置
  USER = 'user',              // 用户配置
  PROJECT = 'project',        // 项目配置
  TEMPLATE = 'template',      // 模板配置
  COMPONENT = 'component',    // 组件配置
}

/**
 * UI配置范围
 */
export enum ConfigScope {
  SYSTEM = 'system',          // 系统级
  ORGANIZATION = 'organization', // 组织级
  USER = 'user',              // 用户级
  SESSION = 'session',        // 会话级
}

/**
 * UI配置数据结构
 */
export interface ConfigData {
  [key: string]: any;
}

@Schema({
  timestamps: true,
  collection: 'ui_configs',
})
export class UIConfig {
  @Prop({ required: true, maxlength: 100 })
  name: string;

  @Prop({ maxlength: 500 })
  description?: string;

  @Prop({ 
    type: String, 
    enum: Object.values(ConfigType),
    required: true,
    index: true 
  })
  type: ConfigType;

  @Prop({ 
    type: String, 
    enum: Object.values(ConfigScope),
    required: true,
    index: true 
  })
  scope: ConfigScope;

  @Prop({ type: Types.ObjectId, index: true })
  userId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, index: true })
  organizationId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, index: true })
  projectId?: Types.ObjectId;

  @Prop({ type: Object, required: true })
  data: ConfigData;

  @Prop({ type: Object })
  metadata?: {
    version?: string;
    tags?: string[];
    category?: string;
    priority?: number;
    readonly?: boolean;
    encrypted?: boolean;
  };

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ type: Date })
  expiresAt?: Date;

  @Prop({ type: Types.ObjectId, required: true })
  createdBy: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true })
  updatedBy: Types.ObjectId;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const UIConfigSchema = SchemaFactory.createForClass(UIConfig);

// 创建复合索引
UIConfigSchema.index({ type: 1, scope: 1, userId: 1 });
UIConfigSchema.index({ name: 1, type: 1, scope: 1 }, { unique: true });
UIConfigSchema.index({ organizationId: 1, type: 1 });
UIConfigSchema.index({ projectId: 1, type: 1 });
UIConfigSchema.index({ isActive: 1, expiresAt: 1 });

// 添加实例方法
UIConfigSchema.methods.isExpired = function(): boolean {
  return this.expiresAt && this.expiresAt < new Date();
};

UIConfigSchema.methods.canAccess = function(userId: Types.ObjectId, organizationId?: Types.ObjectId): boolean {
  if (this.scope === ConfigScope.SYSTEM) {
    return true;
  }
  
  if (this.scope === ConfigScope.ORGANIZATION) {
    return this.organizationId?.equals(organizationId);
  }
  
  if (this.scope === ConfigScope.USER) {
    return this.userId?.equals(userId);
  }
  
  return false;
};

UIConfigSchema.methods.canEdit = function(userId: Types.ObjectId): boolean {
  if (this.metadata?.readonly) {
    return false;
  }
  
  return this.createdBy.equals(userId) || this.scope === ConfigScope.USER;
};
