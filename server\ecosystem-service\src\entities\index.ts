// 导入实体类
import type {  Partner, PartnerType, PartnerTier, PartnerStatus  } from './partner.entity';
import { PartnerCertification, CertificationStatus } from './partner-certification.entity';
import type {  PartnerAgreement, AgreementType, AgreementStatus  } from './partner-agreement.entity';
import type {  ApiSpecification, APIType, APIStatus  } from './api-specification.entity';
import { ApiUsageRecord } from './api-usage-record.entity';
import type {  ThirdPartyApplication, ApplicationType, ApplicationStatus, PricingType  } from './third-party-application.entity';
import { ApplicationReview } from './application-review.entity';
import { IndustryStandard, StandardStatus } from './industry-standard.entity';
import { StandardCertification, CertificationApplicationStatus } from './standard-certification.entity';
import { ComplianceRecord } from './compliance-record.entity';

// 导出实体类和枚举
export { Partner, PartnerType, PartnerTier, PartnerStatus };
export { PartnerCertification, CertificationStatus };
export { PartnerAgreement, AgreementType, AgreementStatus };
export { ApiSpecification, APIType, APIStatus };
export { ApiUsageRecord };
export { ThirdPartyApplication, ApplicationType, ApplicationStatus, PricingType };
export { ApplicationReview };
export { IndustryStandard, StandardStatus };
export { StandardCertification, CertificationApplicationStatus };
export { ComplianceRecord };

// 所有实体的数组，用于TypeORM配置
export const entities = [
  // 合作伙伴相关
  Partner,
  PartnerCertification,
  PartnerAgreement,

  // API相关
  ApiSpecification,
  ApiUsageRecord,

  // 第三方应用相关
  ThirdPartyApplication,
  ApplicationReview,

  // 行业标准相关
  IndustryStandard,
  StandardCertification,
  ComplianceRecord,
];
