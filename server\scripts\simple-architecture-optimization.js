#!/usr/bin/env node

/**
 * DL引擎微服务项目架构优化脚本 - 简化版
 */

const fs = require('fs');
const path = require('path');

class SimpleArchitectureOptimizer {
  constructor() {
    this.serverDir = path.join(__dirname, '..');
    this.services = this.getServiceDirectories();
    this.report = {
      timestamp: new Date().toISOString(),
      processed: 0,
      total: 0,
      tasks: {
        sharedComponents: false,
        communicationProtocol: false,
        errorHandling: false,
        logging: false,
        monitoring: false
      }
    };
  }

  getServiceDirectories() {
    const items = fs.readdirSync(this.serverDir);
    return items.filter(item => {
      const itemPath = path.join(this.serverDir, item);
      const stat = fs.statSync(itemPath);
      return stat.isDirectory() && 
             !['node_modules', 'dist', 'scripts', 'docs', 'shared', 'database', 'src'].includes(item) &&
             !item.startsWith('.') &&
             !item.endsWith('-reports');
    });
  }

  async run() {
    console.log('🏗️ 开始架构优化...\n');
    
    this.report.total = this.services.length;

    try {
      // 创建共享组件
      await this.createSharedComponents();
      
      // 创建监控配置
      await this.createMonitoringConfig();
      
      // 为每个服务创建标准文件
      await this.processServices();

      this.generateReport();
      this.printSummary();
      
    } catch (error) {
      console.error('❌ 架构优化失败:', error.message);
    }
  }

  async createSharedComponents() {
    console.log('🔧 创建共享架构组件...');
    
    // 创建协议目录
    const protocolDir = path.join(this.serverDir, 'shared', 'protocols');
    if (!fs.existsSync(protocolDir)) {
      fs.mkdirSync(protocolDir, { recursive: true });
    }

    // 创建HTTP协议文件
    const httpProtocol = `// HTTP通信协议标准
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: any;
  timestamp: string;
  requestId: string;
}

export class HttpProtocol {
  static success<T>(data: T, message?: string) {
    return {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
      requestId: 'req_' + Date.now()
    };
  }

  static error(error: any) {
    return {
      success: false,
      error,
      timestamp: new Date().toISOString(),
      requestId: 'req_' + Date.now()
    };
  }
}`;

    fs.writeFileSync(path.join(protocolDir, 'http-protocol.ts'), httpProtocol);
    this.report.tasks.communicationProtocol = true;

    // 创建错误处理
    const errorDir = path.join(this.serverDir, 'shared', 'errors');
    if (!fs.existsSync(errorDir)) {
      fs.mkdirSync(errorDir, { recursive: true });
    }

    const errorHandler = `// 统一错误处理
export enum ErrorCode {
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  BAD_REQUEST = 'BAD_REQUEST',
  UNAUTHORIZED = 'UNAUTHORIZED',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR'
}

export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;

  constructor(code: ErrorCode, message: string, statusCode: number = 500) {
    super(message);
    this.code = code;
    this.statusCode = statusCode;
  }
}

export class ErrorHandler {
  static handle(error: Error) {
    if (error instanceof AppError) {
      return {
        code: error.code,
        message: error.message,
        statusCode: error.statusCode
      };
    }

    return {
      code: ErrorCode.INTERNAL_SERVER_ERROR,
      message: 'Internal server error',
      statusCode: 500
    };
  }
}`;

    fs.writeFileSync(path.join(errorDir, 'error-handler.ts'), errorHandler);
    this.report.tasks.errorHandling = true;

    // 创建日志系统
    const loggingDir = path.join(this.serverDir, 'shared', 'logging');
    if (!fs.existsSync(loggingDir)) {
      fs.mkdirSync(loggingDir, { recursive: true });
    }

    const logger = `// 统一日志格式
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug'
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  service: string;
  message: string;
  context?: any;
}

export class Logger {
  private serviceName: string;

  constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  private log(level: LogLevel, message: string, context?: any): void {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      service: this.serviceName,
      message,
      context
    };

    console.log(JSON.stringify(entry));
  }

  error(message: string, context?: any): void {
    this.log(LogLevel.ERROR, message, context);
  }

  warn(message: string, context?: any): void {
    this.log(LogLevel.WARN, message, context);
  }

  info(message: string, context?: any): void {
    this.log(LogLevel.INFO, message, context);
  }

  debug(message: string, context?: any): void {
    this.log(LogLevel.DEBUG, message, context);
  }
}

export const createLogger = (serviceName: string): Logger => {
  return new Logger(serviceName);
};`;

    fs.writeFileSync(path.join(loggingDir, 'logger.ts'), logger);
    this.report.tasks.logging = true;
    this.report.tasks.sharedComponents = true;

    console.log('✅ 共享架构组件创建完成');
  }

  async createMonitoringConfig() {
    console.log('📊 创建监控配置...');
    
    const monitoringDir = path.join(this.serverDir, 'shared', 'monitoring');
    if (!fs.existsSync(monitoringDir)) {
      fs.mkdirSync(monitoringDir, { recursive: true });
    }

    // Prometheus配置
    const prometheusConfig = `# Prometheus配置
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'dl-engine-services'
    static_configs:
      - targets: ['localhost:3000', 'localhost:3001', 'localhost:3002']
    metrics_path: /metrics
    scrape_interval: 5s`;

    fs.writeFileSync(path.join(monitoringDir, 'prometheus.yml'), prometheusConfig);

    // 告警规则
    const alertRules = `groups:
  - name: dl-engine-alerts
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"`;

    fs.writeFileSync(path.join(monitoringDir, 'alert_rules.yml'), alertRules);
    this.report.tasks.monitoring = true;

    console.log('✅ 监控配置创建完成');
  }

  async processServices() {
    console.log('📦 处理微服务...');
    
    for (const service of this.services) {
      try {
        await this.processService(service);
        this.report.processed++;
      } catch (error) {
        console.error(`❌ 处理服务 ${service} 失败:`, error.message);
      }
    }
  }

  async processService(serviceName) {
    const servicePath = path.join(this.serverDir, serviceName);
    
    // 创建全局错误过滤器
    const filtersDir = path.join(servicePath, 'src', 'common', 'filters');
    if (!fs.existsSync(filtersDir)) {
      fs.mkdirSync(filtersDir, { recursive: true });
    }

    const globalErrorFilter = `import { ExceptionFilter, Catch, ArgumentsHost, HttpException } from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const status = exception instanceof HttpException ? exception.getStatus() : 500;
    const message = exception instanceof HttpException ? exception.message : 'Internal server error';

    response.status(status).json({
      success: false,
      error: {
        message,
        statusCode: status,
        timestamp: new Date().toISOString(),
        path: request.url
      }
    });
  }
}`;

    fs.writeFileSync(path.join(filtersDir, 'global-exception.filter.ts'), globalErrorFilter);

    // 创建日志拦截器
    const interceptorsDir = path.join(servicePath, 'src', 'common', 'interceptors');
    if (!fs.existsSync(interceptorsDir)) {
      fs.mkdirSync(interceptorsDir, { recursive: true });
    }

    const loggingInterceptor = `import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url } = request;
    const startTime = Date.now();

    console.log(\`[\${new Date().toISOString()}] \${method} \${url} - Start\`);

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - startTime;
        console.log(\`[\${new Date().toISOString()}] \${method} \${url} - Completed in \${duration}ms\`);
      })
    );
  }
}`;

    fs.writeFileSync(path.join(interceptorsDir, 'logging.interceptor.ts'), loggingInterceptor);
  }

  generateReport() {
    const reportPath = path.join(this.serverDir, 'cleanup-reports', 
      `architecture-optimization-report-${new Date().toISOString().replace(/[:.]/g, '-')}.json`);
    
    const reportDir = path.dirname(reportPath);
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(this.report, null, 2));
    console.log(`📊 报告已生成: ${reportPath}`);
  }

  printSummary() {
    console.log('\n📋 架构优化总结');
    console.log('='.repeat(50));
    console.log(`总服务数: ${this.report.total}`);
    console.log(`已处理: ${this.report.processed}`);
    console.log(`成功率: ${((this.report.processed / this.report.total) * 100).toFixed(1)}%`);
    
    console.log('\n✅ 架构优化完成！');
    console.log('\n📝 已完成的任务:');
    console.log('✅ 创建共享通信协议');
    console.log('✅ 统一错误处理机制');
    console.log('✅ 标准化日志格式');
    console.log('✅ 配置监控告警');
    console.log('✅ 为所有服务添加过滤器和拦截器');
  }
}

// 执行优化
if (require.main === module) {
  const optimizer = new SimpleArchitectureOptimizer();
  optimizer.run().catch(console.error);
}

module.exports = SimpleArchitectureOptimizer;
