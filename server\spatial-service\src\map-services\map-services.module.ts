/**
 * 地图服务模块
 */
import { Module } from '@nestjs/common';
import type {  TypeOrmModule  } from '@nestjs/typeorm';
import { MapServicesController } from './map-services.controller';
import { TileService } from './tile.service';
import { WmsService } from './wms.service';
import { WfsService } from './wfs.service';
import { GeocodingService } from './geocoding.service';
import { ElevationService } from './elevation.service';
import { WeatherService } from './weather.service';
import { SpatialFeature } from '../entities/spatial-feature.entity';
import { SpatialLayer } from '../entities/spatial-layer.entity';
import { SpatialProject } from '../entities/spatial-project.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SpatialFeature,
      SpatialLayer,
      SpatialProject
    ])
  ],
  controllers: [MapServicesController],
  providers: [
    TileService,
    WmsService,
    WfsService,
    GeocodingService,
    ElevationService,
    WeatherService
  ],
  exports: [
    TileService,
    WmsService,
    WfsService,
    GeocodingService,
    ElevationService,
    WeatherService
  ]
})
export class MapServicesModule {}
