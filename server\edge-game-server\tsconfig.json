{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": true, "paths": {"@/*": ["src/*"], "@/common/*": ["src/common/*"], "@/config/*": ["src/config/*"], "@/controllers/*": ["src/controllers/*"], "@/dto/*": ["src/dto/*"], "@/entities/*": ["src/entities/*"], "@/interfaces/*": ["src/interfaces/*"], "@/modules/*": ["src/modules/*"], "@/services/*": ["src/services/*"], "@/utils/*": ["src/utils/*"]}, "lib": ["ES2020"], "esModuleInterop": true, "resolveJsonModule": true, "moduleResolution": "node", "strict": true, "strictFunctionTypes": true, "noImplicitReturns": true}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist", "coverage", "**/*.spec.ts", "**/*.e2e-spec.ts"], "extends": "../tsconfig.base.json"}