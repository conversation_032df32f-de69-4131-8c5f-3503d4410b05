import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import type {  Types  } from 'mongoose';
import { UIComponentService } from './ui-component.service';
import { CreateUIComponentDto, UpdateUIComponentDto, QueryUIComponentDto } from './dto/ui-component.dto';

@ApiTags('ui-component')
@Controller('ui-component')
// @UseGuards(JwtAuthGuard) // 需要实现认证守卫
export class UIComponentController {
  constructor(private readonly componentService: UIComponentService) {}

  @Post()
  @ApiOperation({ summary: '创建UI组件' })
  @ApiResponse({ status: HttpStatus.CREATED, description: '组件创建成功' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: '请求参数错误' })
  @ApiBearerAuth()
  async create(@Body() createComponentDto: CreateUIComponentDto, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.componentService.create(createComponentDto, userId);
  }

  @Get()
  @ApiOperation({ summary: '获取UI组件列表' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiBearerAuth()
  async findAll(@Query() queryDto: QueryUIComponentDto, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.componentService.findAll(queryDto, userId);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取单个UI组件' })
  @ApiParam({ name: 'id', description: '组件ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '组件不存在' })
  @ApiBearerAuth()
  async findOne(@Param('id') id: string, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.componentService.findOne(id, userId);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新UI组件' })
  @ApiParam({ name: 'id', description: '组件ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '更新成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '组件不存在' })
  @ApiBearerAuth()
  async update(
    @Param('id') id: string,
    @Body() updateComponentDto: UpdateUIComponentDto,
    @Request() req: any,
  ) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.componentService.update(id, updateComponentDto, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除UI组件' })
  @ApiParam({ name: 'id', description: '组件ID' })
  @ApiResponse({ status: HttpStatus.NO_CONTENT, description: '删除成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '组件不存在' })
  @ApiBearerAuth()
  async remove(@Param('id') id: string, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    await this.componentService.remove(id, userId);
  }

  @Post(':id/download')
  @ApiOperation({ summary: '下载UI组件' })
  @ApiParam({ name: 'id', description: '组件ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '下载成功' })
  @ApiBearerAuth()
  async download(@Param('id') id: string, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.componentService.download(id, userId);
  }

  @Post(':id/publish')
  @ApiOperation({ summary: '发布UI组件' })
  @ApiResponse({ status: HttpStatus.OK, description: '发布成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '组件不存在' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: '发布验证失败' })
  @ApiParam({ name: 'id', description: '组件ID' })
  @ApiBearerAuth()
  async publish(@Param('id') id: string, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.componentService.publishComponent(id, userId);
  }

  @Get(':id/dependencies')
  @ApiOperation({ summary: '获取组件依赖' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '组件不存在' })
  @ApiParam({ name: 'id', description: '组件ID' })
  @ApiBearerAuth()
  async getDependencies(@Param('id') id: string, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.componentService.getComponentDependencies(id, userId);
  }

  @Get(':id/usages')
  @ApiOperation({ summary: '获取组件使用者' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '组件不存在' })
  @ApiParam({ name: 'id', description: '组件ID' })
  @ApiBearerAuth()
  async getUsages(@Param('id') id: string, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.componentService.getComponentUsages(id, userId);
  }

  @Post(':id/duplicate')
  @ApiOperation({ summary: '复制组件' })
  @ApiResponse({ status: HttpStatus.CREATED, description: '复制成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '组件不存在' })
  @ApiParam({ name: 'id', description: '组件ID' })
  @ApiBearerAuth()
  async duplicate(
    @Param('id') id: string,
    @Body() body: { newName?: string },
    @Request() req: any
  ) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.componentService.duplicateComponent(id, userId, body.newName);
  }

  @Get(':id/stats')
  @ApiOperation({ summary: '获取组件统计信息' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: '组件不存在' })
  @ApiParam({ name: 'id', description: '组件ID' })
  @ApiBearerAuth()
  async getStats(@Param('id') id: string, @Request() req: any) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    return this.componentService.getComponentStats(id, userId);
  }

  @Get('search/library')
  @ApiOperation({ summary: '搜索组件库' })
  @ApiResponse({ status: HttpStatus.OK, description: '搜索成功' })
  @ApiBearerAuth()
  async searchLibrary(
    @Query('q') query: string,
    @Query('type') type?: string,
    @Query('tags') tags?: string,
    @Request() req?: any
  ) {
    const userId = new Types.ObjectId(req.user?.id || '000000000000000000000000');
    const filters: any = {};
    if (type) filters.type = type;
    if (tags) filters.tags = tags.split(',');

    return this.componentService.searchComponentLibrary(query, filters, userId);
  }
}
