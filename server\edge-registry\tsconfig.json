{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": true, "paths": {"@/*": ["src/*"], "@/controllers/*": ["src/controllers/*"], "@/services/*": ["src/services/*"], "@/entities/*": ["src/entities/*"], "@/dto/*": ["src/dto/*"], "@/config/*": ["src/config/*"], "@/guards/*": ["src/guards/*"], "@/interceptors/*": ["src/interceptors/*"], "@/decorators/*": ["src/decorators/*"], "@/utils/*": ["src/utils/*"]}, "strict": true, "strictFunctionTypes": true, "noImplicitReturns": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test"], "extends": "../tsconfig.base.json"}