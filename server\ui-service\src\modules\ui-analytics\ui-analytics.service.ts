import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import type {  Model, Types  } from 'mongoose';
import type {  UIAnalytics, UIAnalyticsDocument, EventType, ResourceType  } from './schemas/ui-analytics.schema';

@Injectable()
export class UIAnalyticsService {
  constructor(
    @InjectModel(UIAnalytics.name) private analyticsModel: Model<UIAnalyticsDocument>,
  ) {}

  /**
   * 记录事件
   */
  async recordEvent(
    eventType: EventType,
    resourceType: ResourceType,
    resourceId: Types.ObjectId,
    userId?: Types.ObjectId,
    organizationId?: Types.ObjectId,
    sessionId?: string,
    metadata?: any,
  ): Promise<UIAnalytics> {
    const event = new this.analyticsModel({
      eventType,
      resourceType,
      resourceId,
      userId,
      organizationId,
      sessionId,
      metadata,
    });

    return event.save();
  }

  /**
   * 获取资源统计
   */
  async getResourceStats(resourceId: Types.ObjectId, days = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const stats = await this.analyticsModel.aggregate([
      {
        $match: {
          resourceId,
          timestamp: { $gte: startDate },
        },
      },
      {
        $group: {
          _id: '$eventType',
          count: { $sum: 1 },
        },
      },
    ]);

    return stats.reduce((acc, stat) => {
      acc[stat._id] = stat.count;
      return acc;
    }, {});
  }

  /**
   * 获取热门资源
   */
  async getPopularResources(resourceType: ResourceType, limit = 10, days = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return this.analyticsModel.aggregate([
      {
        $match: {
          resourceType,
          eventType: EventType.VIEW,
          timestamp: { $gte: startDate },
        },
      },
      {
        $group: {
          _id: '$resourceId',
          views: { $sum: 1 },
          uniqueUsers: { $addToSet: '$userId' },
        },
      },
      {
        $project: {
          resourceId: '$_id',
          views: 1,
          uniqueUsers: { $size: '$uniqueUsers' },
        },
      },
      {
        $sort: { views: -1 },
      },
      {
        $limit: limit,
      },
    ]);
  }
}
