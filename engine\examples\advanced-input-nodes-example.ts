/**
 * 高级输入节点示例
 * 演示如何使用批次1.4的18个输入系统增强节点
 */
import {
  Engine,
  World,
  Scene,
  Camera,
  Renderer,
  MultiTouchNode,
  GestureRecognitionNode,
  VoiceInputNode,
  MotionSensorNode,
  AccelerometerNode,
  GyroscopeNode,
  CompassNode,
  ProximityNode,
  LightSensorNode,
  PressureSensorNode,
  VRControllerInputNode,
  VRHeadsetTrackingNode,
  ARTouchInputNode,
  ARGestureInputNode,
  SpatialInputNode,
  EyeTrackingInputNode,
  HandTrackingInputNode,
  VoiceCommandInputNode
} from '../src';

// 创建引擎
const engine = new Engine();

// 创建世界
const world = new World();
engine.setWorld(world);

// 创建场景
const scene = new Scene();
world.setScene(scene);

// 创建相机
const camera = new Camera();
scene.add(camera);

// 创建渲染器
const renderer = new Renderer();

/**
 * 高级输入节点示例
 */
function demonstrateAdvancedInputNodes() {
  console.log('=== 高级输入节点示例 ===');

  // 1. 多点触控节点
  const multiTouchNode = new MultiTouchNode();
  const touchResult = multiTouchNode.execute({
    enable: true,
    maxTouches: 5,
    minPressure: 0.2
  });
  console.log('多点触控结果:', {
    touchCount: touchResult.touchCount,
    averagePosition: touchResult.averagePosition,
    averagePressure: touchResult.averagePressure
  });

  // 2. 手势识别节点
  const gestureNode = new GestureRecognitionNode();
  const gestureResult = gestureNode.execute({
    enable: true,
    sensitivity: 1.2,
    minConfidence: 0.8
  });
  console.log('手势识别结果:', {
    gestureType: gestureResult.gestureType,
    confidence: gestureResult.confidence,
    onGestureDetected: gestureResult.onGestureDetected
  });

  // 3. 语音输入节点
  const voiceNode = new VoiceInputNode();
  const voiceResult = voiceNode.execute({
    enable: true,
    language: 'zh-CN',
    commands: ['开始', '停止', '确认', '取消']
  });
  console.log('语音输入结果:', {
    transcript: voiceResult.transcript,
    confidence: voiceResult.confidence,
    matchedCommand: voiceResult.matchedCommand
  });

  // 4. 运动传感器节点
  const motionNode = new MotionSensorNode();
  const motionResult = motionNode.execute({
    enable: true,
    sensitivity: 1.0,
    smoothing: 0.1
  });
  console.log('运动传感器结果:', {
    acceleration: motionResult.acceleration,
    magnitude: motionResult.magnitude,
    onMotion: motionResult.onMotion
  });
}

/**
 * 传感器输入节点示例
 */
function demonstrateSensorInputNodes() {
  console.log('=== 传感器输入节点示例 ===');

  // 1. 加速度计节点
  const accelerometerNode = new AccelerometerNode();
  const accelResult = accelerometerNode.execute({
    enable: true,
    sensitivity: 1.0,
    threshold: 0.5
  });
  console.log('加速度计结果:', {
    acceleration: accelResult.acceleration,
    magnitude: accelResult.magnitude,
    onShake: accelResult.onShake
  });

  // 2. 陀螺仪节点
  const gyroscopeNode = new GyroscopeNode();
  const gyroResult = gyroscopeNode.execute({
    enable: true,
    sensitivity: 1.0
  });
  console.log('陀螺仪结果:', {
    rotation: gyroResult.rotation,
    alpha: gyroResult.alpha,
    beta: gyroResult.beta,
    gamma: gyroResult.gamma
  });

  // 3. 指南针节点
  const compassNode = new CompassNode();
  const compassResult = compassNode.execute({
    enable: true,
    magneticDeclination: 5.0
  });
  console.log('指南针结果:', {
    heading: compassResult.heading,
    magneticHeading: compassResult.magneticHeading,
    trueHeading: compassResult.trueHeading
  });

  // 4. 距离传感器节点
  const proximityNode = new ProximityNode();
  const proximityResult = proximityNode.execute({
    enable: true,
    threshold: 3.0
  });
  console.log('距离传感器结果:', {
    distance: proximityResult.distance,
    isNear: proximityResult.isNear
  });

  // 5. 光线传感器节点
  const lightNode = new LightSensorNode();
  const lightResult = lightNode.execute({
    enable: true,
    sensitivity: 1.0
  });
  console.log('光线传感器结果:', {
    illuminance: lightResult.illuminance,
    lightLevel: lightResult.lightLevel,
    isDark: lightResult.isDark,
    isBright: lightResult.isBright
  });

  // 6. 压力传感器节点
  const pressureNode = new PressureSensorNode();
  const pressureResult = pressureNode.execute({
    enable: true,
    sensitivity: 1.0
  });
  console.log('压力传感器结果:', {
    pressure: pressureResult.pressure,
    pressureLevel: pressureResult.pressureLevel,
    isLightPress: pressureResult.isLightPress,
    isHeavyPress: pressureResult.isHeavyPress
  });
}

/**
 * VR/AR输入节点示例
 */
function demonstrateVRARInputNodes() {
  console.log('=== VR/AR输入节点示例 ===');

  // 1. VR控制器输入节点
  const vrControllerNode = new VRControllerInputNode();
  const vrControllerResult = vrControllerNode.execute({
    enable: true,
    controllerId: 'right',
    hapticIntensity: 0.5,
    hapticDuration: 200
  });
  console.log('VR控制器结果:', {
    position: vrControllerResult.position,
    connected: vrControllerResult.connected,
    triggerValue: vrControllerResult.triggerValue,
    onTriggerPress: vrControllerResult.onTriggerPress
  });

  // 2. VR头显追踪节点
  const vrHeadsetNode = new VRHeadsetTrackingNode();
  const vrHeadsetResult = vrHeadsetNode.execute({
    enable: true,
    smoothing: 0.1
  });
  console.log('VR头显追踪结果:', {
    position: vrHeadsetResult.position,
    isTracking: vrHeadsetResult.isTracking,
    trackingQuality: vrHeadsetResult.trackingQuality
  });

  // 3. AR触摸输入节点
  const arTouchNode = new ARTouchInputNode();
  const arTouchResult = arTouchNode.execute({
    enable: true,
    raycastDistance: 10,
    touchSensitivity: 1.0
  });
  console.log('AR触摸输入结果:', {
    touchPosition: arTouchResult.touchPosition,
    worldPosition: arTouchResult.worldPosition,
    isTouching: arTouchResult.isTouching,
    onObjectHit: arTouchResult.onObjectHit
  });

  // 4. AR手势输入节点
  const arGestureNode = new ARGestureInputNode();
  const arGestureResult = arGestureNode.execute({
    enable: true,
    gestureTypes: ['tap', 'pinch', 'swipe'],
    minConfidence: 0.8
  });
  console.log('AR手势输入结果:', {
    gestureType: arGestureResult.gestureType,
    confidence: arGestureResult.confidence,
    handPosition: arGestureResult.handPosition,
    onHandDetected: arGestureResult.onHandDetected
  });

  // 5. 空间输入节点
  const spatialNode = new SpatialInputNode();
  const spatialResult = spatialNode.execute({
    enable: true,
    trackingSpace: 'room',
    referenceFrame: 'local'
  });
  console.log('空间输入结果:', {
    position: spatialResult.position,
    velocity: spatialResult.velocity,
    trackingState: spatialResult.trackingState,
    onBoundaryWarning: spatialResult.onBoundaryWarning
  });

  // 6. 眼动追踪输入节点
  const eyeTrackingNode = new EyeTrackingInputNode();
  const eyeTrackingResult = eyeTrackingNode.execute({
    enable: true,
    sensitivity: 1.0,
    smoothing: 0.2
  });
  console.log('眼动追踪结果:', {
    gazePoint: eyeTrackingResult.gazePoint,
    gazeDirection: eyeTrackingResult.gazeDirection,
    trackingQuality: eyeTrackingResult.trackingQuality,
    onBlink: eyeTrackingResult.onBlink
  });

  // 7. 手部追踪输入节点
  const handTrackingNode = new HandTrackingInputNode();
  const handTrackingResult = handTrackingNode.execute({
    enable: true,
    handedness: 'both',
    trackingMode: 'full',
    minConfidence: 0.7
  });
  console.log('手部追踪结果:', {
    leftHandPosition: handTrackingResult.leftHandPosition,
    rightHandPosition: handTrackingResult.rightHandPosition,
    leftHandGesture: handTrackingResult.leftHandGesture,
    rightHandGesture: handTrackingResult.rightHandGesture,
    onHandDetected: handTrackingResult.onHandDetected
  });

  // 8. 语音命令输入节点
  const voiceCommandNode = new VoiceCommandInputNode();
  const voiceCommandResult = voiceCommandNode.execute({
    enable: true,
    language: 'zh-CN',
    commands: ['开始游戏', '暂停', '退出', '帮助'],
    wakeWord: '你好'
  });
  console.log('语音命令结果:', {
    recognizedText: voiceCommandResult.recognizedText,
    matchedCommand: voiceCommandResult.matchedCommand,
    isWakeWordDetected: voiceCommandResult.isWakeWordDetected,
    onCommandRecognized: voiceCommandResult.onCommandRecognized
  });
}

/**
 * 运行示例
 */
function runExample() {
  console.log('开始演示批次1.4输入系统增强节点...');
  
  // 演示高级输入节点（10个）
  demonstrateAdvancedInputNodes();
  
  // 演示传感器输入节点（6个）
  demonstrateSensorInputNodes();
  
  // 演示VR/AR输入节点（8个）
  demonstrateVRARInputNodes();
  
  console.log('批次1.4输入系统增强节点演示完成！');
  console.log('总计18个节点已成功实现并可在编辑器中使用。');
}

// 运行示例
runExample();

export { runExample };
